#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试转体检测
分析为什么没有转体的滞空被判断为转体
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import math


def analyze_rotation_detection():
    """分析转体检测的准确性"""
    print("🔍 转体检测准确性分析")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    quality_events = [e for e in airtime_events if e.confidence >= 0.6]
    
    print(f"检测到 {len(quality_events)} 个高质量滞空事件")
    
    # 分析每个事件的转体判断
    for i, event in enumerate(quality_events[:10], 1):  # 只分析前10个
        print(f"\n{'='*60}")
        print(f"🎿 事件 {i} 转体分析")
        print(f"{'='*60}")
        
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            
            print(f"📊 基本信息:")
            print(f"   持续时间: {event.duration_seconds:.3f}秒")
            print(f"   数据点数: {len(airtime_segment)}")
            
            # 详细分析角速度数据
            print(f"\n📈 角速度数据分析:")
            
            gx_values = [data.gx for data in airtime_segment]
            gy_values = [data.gy for data in airtime_segment]
            gz_values = [data.gz for data in airtime_segment]
            
            print(f"   X轴角速度范围: {min(gx_values):.1f} ~ {max(gx_values):.1f} °/s")
            print(f"   Y轴角速度范围: {min(gy_values):.1f} ~ {max(gy_values):.1f} °/s")
            print(f"   Z轴角速度范围: {min(gz_values):.1f} ~ {max(gz_values):.1f} °/s")
            
            # 计算角度积分
            total_time = event.duration_seconds
            dt = total_time / (len(airtime_segment) - 1) if len(airtime_segment) > 1 else 0
            
            x_angle = 0.0
            y_angle = 0.0
            z_angle = 0.0
            
            for j in range(1, len(airtime_segment)):
                gx_avg = (airtime_segment[j].gx + airtime_segment[j-1].gx) / 2
                gy_avg = (airtime_segment[j].gy + airtime_segment[j-1].gy) / 2
                gz_avg = (airtime_segment[j].gz + airtime_segment[j-1].gz) / 2
                
                x_angle += gx_avg * dt
                y_angle += gy_avg * dt
                z_angle += gz_avg * dt
            
            print(f"\n🔄 角度积分结果:")
            print(f"   X轴总角度: {abs(x_angle):.1f}° ({abs(x_angle)/360:.2f}圈)")
            print(f"   Y轴总角度: {abs(y_angle):.1f}° ({abs(y_angle)/360:.2f}圈)")
            print(f"   Z轴总角度: {abs(z_angle):.1f}° ({abs(z_angle)/360:.2f}圈)")
            
            # 判断主要旋转轴
            max_angle = max(abs(x_angle), abs(y_angle), abs(z_angle))
            if abs(z_angle) == max_angle:
                main_axis = "Z轴 (转体)"
            elif abs(x_angle) == max_angle:
                main_axis = "X轴 (前后翻)"
            else:
                main_axis = "Y轴 (侧翻)"
            
            print(f"   主要旋转轴: {main_axis}")
            print(f"   最大角度: {max_angle:.1f}°")
            
            # 当前算法的判断
            current_judgment = abs(z_angle) > 30
            print(f"\n⚖️ 当前算法判断:")
            print(f"   Z轴角度 > 30°: {abs(z_angle):.1f}° > 30° = {current_judgment}")
            print(f"   判断结果: {'转体' if current_judgment else '非转体'}")
            
            # 建议的判断逻辑
            is_real_spin = abs(z_angle) > 90 and abs(z_angle) > max(abs(x_angle), abs(y_angle)) * 1.5
            print(f"\n💡 建议判断逻辑:")
            print(f"   Z轴角度 > 90° 且 Z轴角度 > 其他轴×1.5")
            print(f"   {abs(z_angle):.1f}° > 90° 且 {abs(z_angle):.1f}° > {max(abs(x_angle), abs(y_angle))*1.5:.1f}°")
            print(f"   建议结果: {'转体' if is_real_spin else '非转体'}")
            
            # 分析角速度的稳定性
            gz_variance = sum((gz - sum(gz_values)/len(gz_values))**2 for gz in gz_values) / len(gz_values)
            gz_std = math.sqrt(gz_variance)
            print(f"\n📊 Z轴角速度稳定性:")
            print(f"   平均值: {sum(gz_values)/len(gz_values):.1f}°/s")
            print(f"   标准差: {gz_std:.1f}°/s")
            print(f"   稳定性: {'稳定' if gz_std < 500 else '不稳定'}")


def suggest_improved_detection():
    """建议改进的检测逻辑"""
    print(f"\n💡 改进建议")
    print("=" * 80)
    
    print(f"当前问题:")
    print(f"   ❌ 只要Z轴角度>30°就判断为转体")
    print(f"   ❌ 没有考虑其他轴的角度大小")
    print(f"   ❌ 没有考虑角速度的稳定性")
    
    print(f"\n改进方案:")
    print(f"   ✅ 提高Z轴角度阈值 (30° → 90°)")
    print(f"   ✅ 要求Z轴角度明显大于其他轴")
    print(f"   ✅ 检查Z轴角速度的一致性")
    print(f"   ✅ 过滤传感器噪声和异常值")
    
    print(f"\n建议的判断条件:")
    print(f"   1. Z轴总角度 > 90° (至少1/4圈)")
    print(f"   2. Z轴角度 > max(X轴, Y轴) × 1.5 (明显的转体特征)")
    print(f"   3. Z轴角速度标准差 < 阈值 (稳定的旋转)")
    print(f"   4. 持续时间 > 0.2秒 (排除瞬时波动)")


def main():
    """主函数"""
    analyze_rotation_detection()
    suggest_improved_detection()
    
    print(f"\n🔧 下一步:")
    print(f"   根据分析结果修改 _calculate_rotation_metrics 方法")
    print(f"   使用更严格的转体判断条件")


if __name__ == "__main__":
    main()
