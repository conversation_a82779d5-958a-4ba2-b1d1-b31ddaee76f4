#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据加载
"""

import re
import os
from datetime import datetime

def debug_load():
    filename = '720.txt'
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"文件是否存在: {os.path.exists(filename)}")
    
    if not os.path.exists(filename):
        print("尝试查找其他数据文件...")
        for f in os.listdir('.'):
            if f.endswith('.txt'):
                print(f"  找到: {f}")
        return
    
    # 测试正则表达式
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+).*?lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 3:
                    break
                line = line.strip()
                print(f"\n第{i+1}行: {line}")
                
                match = re.match(pattern, line)
                if match:
                    print(f"  ✅ 匹配成功，提取到 {len(match.groups())} 个字段")
                    groups = match.groups()
                    print(f"  时间戳: {groups[0]}")
                    print(f"  毫秒: {groups[1]}")
                    print(f"  az: {groups[8]}")
                    print(f"  h: {groups[16]}")
                    print(f"  s: {groups[17]}")
                else:
                    print(f"  ❌ 匹配失败")
                    
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    debug_load()
