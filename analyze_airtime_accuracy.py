#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析滞空检测的准确性
提供优化建议
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import matplotlib.pyplot as plt
import numpy as np


def analyze_detection_accuracy():
    """分析检测准确性"""
    print("🔍 分析滞空检测准确性")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间跨度: {sensor_data[0].timestamp} ~ {sensor_data[-1].timestamp}")
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    quality_events = [e for e in airtime_events if e.confidence >= 0.6]
    
    print(f"\n📈 检测结果:")
    print(f"   总检测事件: {len(airtime_events)}")
    print(f"   高质量事件: {len(quality_events)}")
    
    # 分析检测逻辑的问题
    print(f"\n🔍 检测逻辑分析:")
    
    # 1. 分析Z轴加速度分布
    az_values = [abs(data.az) for data in sensor_data]
    gravity_deviations = [abs(abs(data.az) - detector.standard_gravity) for data in sensor_data]
    
    print(f"   Z轴加速度统计:")
    print(f"     平均值: {np.mean(az_values):.0f} mg")
    print(f"     标准差: {np.std(az_values):.0f} mg")
    print(f"     重力偏差平均: {np.mean(gravity_deviations):.0f} mg")
    print(f"     重力偏差标准差: {np.std(gravity_deviations):.0f} mg")
    
    # 2. 分析角速度分布
    angular_velocities = [data.total_angular_velocity() for data in sensor_data]
    print(f"   角速度统计:")
    print(f"     平均值: {np.mean(angular_velocities):.0f} °/s")
    print(f"     标准差: {np.std(angular_velocities):.0f} °/s")
    print(f"     最大值: {np.max(angular_velocities):.0f} °/s")
    
    # 3. 分析当前阈值的合理性
    current_gravity_threshold = detector.gravity_threshold
    current_angular_threshold = detector.angular_velocity_threshold
    
    gravity_exceed_count = sum(1 for gd in gravity_deviations if gd > current_gravity_threshold)
    angular_exceed_count = sum(1 for av in angular_velocities if av > current_angular_threshold)
    
    print(f"\n⚖️ 阈值分析:")
    print(f"   当前重力阈值: {current_gravity_threshold} mg")
    print(f"   超过重力阈值的点: {gravity_exceed_count} / {len(sensor_data)} ({gravity_exceed_count/len(sensor_data)*100:.1f}%)")
    print(f"   当前角速度阈值: {current_angular_threshold} °/s")
    print(f"   超过角速度阈值的点: {angular_exceed_count} / {len(sensor_data)} ({angular_exceed_count/len(sensor_data)*100:.1f}%)")


def analyze_event_boundaries():
    """分析事件边界的准确性"""
    print(f"\n🎯 事件边界分析")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    airtime_events = detector.detect_airtime_events(sensor_data)
    quality_events = [e for e in airtime_events if e.confidence >= 0.6]
    
    # 分析前3个事件的边界
    for i, event in enumerate(quality_events[:3], 1):
        print(f"\n{'='*50}")
        print(f"🎿 事件 {i} 边界分析")
        print(f"{'='*50}")
        
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            start_idx = event.start_data_index
            end_idx = event.end_data_index
            
            # 扩展分析范围（前后各5个点）
            analysis_start = max(0, start_idx - 5)
            analysis_end = min(len(sensor_data), end_idx + 6)
            
            print(f"📊 事件信息:")
            print(f"   检测范围: 索引 {start_idx} ~ {end_idx}")
            print(f"   持续时间: {event.duration_seconds:.3f}秒")
            print(f"   置信度: {event.confidence:.3f}")
            
            print(f"\n📈 边界附近数据分析:")
            print(f"{'索引':<4} {'时间':<12} {'|az|':<8} {'重力偏差':<8} {'角速度':<8} {'状态':<8}")
            print("-" * 60)
            
            for j in range(analysis_start, analysis_end):
                data = sensor_data[j]
                az_abs = abs(data.az)
                gravity_dev = abs(az_abs - detector.standard_gravity)
                angular_vel = data.total_angular_velocity()
                
                # 判断当前点的状态
                if j < start_idx:
                    status = "起跳前"
                elif j > end_idx:
                    status = "落地后"
                else:
                    status = "滞空中"
                
                time_str = data.timestamp.strftime('%H:%M:%S')
                print(f"{j:<4} {time_str:<12} {az_abs:<8.0f} {gravity_dev:<8.0f} {angular_vel:<8.0f} {status:<8}")


def suggest_optimizations():
    """建议优化方案"""
    print(f"\n💡 滞空检测优化建议")
    print("=" * 80)
    
    print(f"🔧 当前检测逻辑的问题:")
    print(f"   1. 单一阈值判断：可能过于简单")
    print(f"   2. 边界不精确：起跳和落地时刻判断不准")
    print(f"   3. 噪声干扰：传感器噪声影响判断")
    print(f"   4. 缺乏连续性：没有考虑时间序列的连续性")
    
    print(f"\n✅ 优化方案:")
    
    print(f"\n1. 📊 多特征融合检测:")
    print(f"   - 结合Z轴加速度、角速度、高度变化")
    print(f"   - 使用加权评分而非硬阈值")
    print(f"   - 考虑特征的时间序列变化")
    
    print(f"\n2. 🔄 滑动窗口平滑:")
    print(f"   - 使用滑动窗口平滑数据")
    print(f"   - 减少瞬时噪声的影响")
    print(f"   - 提高边界检测精度")
    
    print(f"\n3. 📈 梯度变化检测:")
    print(f"   - 检测加速度的突变点")
    print(f"   - 识别起跳和落地的瞬间")
    print(f"   - 基于物理规律优化边界")
    
    print(f"\n4. 🎯 自适应阈值:")
    print(f"   - 根据数据分布动态调整阈值")
    print(f"   - 考虑个人运动特征")
    print(f"   - 使用统计方法确定阈值")
    
    print(f"\n5. 🔍 后处理优化:")
    print(f"   - 合并过近的事件")
    print(f"   - 过滤过短的事件")
    print(f"   - 基于物理约束验证事件")
    
    print(f"\n6. 📊 机器学习方法:")
    print(f"   - 使用标注数据训练模型")
    print(f"   - 特征工程优化")
    print(f"   - 端到端的序列检测")


def create_improved_detector():
    """创建改进的检测器示例"""
    print(f"\n🚀 改进检测器示例")
    print("=" * 80)
    
    print(f"改进的检测逻辑框架:")
    print(f"""
class ImprovedAirtimeDetector:
    def __init__(self):
        # 多特征阈值
        self.gravity_threshold_low = 400    # 低阈值
        self.gravity_threshold_high = 800   # 高阈值
        self.angular_threshold_low = 500    # 低阈值
        self.angular_threshold_high = 1000  # 高阈值
        
        # 滑动窗口参数
        self.window_size = 5
        self.min_duration = 0.15  # 最小持续时间
        
    def detect_with_multi_features(self, sensor_data):
        # 1. 数据预处理和平滑
        smoothed_data = self.smooth_data(sensor_data)
        
        # 2. 多特征评分
        scores = self.calculate_airtime_scores(smoothed_data)
        
        # 3. 阈值检测和连续性分析
        candidates = self.find_airtime_candidates(scores)
        
        # 4. 边界优化
        events = self.optimize_boundaries(candidates, smoothed_data)
        
        # 5. 后处理
        final_events = self.post_process_events(events)
        
        return final_events
    """)


def main():
    """主函数"""
    analyze_detection_accuracy()
    analyze_event_boundaries()
    suggest_optimizations()
    create_improved_detector()
    
    print(f"\n🎯 下一步行动:")
    print(f"   1. 实现多特征融合检测")
    print(f"   2. 添加滑动窗口平滑")
    print(f"   3. 优化边界检测算法")
    print(f"   4. 增加后处理步骤")


if __name__ == "__main__":
    main()
