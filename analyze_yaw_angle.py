#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析偏航角数据（z字段）
验证使用偏航角计算转体的可行性
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import statistics


def analyze_yaw_data():
    """分析偏航角数据"""
    print("🔍 分析偏航角数据（z字段）")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    
    # 提取偏航角数据
    z_values = [data.z for data in sensor_data]
    
    print(f"\n📈 偏航角数据统计:")
    print(f"   偏航角 (z):")
    print(f"     范围: {min(z_values):.1f} ~ {max(z_values):.1f}")
    print(f"     平均: {statistics.mean(z_values):.1f}")
    print(f"     标准差: {statistics.stdev(z_values):.1f}")
    
    # 显示前20个数据点
    print(f"\n📋 前20个数据点的偏航角:")
    print(f"{'序号':<4} {'时间':<12} {'偏航角(z)':<10} {'角度变化':<10}")
    print("-" * 45)
    
    for i in range(min(20, len(sensor_data))):
        data = sensor_data[i]
        angle_change = 0 if i == 0 else data.z - sensor_data[i-1].z
        time_str = data.timestamp.strftime('%H:%M:%S')
        print(f"{i+1:<4} {time_str:<12} {data.z:<10.1f} {angle_change:<10.1f}")
    
    # 分析角度变化
    angle_changes = []
    for i in range(1, len(sensor_data)):
        change = sensor_data[i].z - sensor_data[i-1].z
        
        # 处理角度跨越360°的情况
        if change > 180:
            change -= 360
        elif change < -180:
            change += 360
            
        angle_changes.append(change)
    
    print(f"\n🔄 角度变化分析:")
    print(f"   角度变化范围: {min(angle_changes):.1f}° ~ {max(angle_changes):.1f}°")
    print(f"   平均角度变化: {statistics.mean(angle_changes):.1f}°")
    print(f"   角度变化标准差: {statistics.stdev(angle_changes):.1f}°")
    
    # 检测大的角度变化（可能的转体）
    large_changes = [change for change in angle_changes if abs(change) > 10]
    print(f"   大角度变化(>10°): {len(large_changes)} 次")
    if large_changes:
        print(f"   大角度变化范围: {min(large_changes):.1f}° ~ {max(large_changes):.1f}°")


def test_yaw_based_rotation():
    """测试基于偏航角的转体计算"""
    print(f"\n🧪 测试基于偏航角的转体计算")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    quality_events = [e for e in airtime_events if e.confidence >= 0.6]
    
    print(f"检测到 {len(quality_events)} 个高质量滞空事件")
    
    # 分析前3个事件的偏航角变化
    for i, event in enumerate(quality_events[:3], 1):
        print(f"\n{'='*50}")
        print(f"🎿 事件 {i} 偏航角分析")
        print(f"{'='*50}")
        
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            
            print(f"📊 基本信息:")
            print(f"   持续时间: {event.duration_seconds:.3f}秒")
            print(f"   数据点数: {len(airtime_segment)}")
            
            # 提取偏航角数据
            yaw_angles = [data.z for data in airtime_segment]
            start_yaw = yaw_angles[0]
            end_yaw = yaw_angles[-1]
            
            print(f"\n🧭 偏航角数据:")
            print(f"   开始偏航角: {start_yaw:.1f}°")
            print(f"   结束偏航角: {end_yaw:.1f}°")
            
            # 计算总角度变化（处理跨越360°的情况）
            total_rotation = end_yaw - start_yaw
            if total_rotation > 180:
                total_rotation -= 360
            elif total_rotation < -180:
                total_rotation += 360
            
            # 计算累积旋转角度（考虑多圈旋转）
            cumulative_rotation = 0
            for j in range(1, len(yaw_angles)):
                angle_change = yaw_angles[j] - yaw_angles[j-1]
                
                # 处理角度跨越
                if angle_change > 180:
                    angle_change -= 360
                elif angle_change < -180:
                    angle_change += 360
                
                cumulative_rotation += angle_change
            
            print(f"   直接角度差: {total_rotation:.1f}°")
            print(f"   累积旋转角度: {cumulative_rotation:.1f}°")
            print(f"   累积旋转圈数: {abs(cumulative_rotation)/360:.2f}圈")
            
            # 计算转体速度
            if event.duration_seconds > 0:
                rotation_speed_deg_per_sec = abs(cumulative_rotation) / event.duration_seconds
                rotation_speed_rev_per_sec = rotation_speed_deg_per_sec / 360
                
                print(f"   转体速度: {rotation_speed_deg_per_sec:.1f}°/s")
                print(f"   转体速度: {rotation_speed_rev_per_sec:.2f}圈/s")
            
            # 与当前积分方法对比
            current_rotation_metrics = detector._calculate_rotation_metrics(airtime_segment)
            current_angle = current_rotation_metrics.get('rotation_angle', 0)
            current_speed = current_rotation_metrics.get('avg_rotation_speed', 0)
            
            print(f"\n🔄 方法对比:")
            print(f"   积分方法角度: {current_angle:.1f}°")
            print(f"   偏航角方法角度: {abs(cumulative_rotation):.1f}°")
            print(f"   积分方法速度: {current_speed:.2f}圈/s")
            print(f"   偏航角方法速度: {rotation_speed_rev_per_sec:.2f}圈/s")
            
            # 判断哪种方法更合理
            angle_diff = abs(current_angle - abs(cumulative_rotation))
            speed_diff = abs(current_speed - rotation_speed_rev_per_sec)
            
            print(f"   角度差异: {angle_diff:.1f}°")
            print(f"   速度差异: {speed_diff:.2f}圈/s")


def main():
    """主函数"""
    analyze_yaw_data()
    test_yaw_based_rotation()
    
    print(f"\n💡 结论:")
    print(f"   偏航角方法的优势:")
    print(f"   ✅ 直接测量，无积分漂移")
    print(f"   ✅ 物理意义明确")
    print(f"   ✅ 计算简单可靠")
    print(f"   ✅ 避免传感器噪声累积")


if __name__ == "__main__":
    main()
