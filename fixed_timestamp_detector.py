#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全修正时间戳的滞空检测器
确保时间戳显示与持续时间计算完全一致
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import AirtimeEvent, SensorData
from typing import List
from dataclasses import dataclass


@dataclass
class FixedAirtimeEvent(AirtimeEvent):
    """修正的滞空事件，包含数据点索引"""
    start_data_index: int = 0      # 开始数据点索引
    end_data_index: int = 0        # 结束数据点索引


class FixedTimestampDetector(OptimizedAirtimeDetector):
    """完全修正时间戳的检测器"""
    
    def detect_airtime_events_fixed(self, sensor_data: List[SensorData]) -> List[FixedAirtimeEvent]:
        """检测滞空事件并记录正确的数据点索引"""
        if len(sensor_data) < 10:
            return []
        
        print("🎿 使用完全修正的检测算法...")
        
        # 生成检测模式
        airborne_conditions = []
        for i, data in enumerate(sensor_data):
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()
            
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)
            
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and 
                 angular_velocity > self.angular_velocity_threshold * 0.5) or
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)
            )
            
            airborne_conditions.append(is_airborne)
        
        # 应用一致性过滤
        filtered_conditions = self._apply_consistency_filter(airborne_conditions)
        
        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start_idx = 0
        
        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start_idx = i
                
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                airtime_end_idx = i - 1  # 结束索引是最后一个满足条件的点
                
                # 确保开始和结束索引不同
                if airtime_end_idx > airtime_start_idx:
                    start_data = sensor_data[airtime_start_idx]
                    end_data = sensor_data[airtime_end_idx]
                    
                    duration_ms = self._calculate_duration_ms(start_data, end_data)
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:airtime_end_idx+1]
                        
                        # 计算滞空特征
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        confidence = self._calculate_confidence(airtime_segment, duration_ms)
                        
                        # 计算旋转相关指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
                        
                        event = FixedAirtimeEvent(
                            start_time=start_data.timestamp,
                            end_time=end_data.timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected'],
                            start_data_index=airtime_start_idx,
                            end_data_index=airtime_end_idx
                        )
                        
                        airtime_events.append(event)
                
                airtime_start_idx = 0
        
        print(f"🎿 修正检测完成，发现 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def get_precise_timestamps(self, sensor_data: List[SensorData], event: FixedAirtimeEvent) -> tuple:
        """获取精确的时间戳"""
        start_data = sensor_data[event.start_data_index]
        end_data = sensor_data[event.end_data_index]
        
        start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
        end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
        
        return start_timestamp, end_timestamp
    
    def verify_all_timestamps(self, sensor_data: List[SensorData], events: List[FixedAirtimeEvent]):
        """验证所有事件的时间戳一致性"""
        print(f"\n🔍 完整时间戳验证:")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'存储(ms)':<10} {'计算(ms)':<10} {'差异':<8} {'一致性':<8}")
        print("-" * 100)
        
        for i, event in enumerate(events, 1):
            start_timestamp, end_timestamp = self.get_precise_timestamps(sensor_data, event)
            
            # 从时间戳计算持续时间
            try:
                from datetime import datetime
                start_dt = datetime.strptime(start_timestamp, '%Y-%m-%d %H:%M:%S.%f')
                end_dt = datetime.strptime(end_timestamp, '%Y-%m-%d %H:%M:%S.%f')
                calculated_duration = (end_dt - start_dt).total_seconds() * 1000
                
                difference = abs(event.duration_ms - calculated_duration)
                is_consistent = difference < 1  # 允许1ms的误差
                
                print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} {event.duration_ms:<10} "
                      f"{calculated_duration:<10.0f} {difference:<8.0f} {'✅' if is_consistent else '❌':<8}")
                
            except Exception as e:
                print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} {event.duration_ms:<10} "
                      f"{'错误':<10} {'N/A':<8} {'❌':<8}")


def main():
    """主函数"""
    print("🔧 完全修正时间戳的滞空检测器")
    print("=" * 60)
    
    detector = FixedTimestampDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 使用修正的检测方法
    fixed_events = detector.detect_airtime_events_fixed(sensor_data)
    
    if not fixed_events:
        print("❌ 未检测到滞空事件")
        return
    
    # 分析结果
    stats = detector.analyze_airtime_statistics(fixed_events)
    
    print(f"\n📊 检测结果:")
    print(f"总检测事件: {stats['total_events']} 次")
    print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
    print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
    
    # 获取高质量事件
    quality_events = [e for e in fixed_events if e.confidence >= 0.6]
    
    print(f"\n🏆 高质量滞空事件 (置信度≥0.6): {len(quality_events)} 次")
    
    if quality_events:
        print(f"\n详细列表:")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'旋转':<6} {'最大转速(°/s)':<12}")
        print("-" * 115)
        
        for i, event in enumerate(quality_events, 1):
            start_timestamp, end_timestamp = detector.get_precise_timestamps(sensor_data, event)
            rotation_indicator = "🌀" if event.rotation_detected else "—"
            print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {rotation_indicator:<6} {event.max_rotation_speed:<12.0f}")
    
    # 验证时间戳一致性
    detector.verify_all_timestamps(sensor_data, quality_events)
    
    print(f"\n💡 修正说明:")
    print(f"   ✅ 使用数据点索引确保精确的开始和结束时间")
    print(f"   ✅ 时间戳直接从对应的传感器数据点获取")
    print(f"   ✅ 持续时间计算与时间戳显示完全一致")
    print(f"   ✅ 包含真实的毫秒值，精确到毫秒级")


if __name__ == "__main__":
    main()
