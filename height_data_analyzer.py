#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高度数据分析工具
分析传感器数据中的高度字段h，验证滞空高度计算
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator
from optimized_airtime_detector import OptimizedAirtimeDetector
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class HeightDataAnalyzer:
    """高度数据分析器"""
    
    def __init__(self):
        pass
    
    def analyze_height_data(self, filename: str):
        """分析高度数据的基本特征"""
        print("🏔️ 高度数据分析")
        print("=" * 60)
        
        calculator = SkiingAirtimeCalculator()
        sensor_data = calculator.load_sensor_data(filename)
        
        if not sensor_data:
            print("无法加载数据")
            return
        
        # 提取高度数据 (h字段存储在humidity中)
        height_values = [data.humidity for data in sensor_data]
        timestamps = [data.timestamp for data in sensor_data]
        
        print(f"📊 高度数据基本统计:")
        print(f"   数据点数量: {len(height_values)}")
        print(f"   高度范围: {min(height_values)} - {max(height_values)}")
        print(f"   高度变化: {max(height_values) - min(height_values)}")
        print(f"   平均高度: {sum(height_values)/len(height_values):.1f}")
        print(f"   高度标准差: {np.std(height_values):.1f}")
        
        # 分析高度变化模式
        height_changes = []
        for i in range(1, len(height_values)):
            change = abs(height_values[i] - height_values[i-1])
            height_changes.append(change)
        
        print(f"\n📈 高度变化分析:")
        print(f"   最大单次变化: {max(height_changes)}")
        print(f"   平均单次变化: {sum(height_changes)/len(height_changes):.2f}")
        print(f"   变化标准差: {np.std(height_changes):.2f}")
        
        # 分析可能的单位
        total_range = max(height_values) - min(height_values)
        max_change = max(height_changes)
        
        print(f"\n🔍 单位分析:")
        if total_range < 10:
            print(f"   总变化范围{total_range}很小，可能单位是米")
            unit_guess = "米"
        elif total_range < 1000:
            print(f"   总变化范围{total_range}中等，可能单位是厘米")
            unit_guess = "厘米"
        else:
            print(f"   总变化范围{total_range}较大，可能单位是毫米")
            unit_guess = "毫米"
        
        return {
            'height_values': height_values,
            'timestamps': timestamps,
            'total_range': total_range,
            'max_change': max_change,
            'unit_guess': unit_guess
        }
    
    def analyze_airtime_heights(self, filename: str):
        """分析滞空事件的高度变化"""
        print(f"\n🎿 滞空事件高度分析")
        print("=" * 60)
        
        # 使用优化检测器
        detector = OptimizedAirtimeDetector()
        sensor_data = detector.load_sensor_data(filename)
        
        if not sensor_data:
            return
        
        # 检测滞空事件
        airtime_events = detector.detect_airtime_events(sensor_data)
        
        print(f"分析 {len(airtime_events)} 个滞空事件的高度变化:")
        print(f"{'序号':<4} {'时间段':<17} {'持续(s)':<8} {'原始高度差':<10} {'转换后高度(m)':<12} {'置信度':<8}")
        print("-" * 75)
        
        for i, event in enumerate(airtime_events[:15], 1):  # 只显示前15个
            # 找到对应的传感器数据段
            segment = []
            for data in sensor_data:
                if event.start_time <= data.timestamp <= event.end_time:
                    segment.append(data)
            
            if segment:
                # 计算原始高度差
                height_values = [data.humidity for data in segment]
                raw_height_diff = max(height_values) - min(height_values)
                
                # 使用改进的高度计算
                converted_height = self._calculate_improved_height(segment)
                
                print(f"{i:<4} {event.start_time.strftime('%H:%M:%S')}-{event.end_time.strftime('%H:%M:%S'):<17} "
                      f"{event.duration_seconds:<8.2f} {raw_height_diff:<10.1f} "
                      f"{converted_height:<12.3f} {event.confidence:<8.2f}")
        
        # 统计分析
        all_raw_diffs = []
        all_converted_heights = []
        
        for event in airtime_events:
            segment = []
            for data in sensor_data:
                if event.start_time <= data.timestamp <= event.end_time:
                    segment.append(data)
            
            if segment:
                height_values = [data.humidity for data in segment]
                raw_diff = max(height_values) - min(height_values)
                converted = self._calculate_improved_height(segment)
                
                all_raw_diffs.append(raw_diff)
                all_converted_heights.append(converted)
        
        if all_raw_diffs:
            print(f"\n📊 滞空高度统计:")
            print(f"   原始高度差范围: {min(all_raw_diffs):.1f} - {max(all_raw_diffs):.1f}")
            print(f"   转换后高度范围: {min(all_converted_heights):.3f} - {max(all_converted_heights):.3f} 米")
            print(f"   平均滞空高度: {sum(all_converted_heights)/len(all_converted_heights):.3f} 米")
    
    def _calculate_improved_height(self, segment):
        """改进的高度计算方法"""
        if len(segment) < 2:
            return 0.0
        
        height_values = [data.humidity for data in segment]
        height_change = (max(height_values) - min(height_values))/10
        
        # 智能单位转换
        if height_change > 1000:
            # 毫米转米
            return min(height_change / 1000.0, 3.0)
        elif height_change > 100:
            # 厘米转米
            return min(height_change / 100.0, 3.0)
        elif height_change > 10:
            # 分米转米
            return min(height_change / 10.0, 3.0)
        else:
            # 可能已经是米或很小的变化
            return min(height_change, 3.0)
    
    def compare_height_calculation_methods(self, filename: str):
        """对比不同高度计算方法"""
        print(f"\n🔄 高度计算方法对比")
        print("=" * 60)
        
        detector = OptimizedAirtimeDetector()
        sensor_data = detector.load_sensor_data(filename)
        
        if not sensor_data:
            return
        
        airtime_events = detector.detect_airtime_events(sensor_data)
        
        print(f"{'序号':<4} {'Z坐标差值':<10} {'高度h差值':<10} {'改进高度(m)':<12} {'持续时间(s)':<10}")
        print("-" * 60)
        
        for i, event in enumerate(airtime_events[:10], 1):
            segment = []
            for data in sensor_data:
                if event.start_time <= data.timestamp <= event.end_time:
                    segment.append(data)
            
            if segment:
                # 方法1: Z坐标差值 (原方法)
                z_values = [data.z for data in segment]
                z_diff = max(z_values) - min(z_values)
                
                # 方法2: 高度h差值 (原始)
                h_values = [data.humidity for data in segment]
                h_diff = max(h_values) - min(h_values)
                
                # 方法3: 改进的高度计算
                improved_height = self._calculate_improved_height(segment)
                
                print(f"{i:<4} {z_diff:<10.1f} {h_diff:<10.1f} {improved_height:<12.3f} {event.duration_seconds:<10.2f}")
        
        print(f"\n💡 结论:")
        print(f"1. Z坐标差值通常较大，不太符合实际跳跃高度")
        print(f"2. 高度h差值需要单位转换才有意义")
        print(f"3. 改进方法结合了智能单位转换和合理性限制")
        print(f"4. 滑雪跳跃高度通常在0.1-2米范围内")


def main():
    """主函数"""
    analyzer = HeightDataAnalyzer()
    
    # 分析高度数据基本特征
    height_info = analyzer.analyze_height_data('20250330153942712068-济洲平花.txt')
    
    # 分析滞空事件的高度变化
    analyzer.analyze_airtime_heights('20250330153942712068-济洲平花.txt')
    
    # 对比不同计算方法
    analyzer.compare_height_calculation_methods('20250330153942712068-济洲平花.txt')
    
    print(f"\n✅ 高度数据分析完成！")
    print(f"💡 建议:")
    print(f"1. 使用高度字段h比Z坐标更准确")
    print(f"2. 需要根据数据特征判断正确的单位")
    print(f"3. 设置合理的高度范围限制")


if __name__ == "__main__":
    main()
