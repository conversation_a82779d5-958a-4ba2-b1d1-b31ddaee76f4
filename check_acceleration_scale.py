#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查加速度数据的缩放因子
分析实际的加速度数值范围
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import statistics


def analyze_acceleration_data():
    """分析加速度数据的实际数值"""
    print("🔍 分析加速度数据缩放因子")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    
    # 提取加速度数据
    ax_values = [data.ax for data in sensor_data]
    ay_values = [data.ay for data in sensor_data]
    az_values = [data.az for data in sensor_data]
    
    print(f"\n📈 加速度数据统计:")
    print(f"   X轴加速度 (ax):")
    print(f"     范围: {min(ax_values):.0f} ~ {max(ax_values):.0f}")
    print(f"     平均: {statistics.mean(ax_values):.0f}")
    print(f"     标准差: {statistics.stdev(ax_values):.0f}")
    
    print(f"   Y轴加速度 (ay):")
    print(f"     范围: {min(ay_values):.0f} ~ {max(ay_values):.0f}")
    print(f"     平均: {statistics.mean(ay_values):.0f}")
    print(f"     标准差: {statistics.stdev(ay_values):.0f}")
    
    print(f"   Z轴加速度 (az):")
    print(f"     范围: {min(az_values):.0f} ~ {max(az_values):.0f}")
    print(f"     平均: {statistics.mean(az_values):.0f}")
    print(f"     标准差: {statistics.stdev(az_values):.0f}")
    
    # 分析Z轴加速度（重力方向）
    az_abs_values = [abs(az) for az in az_values]
    print(f"\n🌍 Z轴加速度分析（重力方向）:")
    print(f"   |Z轴|范围: {min(az_abs_values):.0f} ~ {max(az_abs_values):.0f}")
    print(f"   |Z轴|平均: {statistics.mean(az_abs_values):.0f}")
    print(f"   |Z轴|中位数: {statistics.median(az_abs_values):.0f}")
    
    # 理论重力加速度对比
    print(f"\n⚖️ 重力加速度对比:")
    print(f"   理论重力 (1g): 9.8 m/s² = 980 mg")
    print(f"   如果数据×100: 980 × 100 = 98000")
    print(f"   实际Z轴平均: {statistics.mean(az_abs_values):.0f}")
    
    # 判断缩放因子
    theoretical_gravity_mg = 980
    theoretical_gravity_x100 = 98000
    actual_avg = statistics.mean(az_abs_values)
    
    ratio_to_normal = actual_avg / theoretical_gravity_mg
    ratio_to_x100 = actual_avg / theoretical_gravity_x100
    
    print(f"\n🔢 缩放因子分析:")
    print(f"   实际值 / 理论值(980): {ratio_to_normal:.2f}")
    print(f"   实际值 / 理论值×100(98000): {ratio_to_x100:.2f}")
    
    if 0.8 <= ratio_to_x100 <= 1.2:
        print(f"   ✅ 数据确实是×100的缩放")
        suggested_gravity = 98000
    elif 0.8 <= ratio_to_normal <= 1.2:
        print(f"   ✅ 数据是正常的mg单位")
        suggested_gravity = 980
    else:
        print(f"   ⚠️  数据缩放因子不明确")
        suggested_gravity = int(actual_avg)
    
    print(f"   建议的standard_gravity值: {suggested_gravity}")
    
    # 检查当前算法使用的值
    current_gravity = detector.standard_gravity
    print(f"\n🔧 当前算法设置:")
    print(f"   当前standard_gravity: {current_gravity}")
    print(f"   建议standard_gravity: {suggested_gravity}")
    print(f"   需要修改: {'是' if current_gravity != suggested_gravity else '否'}")
    
    # 分析对检测的影响
    print(f"\n📊 对检测算法的影响:")
    current_threshold = detector.gravity_threshold
    print(f"   当前gravity_threshold: {current_threshold}")
    
    if current_gravity != suggested_gravity:
        scale_factor = suggested_gravity / current_gravity
        suggested_threshold = int(current_threshold * scale_factor)
        print(f"   建议gravity_threshold: {suggested_threshold}")
        print(f"   缩放因子: {scale_factor:.1f}")


def show_sample_data():
    """显示样本数据"""
    print(f"\n📋 样本数据 (前10个数据点):")
    print(f"{'序号':<4} {'ax':<8} {'ay':<8} {'az':<8} {'总加速度':<10}")
    print("-" * 45)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    for i, data in enumerate(sensor_data[:10], 1):
        total_acc = data.total_acceleration()
        print(f"{i:<4} {data.ax:<8.0f} {data.ay:<8.0f} {data.az:<8.0f} {total_acc:<10.0f}")


def main():
    """主函数"""
    analyze_acceleration_data()
    show_sample_data()
    
    print(f"\n💡 结论:")
    print(f"   如果数据确实是×100缩放，需要修改:")
    print(f"   1. standard_gravity: 980 → 98000")
    print(f"   2. gravity_threshold: 相应调整")
    print(f"   3. 其他相关阈值也需要调整")


if __name__ == "__main__":
    main()
