#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple plotting script for az, gz, h sensor data curves
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import re
from datetime import datetime
from typing import List, <PERSON><PERSON>

# Use English labels to avoid font issues
plt.rcParams['font.family'] = 'Arial'


def parse_sensor_data(filename: str) -> Tuple[List[datetime], List[float], List[float], List[float]]:
    """
    Parse sensor data file and extract timestamps, az, gz, h data
    """
    timestamps = []
    az_values = []
    gz_values = []
    h_values = []
    
    # Data parsing regex pattern
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                match = re.match(pattern, line)
                if match:
                    groups = match.groups()
                    
                    # Parse timestamp
                    timestamp = datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S')
                    timestamps.append(timestamp)
                    
                    # Parse sensor data
                    az = float(groups[8])  # Z-axis acceleration
                    gz = float(groups[11])  # Z-axis angular velocity
                    h = float(groups[16])   # Altitude
                    
                    az_values.append(az)
                    gz_values.append(gz)
                    h_values.append(h)
                else:
                    print(f"Warning: Line {line_num} format mismatch")
    
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return [], [], [], []
    except Exception as e:
        print(f"Error reading file: {e}")
        return [], [], [], []
    
    print(f"Successfully parsed {len(timestamps)} data records")
    return timestamps, az_values, gz_values, h_values


def plot_sensor_curves(timestamps: List[datetime], az_values: List[float], 
                      gz_values: List[float], h_values: List[float], 
                      save_path: str = None):
    """
    Plot az, gz, h curves
    """
    if not timestamps:
        print("No data to plot")
        return
    
    # Convert az values to real acceleration (divide by 100)
    az_real = [az / 100.0 for az in az_values]

    # Convert h values to real altitude (divide by 10)
    h_real = [h / 10.0 for h in h_values]
    
    # Create subplots
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle('Sensor Data Curves (az, gz, h)', fontsize=16, fontweight='bold')
    
    # 1. Z-axis acceleration plot
    axes[0].plot(timestamps, az_real, 'b-', linewidth=1, alpha=0.8, label='Z-axis acceleration')
    axes[0].axhline(y=10, color='r', linestyle='--', alpha=0.5, label='Standard gravity (+10 m/s^2)')
    axes[0].axhline(y=-10, color='r', linestyle='--', alpha=0.5, label='Standard gravity (-10 m/s^2)')
    axes[0].axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    axes[0].set_ylabel('Z-axis acceleration (m/s^2)')
    axes[0].set_title('Z-axis acceleration change (az)')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    
    # Add statistics
    az_mean = np.mean(az_real)
    az_std = np.std(az_real)
    axes[0].text(0.02, 0.98, f'Mean: {az_mean:.2f} m/s^2\nStd: {az_std:.2f} m/s^2', 
                transform=axes[0].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. Z-axis angular velocity plot
    axes[1].plot(timestamps, gz_values, 'g-', linewidth=1, alpha=0.8, label='Z-axis angular velocity')
    axes[1].axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    axes[1].set_ylabel('Z-axis angular velocity (deg/s)')
    axes[1].set_title('Z-axis angular velocity change (gz) - Spin rotation')
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()
    
    # Add statistics
    gz_mean = np.mean(gz_values)
    gz_std = np.std(gz_values)
    gz_max = max(abs(min(gz_values)), abs(max(gz_values)))
    axes[1].text(0.02, 0.98, f'Mean: {gz_mean:.1f} deg/s\nStd: {gz_std:.1f} deg/s\nMax: {gz_max:.1f} deg/s', 
                transform=axes[1].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # 3. Altitude plot
    axes[2].plot(timestamps, h_real, 'm-', linewidth=1, alpha=0.8, label='Altitude')
    axes[2].set_ylabel('Altitude (meters)')
    axes[2].set_xlabel('Time')
    axes[2].set_title('Altitude change (h)')
    axes[2].grid(True, alpha=0.3)
    axes[2].legend()

    # Add statistics
    h_mean = np.mean(h_real)
    h_range = max(h_real) - min(h_real)
    axes[2].text(0.02, 0.98, f'Mean: {h_mean:.1f}m\nRange: {h_range:.1f}m\nMax: {max(h_real):.1f}m\nMin: {min(h_real):.1f}m',
                transform=axes[2].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='plum', alpha=0.8))
    
    # Format time axis
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        ax.xaxis.set_major_locator(mdates.SecondLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # Save figure
    if save_path is None:
        save_path = 'sensor_az_gz_h_curves.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"Chart saved to: {save_path}")


def print_data_summary(timestamps: List[datetime], az_values: List[float], 
                      gz_values: List[float], h_values: List[float]):
    """Print data summary"""
    if not timestamps:
        return
    
    print("\n" + "="*60)
    print("Data Summary")
    print("="*60)
    
    # Basic info
    print(f"Data points: {len(timestamps)}")
    print(f"Time range: {timestamps[0]} to {timestamps[-1]}")
    total_duration = (timestamps[-1] - timestamps[0]).total_seconds()
    print(f"Duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
    print(f"Sampling rate: {len(timestamps)/total_duration:.1f} Hz")
    
    # Z-axis acceleration statistics (convert to real values)
    az_real = [az / 100.0 for az in az_values]
    print(f"\nZ-axis acceleration (az):")
    print(f"  Range: {min(az_real):.2f} to {max(az_real):.2f} m/s^2")
    print(f"  Mean: {np.mean(az_real):.2f} m/s^2")
    print(f"  Std: {np.std(az_real):.2f} m/s^2")
    
    # Z-axis angular velocity statistics
    print(f"\nZ-axis angular velocity (gz):")
    print(f"  Range: {min(gz_values):.1f} to {max(gz_values):.1f} deg/s")
    print(f"  Mean: {np.mean(gz_values):.1f} deg/s")
    print(f"  Std: {np.std(gz_values):.1f} deg/s")
    print(f"  Max rotation speed: {max(abs(min(gz_values)), abs(max(gz_values))):.1f} deg/s")
    
    # Altitude statistics (convert to real values)
    h_real = [h / 10.0 for h in h_values]
    print(f"\nAltitude (h):")
    print(f"  Range: {min(h_real):.1f} to {max(h_real):.1f} meters")
    print(f"  Mean: {np.mean(h_real):.1f} meters")
    print(f"  Change: {max(h_real) - min(h_real):.1f} meters")


def main():
    """Main function"""
    filename = '0.txt'
    
    print(f"Reading data file: {filename}")
    timestamps, az_values, gz_values, h_values = parse_sensor_data(filename)
    
    if not timestamps:
        print("No data successfully read")
        return
    
    # Print data summary
    print_data_summary(timestamps, az_values, gz_values, h_values)
    
    # Generate charts
    print("\nGenerating charts...")
    plot_sensor_curves(timestamps, az_values, gz_values, h_values)
    
    print("\nChart generation complete!")


if __name__ == "__main__":
    main()
