#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳输出工具
提供多种时间戳格式的滞空事件输出
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import csv
import json
from datetime import datetime


class TimestampOutputTool:
    """时间戳输出工具"""
    
    def __init__(self):
        self.detector = OptimizedAirtimeDetector()
    
    def output_detailed_timestamps(self, filename: str, min_confidence: float = 0.5):
        """输出详细时间戳信息"""
        print("⏰ 滞空事件详细时间戳输出")
        print("=" * 80)
        
        # 加载数据并检测
        sensor_data = self.detector.load_sensor_data(filename)
        if not sensor_data:
            print("❌ 无法加载数据")
            return
        
        airtime_events = self.detector.detect_airtime_events(sensor_data)
        valid_events = [e for e in airtime_events if e.confidence >= min_confidence]
        
        if not valid_events:
            print(f"❌ 未检测到置信度≥{min_confidence}的滞空事件")
            return
        
        print(f"检测到 {len(valid_events)} 个有效滞空事件")
        print()
        
        # 格式1: 标准时间戳格式
        print("📅 格式1: 标准时间戳 (YYYY-MM-DD HH:MM:SS.mmm)")
        print("-" * 80)
        for i, event in enumerate(valid_events, 1):
            start_ts = event.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            end_ts = event.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            print(f"{i:2d}. {start_ts} → {end_ts} "
                  f"(持续{event.duration_seconds:.3f}s, 高度{event.max_height_change:.3f}m, 置信度{event.confidence:.3f})")
        
        # 格式2: Unix时间戳
        print(f"\n🕐 格式2: Unix时间戳 (秒)")
        print("-" * 80)
        for i, event in enumerate(valid_events, 1):
            start_unix = event.start_time.timestamp()
            end_unix = event.end_time.timestamp()
            print(f"{i:2d}. {start_unix:.3f} → {end_unix:.3f} "
                  f"(持续{event.duration_seconds:.3f}s, 高度{event.max_height_change:.3f}m)")
        
        # 格式3: 毫秒时间戳
        print(f"\n⏱️ 格式3: 毫秒时间戳")
        print("-" * 80)
        for i, event in enumerate(valid_events, 1):
            start_ms = int(event.start_time.timestamp() * 1000)
            end_ms = int(event.end_time.timestamp() * 1000)
            print(f"{i:2d}. {start_ms} → {end_ms} "
                  f"(持续{event.duration_ms}ms, 高度{event.max_height_change:.3f}m)")
        
        # 格式4: 相对时间（从第一个数据点开始）
        if sensor_data:
            base_time = sensor_data[0].timestamp
            print(f"\n📊 格式4: 相对时间 (从{base_time.strftime('%H:%M:%S')}开始的秒数)")
            print("-" * 80)
            for i, event in enumerate(valid_events, 1):
                start_rel = (event.start_time - base_time).total_seconds()
                end_rel = (event.end_time - base_time).total_seconds()
                print(f"{i:2d}. +{start_rel:.3f}s → +{end_rel:.3f}s "
                      f"(持续{event.duration_seconds:.3f}s, 高度{event.max_height_change:.3f}m)")
        
        return valid_events
    
    def export_timestamps_csv(self, events, filename: str):
        """导出时间戳到CSV文件"""
        csv_filename = filename.replace('.txt', '_timestamps.csv')
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = [
                '序号', '开始时间戳', '结束时间戳', 
                '开始Unix时间戳', '结束Unix时间戳',
                '开始毫秒时间戳', '结束毫秒时间戳',
                '持续时间(秒)', '持续时间(毫秒)', 
                '滞空高度(米)', '最大加速度(mg)', '置信度'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for i, event in enumerate(events, 1):
                writer.writerow({
                    '序号': i,
                    '开始时间戳': event.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    '结束时间戳': event.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    '开始Unix时间戳': f"{event.start_time.timestamp():.3f}",
                    '结束Unix时间戳': f"{event.end_time.timestamp():.3f}",
                    '开始毫秒时间戳': int(event.start_time.timestamp() * 1000),
                    '结束毫秒时间戳': int(event.end_time.timestamp() * 1000),
                    '持续时间(秒)': f"{event.duration_seconds:.3f}",
                    '持续时间(毫秒)': event.duration_ms,
                    '滞空高度(米)': f"{event.max_height_change:.3f}",
                    '最大加速度(mg)': f"{event.max_acceleration:.1f}",
                    '置信度': f"{event.confidence:.3f}"
                })
        
        print(f"\n💾 时间戳数据已导出到: {csv_filename}")
    
    def export_timestamps_json(self, events, filename: str):
        """导出时间戳到JSON文件"""
        json_filename = filename.replace('.txt', '_timestamps.json')
        
        data = {
            'metadata': {
                'export_time': datetime.now().isoformat(),
                'total_events': len(events),
                'description': '滑雪滞空事件时间戳数据'
            },
            'events': []
        }
        
        for i, event in enumerate(events, 1):
            event_data = {
                'id': i,
                'timestamps': {
                    'start': {
                        'iso': event.start_time.isoformat(),
                        'formatted': event.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                        'unix': event.start_time.timestamp(),
                        'unix_ms': int(event.start_time.timestamp() * 1000)
                    },
                    'end': {
                        'iso': event.end_time.isoformat(),
                        'formatted': event.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                        'unix': event.end_time.timestamp(),
                        'unix_ms': int(event.end_time.timestamp() * 1000)
                    }
                },
                'duration': {
                    'seconds': round(event.duration_seconds, 3),
                    'milliseconds': event.duration_ms
                },
                'metrics': {
                    'airtime_height_meters': round(event.max_height_change, 3),
                    'max_acceleration_mg': round(event.max_acceleration, 1),
                    'confidence': round(event.confidence, 3)
                }
            }
            data['events'].append(event_data)
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 时间戳数据已导出到: {json_filename}")
    
    def generate_video_sync_markers(self, events, video_start_time=None):
        """生成视频同步标记"""
        print(f"\n🎬 视频同步标记")
        print("-" * 80)
        
        if video_start_time:
            print(f"视频开始时间: {video_start_time}")
            base_time = datetime.fromisoformat(video_start_time)
        else:
            print("使用数据开始时间作为基准")
            base_time = events[0].start_time if events else None
        
        if not base_time:
            print("❌ 无法确定基准时间")
            return
        
        print(f"基准时间: {base_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        print()
        
        for i, event in enumerate(events, 1):
            start_offset = (event.start_time - base_time).total_seconds()
            end_offset = (event.end_time - base_time).total_seconds()
            
            # 转换为视频时间格式 (MM:SS.mmm)
            start_video_time = f"{int(start_offset//60):02d}:{start_offset%60:06.3f}"
            end_video_time = f"{int(end_offset//60):02d}:{end_offset%60:06.3f}"
            
            print(f"滞空事件 {i}: {start_video_time} - {end_video_time} "
                  f"(高度{event.max_height_change:.3f}m, 置信度{event.confidence:.3f})")


def main():
    """主函数"""
    tool = TimestampOutputTool()
    
    # 输出详细时间戳
    events = tool.output_detailed_timestamps('0.txt', min_confidence=0.5)
    
    if events:
        # 导出CSV
        tool.export_timestamps_csv(events, '0.txt')
        
        # 导出JSON
        tool.export_timestamps_json(events, '0.txt')
        
        # 生成视频同步标记
        tool.generate_video_sync_markers(events)
    
    print(f"\n✅ 时间戳输出完成！")


if __name__ == "__main__":
    main()
