#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细滞空事件报告生成器
输出包含时间段、持续时间、滞空高度、置信度的详细报告
"""

import csv
from datetime import datetime
from typing import List
from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import AirtimeEvent


class DetailedAirtimeReporter:
    """详细滞空事件报告生成器"""
    
    def __init__(self):
        self.detector = OptimizedAirtimeDetector()
    
    def generate_detailed_report(self, filename: str, min_confidence: float = 0.5):
        """生成详细的滞空事件报告"""
        print(f"🎿 详细滞空事件分析报告")
        print(f"数据文件: {filename}")
        print(f"最小置信度: {min_confidence}")
        print("=" * 80)
        
        # 加载数据并检测
        sensor_data = self.detector.load_sensor_data(filename)
        if not sensor_data:
            print("❌ 无法加载数据")
            return
        
        airtime_events = self.detector.detect_airtime_events(sensor_data)
        
        # 筛选有效事件
        valid_events = [e for e in airtime_events if e.confidence >= min_confidence]
        
        if not valid_events:
            print(f"❌ 未检测到置信度≥{min_confidence}的滞空事件")
            return
        
        # 计算总体统计
        total_duration = sum(e.duration_seconds for e in valid_events)
        avg_duration = total_duration / len(valid_events)
        avg_confidence = sum(e.confidence for e in valid_events) / len(valid_events)
        avg_height = sum(e.max_height_change for e in valid_events) / len(valid_events)
        
        print(f"📊 总体统计:")
        print(f"   有效滞空事件: {len(valid_events)} 次")
        print(f"   总滞空时间: {total_duration:.2f} 秒")
        print(f"   平均持续时间: {avg_duration:.2f} 秒")
        print(f"   平均滞空高度: {avg_height:.1f}")
        print(f"   平均置信度: {avg_confidence:.2f}")
        
        # 按持续时间分类
        short_events = [e for e in valid_events if e.duration_seconds < 0.3]
        medium_events = [e for e in valid_events if 0.3 <= e.duration_seconds < 0.8]
        long_events = [e for e in valid_events if e.duration_seconds >= 0.8]
        
        print(f"\n⏱️ 按持续时间分类:")
        print(f"   短时滞空 (<0.3s): {len(short_events)} 次")
        print(f"   中等滞空 (0.3-0.8s): {len(medium_events)} 次")
        print(f"   长时滞空 (≥0.8s): {len(long_events)} 次")
        
        # 按高度分类
        low_height = [e for e in valid_events if e.max_height_change < 5]
        medium_height = [e for e in valid_events if 5 <= e.max_height_change < 15]
        high_height = [e for e in valid_events if e.max_height_change >= 15]
        
        print(f"\n🏔️ 按滞空高度分类:")
        print(f"   低高度 (<5): {len(low_height)} 次")
        print(f"   中等高度 (5-15): {len(medium_height)} 次")
        print(f"   高高度 (≥15): {len(high_height)} 次")
        
        # 详细事件列表
        print(f"\n📋 详细滞空事件列表:")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'等级':<6}")
        print("-" * 105)

        for i, event in enumerate(sorted(valid_events, key=lambda x: x.start_time), 1):
            # 确定等级
            if event.confidence >= 0.9:
                grade = "优秀"
            elif event.confidence >= 0.8:
                grade = "良好"
            elif event.confidence >= 0.7:
                grade = "中等"
            else:
                grade = "一般"

            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 精确到毫秒
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

            print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {grade:<6}")
        
        # 时间分布分析
        self._analyze_time_distribution(valid_events)
        
        # 导出到CSV
        self._export_to_csv(valid_events, filename)
        
        return valid_events
    
    def _analyze_time_distribution(self, events: List[AirtimeEvent]):
        """分析滞空事件的时间分布"""
        print(f"\n⏰ 时间分布分析:")
        
        # 按分钟统计
        minute_stats = {}
        for event in events:
            minute_key = event.start_time.strftime('%H:%M')
            if minute_key not in minute_stats:
                minute_stats[minute_key] = []
            minute_stats[minute_key].append(event)
        
        print(f"{'时间':<8} {'事件数':<8} {'总时长(s)':<12} {'平均置信度':<12}")
        print("-" * 45)
        
        for minute, minute_events in sorted(minute_stats.items()):
            total_duration = sum(e.duration_seconds for e in minute_events)
            avg_confidence = sum(e.confidence for e in minute_events) / len(minute_events)
            
            print(f"{minute:<8} {len(minute_events):<8} {total_duration:<12.2f} {avg_confidence:<12.2f}")
        
        # 找出最活跃的时间段
        most_active_minute = max(minute_stats.items(), key=lambda x: len(x[1]))
        print(f"\n🏆 最活跃时间段: {most_active_minute[0]} ({len(most_active_minute[1])} 次滞空)")
    
    def _export_to_csv(self, events: List[AirtimeEvent], original_filename: str):
        """导出详细数据到CSV文件"""
        csv_filename = original_filename.replace('.txt', '_detailed_airtime_report.csv')
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = [
                '序号', '开始时间', '结束时间', '时间段', 
                '持续时间(秒)', '滞空高度', '最大加速度(mg)', 
                '置信度', '等级', '备注'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for i, event in enumerate(sorted(events, key=lambda x: x.start_time), 1):
                # 确定等级和备注
                if event.confidence >= 0.9:
                    grade = "优秀"
                    note = "高质量滞空事件"
                elif event.confidence >= 0.8:
                    grade = "良好"
                    note = "质量较好"
                elif event.confidence >= 0.7:
                    grade = "中等"
                    note = "中等质量"
                else:
                    grade = "一般"
                    note = "需要验证"
                
                # 持续时间分类备注
                if event.duration_seconds >= 0.8:
                    note += ", 长时滞空"
                elif event.duration_seconds >= 0.3:
                    note += ", 中等时长"
                else:
                    note += ", 短时滞空"
                
                # 高度分类备注
                if event.max_height_change >= 15:
                    note += ", 高空动作"
                elif event.max_height_change >= 5:
                    note += ", 中等高度"
                else:
                    note += ", 低高度"
                
                writer.writerow({
                    '序号': i,
                    '开始时间': event.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    '结束时间': event.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    '时间段': f"{event.start_time.strftime('%H:%M:%S')}-{event.end_time.strftime('%H:%M:%S')}",
                    '持续时间(秒)': round(event.duration_seconds, 3),
                    '滞空高度': round(event.max_height_change, 1),
                    '最大加速度(mg)': round(event.max_acceleration, 1),
                    '置信度': round(event.confidence, 3),
                    '等级': grade,
                    '备注': note
                })
        
        print(f"\n💾 详细报告已导出到: {csv_filename}")
    
    def compare_confidence_levels(self, filename: str):
        """对比不同置信度水平的检测结果"""
        print(f"\n🎯 不同置信度水平对比分析")
        print("=" * 60)
        
        sensor_data = self.detector.load_sensor_data(filename)
        if not sensor_data:
            return
        
        airtime_events = self.detector.detect_airtime_events(sensor_data)
        
        confidence_levels = [0.5, 0.6, 0.7, 0.8, 0.85, 0.9]
        
        print(f"{'置信度阈值':<10} {'事件数':<8} {'总时长(s)':<12} {'平均时长(s)':<12} {'平均高度':<10}")
        print("-" * 65)
        
        for level in confidence_levels:
            filtered_events = [e for e in airtime_events if e.confidence >= level]
            
            if filtered_events:
                total_duration = sum(e.duration_seconds for e in filtered_events)
                avg_duration = total_duration / len(filtered_events)
                avg_height = sum(e.max_height_change for e in filtered_events) / len(filtered_events)
                
                print(f"≥{level:<9} {len(filtered_events):<8} {total_duration:<12.2f} "
                      f"{avg_duration:<12.2f} {avg_height:<10.1f}")
            else:
                print(f"≥{level:<9} {0:<8} {0:<12.2f} {0:<12.2f} {0:<10.1f}")


def main():
    """主函数"""
    reporter = DetailedAirtimeReporter()
    
    # 生成详细报告
    events = reporter.generate_detailed_report('20250330153942712068-济洲平花.txt', min_confidence=0.5)
    
    # 对比不同置信度水平
    reporter.compare_confidence_levels('20250330153942712068-济洲平花.txt')
    
    print(f"\n✅ 详细滞空事件分析完成！")
    print(f"💡 建议:")
    print(f"   1. 查看生成的CSV文件获取完整数据")
    print(f"   2. 根据视频验证高置信度事件的准确性")
    print(f"   3. 可以调整置信度阈值来筛选不同质量的事件")


if __name__ == "__main__":
    main()
