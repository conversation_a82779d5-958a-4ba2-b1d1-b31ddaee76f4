#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的时间戳验证工具
逐步验证每个计算步骤
"""

from fixed_timestamp_detector import FixedTimestampDetector
from datetime import datetime


def detailed_verification():
    """详细验证每个事件的时间戳计算"""
    print("🔍 详细时间戳验证工具")
    print("=" * 80)
    
    detector = FixedTimestampDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 检测事件
    events = detector.detect_airtime_events_fixed(sensor_data)
    
    if not events:
        print("❌ 未检测到滞空事件")
        return
    
    print(f"检测到 {len(events)} 个滞空事件")
    
    for i, event in enumerate(events, 1):
        print(f"\n{'='*60}")
        print(f"🎿 事件 {i} 详细分析")
        print(f"{'='*60}")
        
        # 获取开始和结束数据点
        start_data = sensor_data[event.start_data_index]
        end_data = sensor_data[event.end_data_index]
        
        print(f"📊 数据点信息:")
        print(f"   开始索引: {event.start_data_index}")
        print(f"   结束索引: {event.end_data_index}")
        print(f"   索引差: {event.end_data_index - event.start_data_index}")
        
        print(f"\n⏰ 原始时间信息:")
        print(f"   开始Python时间: {start_data.timestamp}")
        print(f"   开始毫秒字段: {start_data.milliseconds}")
        print(f"   结束Python时间: {end_data.timestamp}")
        print(f"   结束毫秒字段: {end_data.milliseconds}")
        
        # 手动计算持续时间
        time_diff_seconds = (end_data.timestamp - start_data.timestamp).total_seconds()
        time_diff_ms = time_diff_seconds * 1000
        ms_diff = end_data.milliseconds - start_data.milliseconds
        manual_duration_ms = int(time_diff_ms + ms_diff)
        
        print(f"\n🧮 持续时间计算步骤:")
        print(f"   Python时间差(秒): {time_diff_seconds}")
        print(f"   Python时间差(毫秒): {time_diff_ms}")
        print(f"   毫秒字段差: {ms_diff}")
        print(f"   总持续时间(毫秒): {time_diff_ms} + {ms_diff} = {manual_duration_ms}")
        print(f"   事件存储持续时间: {event.duration_ms}ms")
        print(f"   计算一致性: {'✅' if manual_duration_ms == event.duration_ms else '❌'}")
        
        # 生成时间戳字符串
        start_timestamp, end_timestamp = detector.get_precise_timestamps(sensor_data, event)
        
        print(f"\n📅 时间戳字符串:")
        print(f"   开始时间戳: {start_timestamp}")
        print(f"   结束时间戳: {end_timestamp}")
        
        # 从时间戳字符串反向计算持续时间
        try:
            start_dt = datetime.strptime(start_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            end_dt = datetime.strptime(end_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            timestamp_duration_seconds = (end_dt - start_dt).total_seconds()
            timestamp_duration_ms = timestamp_duration_seconds * 1000
            
            print(f"\n🔄 时间戳反向验证:")
            print(f"   解析开始时间: {start_dt}")
            print(f"   解析结束时间: {end_dt}")
            print(f"   时间戳计算持续时间(秒): {timestamp_duration_seconds}")
            print(f"   时间戳计算持续时间(毫秒): {timestamp_duration_ms}")
            print(f"   与存储持续时间差异: {abs(timestamp_duration_ms - event.duration_ms):.1f}ms")
            
            # 检查是否一致
            is_consistent = abs(timestamp_duration_ms - event.duration_ms) < 1
            print(f"   时间戳一致性: {'✅' if is_consistent else '❌'}")
            
            # 显示格式验证
            print(f"\n📋 显示格式验证:")
            print(f"   显示持续时间(秒): {event.duration_seconds:.3f}")
            print(f"   手动计算(秒): {timestamp_duration_seconds:.3f}")
            print(f"   显示格式一致性: {'✅' if abs(event.duration_seconds - timestamp_duration_seconds) < 0.001 else '❌'}")
            
        except Exception as e:
            print(f"   ❌ 时间戳解析错误: {e}")
        
        # 检查可能的问题
        print(f"\n⚠️  潜在问题检查:")
        
        # 1. 检查是否跨越了秒边界
        if start_data.timestamp != end_data.timestamp:
            print(f"   ✅ 跨越秒边界: {start_data.timestamp} → {end_data.timestamp}")
        else:
            print(f"   ⚠️  同一秒内: {start_data.timestamp}")
        
        # 2. 检查毫秒字段的合理性
        if 0 <= start_data.milliseconds <= 999 and 0 <= end_data.milliseconds <= 999:
            print(f"   ✅ 毫秒字段合理: {start_data.milliseconds}, {end_data.milliseconds}")
        else:
            print(f"   ❌ 毫秒字段异常: {start_data.milliseconds}, {end_data.milliseconds}")
        
        # 3. 检查持续时间的合理性
        if 50 <= event.duration_ms <= 5000:
            print(f"   ✅ 持续时间合理: {event.duration_ms}ms")
        else:
            print(f"   ⚠️  持续时间异常: {event.duration_ms}ms")


def compare_calculation_methods():
    """对比不同的计算方法"""
    print(f"\n🔬 计算方法对比")
    print("=" * 80)
    
    detector = FixedTimestampDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    events = detector.detect_airtime_events_fixed(sensor_data)
    
    if not events:
        return
    
    print(f"{'事件':<6} {'方法1:存储':<12} {'方法2:Python':<12} {'方法3:时间戳':<12} {'差异1-2':<10} {'差异1-3':<10}")
    print("-" * 75)
    
    for i, event in enumerate(events, 1):
        start_data = sensor_data[event.start_data_index]
        end_data = sensor_data[event.end_data_index]
        
        # 方法1: 事件存储的持续时间
        method1 = event.duration_ms
        
        # 方法2: 直接用Python datetime计算
        time_diff = (end_data.timestamp - start_data.timestamp).total_seconds() * 1000
        ms_diff = end_data.milliseconds - start_data.milliseconds
        method2 = time_diff + ms_diff
        
        # 方法3: 从时间戳字符串计算
        start_timestamp, end_timestamp = detector.get_precise_timestamps(sensor_data, event)
        try:
            start_dt = datetime.strptime(start_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            end_dt = datetime.strptime(end_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            method3 = (end_dt - start_dt).total_seconds() * 1000
        except:
            method3 = 0
        
        diff_1_2 = abs(method1 - method2)
        diff_1_3 = abs(method1 - method3)
        
        print(f"事件{i:<3} {method1:<12.0f} {method2:<12.0f} {method3:<12.0f} {diff_1_2:<10.1f} {diff_1_3:<10.1f}")


def main():
    """主函数"""
    detailed_verification()
    compare_calculation_methods()
    
    print(f"\n💡 总结:")
    print(f"   如果所有验证都显示 ✅，说明时间戳计算完全正确")
    print(f"   如果有 ❌，请检查具体的错误信息")
    print(f"   时间戳应该与持续时间完全对应，误差不超过1ms")


if __name__ == "__main__":
    main()
