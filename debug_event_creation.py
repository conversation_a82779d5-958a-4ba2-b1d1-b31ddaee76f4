#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试事件创建过程
分析为什么开始和结束时间相同
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import SensorData
from typing import List


class DebugAirtimeDetector(OptimizedAirtimeDetector):
    """调试版本的滞空检测器"""
    
    def debug_detect_airtime_events(self, sensor_data: List[SensorData]):
        """调试版本的滞空检测"""
        if len(sensor_data) < 10:
            return []
        
        print("🔍 调试滞空检测过程...")
        
        # 生成检测模式
        patterns = []
        for i, data in enumerate(sensor_data):
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()
            
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)
            
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and 
                 angular_velocity > self.angular_velocity_threshold * 0.5) or
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)
            )
            
            patterns.append({
                'index': i,
                'timestamp': data.timestamp,
                'milliseconds': data.milliseconds,
                'is_airborne': is_airborne,
                'z_gravity_deviation': z_gravity_deviation,
                'angular_velocity': angular_velocity,
                'z_weightless': z_weightless,
                'z_spike': z_spike
            })
        
        # 应用一致性过滤
        filtered_conditions = self._apply_consistency_filter([p['is_airborne'] for p in patterns])
        
        print(f"原始条件满足点数: {sum(p['is_airborne'] for p in patterns)}")
        print(f"过滤后条件满足点数: {sum(filtered_conditions)}")
        
        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0
        event_count = 0
        
        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
                event_count += 1
                
                print(f"\n🛫 事件 {event_count} 开始:")
                print(f"   索引: {i}")
                print(f"   时间: {airtime_start.timestamp}")
                print(f"   毫秒: {airtime_start.milliseconds}")
                
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                print(f"🛬 事件 {event_count} 结束:")
                print(f"   索引: {i}")
                print(f"   时间: {sensor_data[i].timestamp}")
                print(f"   毫秒: {sensor_data[i].milliseconds}")
                
                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])
                    
                    print(f"📊 事件 {event_count} 分析:")
                    print(f"   开始索引: {airtime_start_idx}")
                    print(f"   结束索引: {i}")
                    print(f"   索引差: {i - airtime_start_idx}")
                    print(f"   开始时间: {airtime_start.timestamp}")
                    print(f"   结束时间: {sensor_data[i].timestamp}")
                    print(f"   时间相同: {'是' if airtime_start.timestamp == sensor_data[i].timestamp else '否'}")
                    print(f"   计算持续时间: {duration_ms}ms")
                    
                    # 检查时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        print(f"   ✅ 通过时间过滤")
                        airtime_events.append({
                            'start_idx': airtime_start_idx,
                            'end_idx': i,
                            'start_time': airtime_start.timestamp,
                            'end_time': sensor_data[i].timestamp,
                            'duration_ms': duration_ms,
                            'same_timestamp': airtime_start.timestamp == sensor_data[i].timestamp
                        })
                    else:
                        print(f"   ❌ 未通过时间过滤 (范围: {self.min_airtime_ms}-{self.max_airtime_ms}ms)")
                
                airtime_start = None
                airtime_start_idx = 0
        
        print(f"\n📋 事件创建总结:")
        print(f"检测到的事件数: {len(airtime_events)}")
        
        same_timestamp_count = sum(1 for event in airtime_events if event['same_timestamp'])
        print(f"开始结束时间相同的事件: {same_timestamp_count}")
        
        if same_timestamp_count > 0:
            print(f"\n⚠️  时间相同的事件详情:")
            for i, event in enumerate(airtime_events):
                if event['same_timestamp']:
                    print(f"   事件 {i+1}: 索引 {event['start_idx']}-{event['end_idx']}, "
                          f"持续时间 {event['duration_ms']}ms")
        
        return airtime_events


def main():
    """主函数"""
    print("🔧 事件创建调试工具")
    print("=" * 60)
    
    detector = DebugAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间范围: {sensor_data[0].timestamp} - {sensor_data[-1].timestamp}")
    
    # 调试检测过程
    events = detector.debug_detect_airtime_events(sensor_data)
    
    print(f"\n💡 分析结论:")
    if any(event['same_timestamp'] for event in events):
        print(f"   ❌ 存在开始结束时间相同的事件")
        print(f"   原因: 滞空检测的开始和结束指向了同一个数据点")
        print(f"   解决方案: 需要修改检测逻辑，确保开始和结束是不同的数据点")
    else:
        print(f"   ✅ 所有事件的开始结束时间都不相同")
    
    print(f"\n🔧 建议:")
    print(f"   1. 检查一致性过滤是否过于严格")
    print(f"   2. 确保滞空状态的开始和结束逻辑正确")
    print(f"   3. 考虑增加最小持续数据点数的要求")


if __name__ == "__main__":
    main()
