#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析15:36分时间段的传感器数据特征
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator
import matplotlib.pyplot as plt
from datetime import datetime

def analyze_15_36_data():
    """分析15:36分的数据特征"""
    
    calculator = SkiingAirtimeCalculator()
    sensor_data = calculator.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("无法加载数据")
        return
    
    # 筛选15:36分的数据
    data_15_36 = []
    for data in sensor_data:
        if data.timestamp.hour == 15 and data.timestamp.minute == 36:
            data_15_36.append(data)
    
    print(f"15:36分数据点数量: {len(data_15_36)}")
    
    if not data_15_36:
        print("15:36分没有数据")
        return
    
    # 分析传感器特征
    accelerations = [data.total_acceleration() for data in data_15_36]
    angular_velocities = [data.total_angular_velocity() for data in data_15_36]
    gravity_deviations = [abs(acc - 1000) for acc in accelerations]
    
    print(f"\n15:36分传感器数据分析:")
    print(f"时间范围: {data_15_36[0].timestamp} 到 {data_15_36[-1].timestamp}")
    print(f"总加速度范围: {min(accelerations):.1f} - {max(accelerations):.1f} mg")
    print(f"平均加速度: {sum(accelerations)/len(accelerations):.1f} mg")
    print(f"重力偏差范围: {min(gravity_deviations):.1f} - {max(gravity_deviations):.1f} mg")
    print(f"最大重力偏差: {max(gravity_deviations):.1f} mg (阈值: 800mg)")
    print(f"角速度范围: {min(angular_velocities):.1f} - {max(angular_velocities):.1f} 度/秒")
    print(f"最大角速度: {max(angular_velocities):.1f} 度/秒 (阈值: 1000度/秒)")
    
    # 检查是否有超过阈值的点
    high_gravity_points = [gd for gd in gravity_deviations if gd > 800]
    high_angular_points = [av for av in angular_velocities if av > 1000]
    
    print(f"\n阈值分析:")
    print(f"重力偏差 > 800mg 的点数: {len(high_gravity_points)} / {len(data_15_36)}")
    print(f"角速度 > 1000度/秒 的点数: {len(high_angular_points)} / {len(data_15_36)}")
    
    if high_gravity_points:
        print(f"最大重力偏差值: {max(high_gravity_points):.1f} mg")
    if high_angular_points:
        print(f"最大角速度值: {max(high_angular_points):.1f} 度/秒")
    
    # 检查是否有满足滞空条件的连续时间段
    airborne_points = []
    for i, data in enumerate(data_15_36):
        total_acc = data.total_acceleration()
        gravity_deviation = abs(total_acc - 1000)
        angular_velocity = data.total_angular_velocity()
        
        is_airborne = (gravity_deviation > 800 or angular_velocity > 1000)
        if is_airborne:
            airborne_points.append(i)
    
    print(f"\n满足滞空条件的数据点: {len(airborne_points)} / {len(data_15_36)}")
    
    if airborne_points:
        print("满足滞空条件的时间点:")
        for i in airborne_points[:10]:  # 只显示前10个
            data = data_15_36[i]
            print(f"  {data.timestamp.strftime('%H:%M:%S.%f')[:-3]} - "
                  f"加速度: {data.total_acceleration():.1f}mg, "
                  f"角速度: {data.total_angular_velocity():.1f}度/秒")
    
    # 对比其他时间段
    print(f"\n对比分析:")
    
    # 15:35分数据
    data_15_35 = [data for data in sensor_data if data.timestamp.hour == 15 and data.timestamp.minute == 35]
    if data_15_35:
        acc_15_35 = [data.total_acceleration() for data in data_15_35]
        ang_15_35 = [data.total_angular_velocity() for data in data_15_35]
        grav_dev_15_35 = [abs(acc - 1000) for acc in acc_15_35]
        
        print(f"15:35分 - 最大重力偏差: {max(grav_dev_15_35):.1f}mg, 最大角速度: {max(ang_15_35):.1f}度/秒")
    
    # 15:37分数据
    data_15_37 = [data for data in sensor_data if data.timestamp.hour == 15 and data.timestamp.minute == 37]
    if data_15_37:
        acc_15_37 = [data.total_acceleration() for data in data_15_37]
        ang_15_37 = [data.total_angular_velocity() for data in data_15_37]
        grav_dev_15_37 = [abs(acc - 1000) for acc in acc_15_37]
        
        print(f"15:37分 - 最大重力偏差: {max(grav_dev_15_37):.1f}mg, 最大角速度: {max(ang_15_37):.1f}度/秒")
    
    # 分析Z轴位置变化（可能表示高度变化）
    z_positions = [data.z for data in data_15_36]
    z_range = max(z_positions) - min(z_positions)
    print(f"\n15:36分Z轴位置变化: {z_range} (范围: {min(z_positions)} 到 {max(z_positions)})")
    
    return data_15_36, accelerations, angular_velocities, gravity_deviations


def main():
    print("分析15:36分时间段数据...")
    analyze_15_36_data()


if __name__ == "__main__":
    main()
