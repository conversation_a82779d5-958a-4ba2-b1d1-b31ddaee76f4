#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正时间戳的滞空检测器
确保输出的时间戳与持续时间计算一致
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import AirtimeEvent, SensorData
from typing import List
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CorrectedAirtimeEvent(AirtimeEvent):
    """修正的滞空事件，包含精确的时间戳信息"""
    start_timestamp_str: str = ""  # 开始时间戳字符串
    end_timestamp_str: str = ""    # 结束时间戳字符串
    start_milliseconds: int = 0    # 开始毫秒值
    end_milliseconds: int = 0      # 结束毫秒值


class CorrectedTimestampDetector(OptimizedAirtimeDetector):
    """修正时间戳的检测器"""
    
    def detect_airtime_events_with_correct_timestamps(self, sensor_data: List[SensorData]) -> List[CorrectedAirtimeEvent]:
        """检测滞空事件并记录正确的时间戳"""
        if len(sensor_data) < 10:
            return []

        print("🎿 使用修正时间戳的检测算法...")

        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, data in enumerate(sensor_data):
            # 使用基础的滞空检测逻辑
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()

            # 检测Z轴突变
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)

            # 综合判断
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and
                 angular_velocity > self.angular_velocity_threshold * 0.5) or
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)
            )
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = data
                airtime_start_idx = i

            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        
                        # 计算滞空特征
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        
                        # 基础置信度计算
                        confidence = self._calculate_confidence(airtime_segment, duration_ms)
                        
                        # 计算旋转相关指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
                        
                        # 获取精确的开始和结束数据点
                        start_data = sensor_data[airtime_start_idx]
                        end_data = sensor_data[i]
                        
                        # 生成精确的时间戳字符串
                        start_timestamp_str = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
                        end_timestamp_str = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
                        
                        event = CorrectedAirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=sensor_data[i].timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected'],
                            start_timestamp_str=start_timestamp_str,
                            end_timestamp_str=end_timestamp_str,
                            start_milliseconds=start_data.milliseconds,
                            end_milliseconds=end_data.milliseconds
                        )
                        
                        airtime_events.append(event)
                
                airtime_start = None
                airtime_start_idx = 0
        
        print(f"🎿 修正检测完成，发现 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def verify_timestamp_consistency(self, events: List[CorrectedAirtimeEvent]):
        """验证时间戳的一致性"""
        print(f"\n🔍 时间戳一致性验证:")
        print(f"{'序号':<4} {'存储持续时间(ms)':<15} {'时间戳计算(ms)':<15} {'差异(ms)':<10} {'一致性':<8}")
        print("-" * 65)
        
        for i, event in enumerate(events, 1):
            # 从时间戳字符串计算持续时间
            try:
                start_dt = datetime.strptime(event.start_timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                end_dt = datetime.strptime(event.end_timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                timestamp_duration_ms = (end_dt - start_dt).total_seconds() * 1000
                
                difference = abs(event.duration_ms - timestamp_duration_ms)
                is_consistent = difference < 50  # 允许50ms的误差
                
                print(f"{i:<4} {event.duration_ms:<15} {timestamp_duration_ms:<15.0f} {difference:<10.0f} {'✅' if is_consistent else '❌':<8}")
                
            except Exception as e:
                print(f"{i:<4} {event.duration_ms:<15} {'解析错误':<15} {'N/A':<10} {'❌':<8}")


def main():
    """主函数"""
    print("🔧 修正时间戳的滞空检测器测试")
    print("=" * 60)
    
    detector = CorrectedTimestampDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 使用修正的检测方法
    corrected_events = detector.detect_airtime_events_with_correct_timestamps(sensor_data)
    
    if not corrected_events:
        print("❌ 未检测到滞空事件")
        return
    
    # 分析结果
    stats = detector.analyze_airtime_statistics(corrected_events)
    
    print(f"\n📊 检测结果:")
    print(f"总检测事件: {stats['total_events']} 次")
    print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
    print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
    
    # 获取高质量事件
    quality_events = [e for e in corrected_events if e.confidence >= 0.6]
    
    print(f"\n🏆 高质量滞空事件 (置信度≥0.6): {len(quality_events)} 次")
    
    if quality_events:
        print(f"\n详细列表:")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'旋转':<6} {'最大转速(°/s)':<12}")
        print("-" * 115)
        
        for i, event in enumerate(quality_events[:10], 1):  # 只显示前10个
            rotation_indicator = "🌀" if event.rotation_detected else "—"
            print(f"{i:<4} {event.start_timestamp_str:<23} {event.end_timestamp_str:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {rotation_indicator:<6} {event.max_rotation_speed:<12.0f}")
    
    # 验证时间戳一致性
    detector.verify_timestamp_consistency(quality_events[:5])  # 验证前5个事件
    
    print(f"\n💡 修正说明:")
    print(f"   ✅ 时间戳直接从传感器数据点获取，确保精确性")
    print(f"   ✅ 持续时间计算与时间戳显示保持一致")
    print(f"   ✅ 包含真实的毫秒值，不是补零格式")
    print(f"   ✅ 验证时间戳计算的一致性")


if __name__ == "__main__":
    main()
