#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有效滞空事件专项分析工具
专注于分析置信度>0.5的有效滞空事件
"""

import os
from typing import List
from skiing_airtime_calculator import SkiingAirtimeCalculator, AirtimeEvent
from config import get_config_for_scenario


def analyze_valid_airtime_events(filename: str, scenario: str = 'alpine') -> dict:
    """分析有效滞空事件"""
    print(f"\n{'='*60}")
    print(f"有效滞空事件分析: {os.path.basename(filename)}")
    print(f"有效标准: 置信度 > 0.5")
    print(f"{'='*60}")
    
    # 获取配置并创建计算器
    config = get_config_for_scenario(scenario)
    calculator = SkiingAirtimeCalculator(**config)
    
    # 加载数据
    sensor_data = calculator.load_sensor_data(filename)
    if not sensor_data:
        print("❌ 无法加载数据")
        return {}
    
    # 检测滞空事件
    airtime_events = calculator.detect_airtime_events(sensor_data)
    stats = calculator.analyze_airtime_statistics(airtime_events)
    
    # 筛选有效事件
    valid_events = [e for e in airtime_events if e.confidence > 0.5]
    
    total_duration = (sensor_data[-1].timestamp - sensor_data[0].timestamp).total_seconds()
    
    print(f"📊 基本统计:")
    print(f"   总检测事件: {stats['total_events']} 次")
    print(f"   🎯 有效滞空事件: {len(valid_events)} 次")
    print(f"   有效率: {len(valid_events)/stats['total_events']*100:.1f}%" if stats['total_events'] > 0 else "   有效率: 0%")
    print(f"   有效滞空总时间: {stats['valid_airtime_seconds']:.2f} 秒")
    print(f"   有效滞空占比: {stats['valid_airtime_seconds']/total_duration*100:.1f}%")
    print(f"   有效滞空频率: {len(valid_events)/(total_duration/60):.1f} 次/分钟")
    
    if valid_events:
        # 有效事件的详细分析
        valid_durations = [e.duration_seconds for e in valid_events]
        valid_confidences = [e.confidence for e in valid_events]
        valid_accelerations = [e.max_acceleration for e in valid_events]
        valid_height_changes = [e.max_height_change for e in valid_events]
        
        print(f"\n📈 有效事件分析:")
        print(f"   平均持续时间: {sum(valid_durations)/len(valid_durations):.2f} 秒")
        print(f"   最长持续时间: {max(valid_durations):.2f} 秒")
        print(f"   最短持续时间: {min(valid_durations):.2f} 秒")
        print(f"   平均置信度: {sum(valid_confidences)/len(valid_confidences):.2f}")
        print(f"   最高置信度: {max(valid_confidences):.2f}")
        print(f"   平均最大加速度: {sum(valid_accelerations)/len(valid_accelerations):.1f} mg")
        print(f"   平均高度变化: {sum(valid_height_changes)/len(valid_height_changes):.1f}")
        
        # 按持续时间分类
        short_events = [e for e in valid_events if e.duration_seconds < 0.5]
        medium_events = [e for e in valid_events if 0.5 <= e.duration_seconds < 1.5]
        long_events = [e for e in valid_events if e.duration_seconds >= 1.5]
        
        print(f"\n⏱️ 按持续时间分类:")
        print(f"   短时滞空 (<0.5s): {len(short_events)} 次")
        print(f"   中等滞空 (0.5-1.5s): {len(medium_events)} 次")
        print(f"   长时滞空 (≥1.5s): {len(long_events)} 次")
        
        # 按置信度分类
        medium_confidence = [e for e in valid_events if 0.5 <= e.confidence < 0.7]
        high_confidence = [e for e in valid_events if 0.7 <= e.confidence < 0.9]
        very_high_confidence = [e for e in valid_events if e.confidence >= 0.9]
        
        print(f"\n🎯 按置信度分类:")
        print(f"   中等置信度 (0.5-0.7): {len(medium_confidence)} 次")
        print(f"   高置信度 (0.7-0.9): {len(high_confidence)} 次")
        print(f"   极高置信度 (≥0.9): {len(very_high_confidence)} 次")
        
        # 显示最佳有效事件
        print(f"\n🏆 最佳有效滞空事件:")
        
        # 最长持续时间事件
        longest_event = max(valid_events, key=lambda x: x.duration_seconds)
        print(f"   最长持续: {longest_event.start_time.strftime('%H:%M:%S')} - "
              f"{longest_event.end_time.strftime('%H:%M:%S')}, "
              f"持续 {longest_event.duration_seconds:.2f}s, 置信度 {longest_event.confidence:.2f}")
        
        # 最高置信度事件
        most_confident_event = max(valid_events, key=lambda x: x.confidence)
        print(f"   最高置信度: {most_confident_event.start_time.strftime('%H:%M:%S')} - "
              f"{most_confident_event.end_time.strftime('%H:%M:%S')}, "
              f"持续 {most_confident_event.duration_seconds:.2f}s, 置信度 {most_confident_event.confidence:.2f}")
        
        # 最大加速度事件
        max_acceleration_event = max(valid_events, key=lambda x: x.max_acceleration)
        print(f"   最大加速度: {max_acceleration_event.start_time.strftime('%H:%M:%S')} - "
              f"{max_acceleration_event.end_time.strftime('%H:%M:%S')}, "
              f"加速度 {max_acceleration_event.max_acceleration:.1f}mg, 置信度 {max_acceleration_event.confidence:.2f}")
        
        # 显示所有有效事件列表
        print(f"\n📋 所有有效滞空事件详单:")
        print(f"{'序号':<4} {'开始时间':<10} {'结束时间':<10} {'持续(s)':<8} {'置信度':<8} {'加速度(mg)':<12} {'高度变化':<8}")
        print("-" * 75)
        
        for i, event in enumerate(sorted(valid_events, key=lambda x: x.start_time), 1):
            print(f"{i:<4} {event.start_time.strftime('%H:%M:%S'):<10} "
                  f"{event.end_time.strftime('%H:%M:%S'):<10} "
                  f"{event.duration_seconds:<8.2f} {event.confidence:<8.2f} "
                  f"{event.max_acceleration:<12.1f} {event.max_height_change:<8.1f}")
    
    return {
        'filename': filename,
        'total_events': stats['total_events'],
        'valid_events': len(valid_events),
        'valid_airtime_seconds': stats['valid_airtime_seconds'],
        'valid_events_list': valid_events
    }


def export_valid_events_to_csv(valid_events: List[AirtimeEvent], filename: str) -> None:
    """导出有效滞空事件到CSV文件"""
    import csv
    
    csv_filename = filename.replace('.txt', '_valid_airtime_events.csv')
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['序号', '开始时间', '结束时间', '持续时间(秒)', '置信度', '最大加速度(mg)', '最大高度变化']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for i, event in enumerate(sorted(valid_events, key=lambda x: x.start_time), 1):
            writer.writerow({
                '序号': i,
                '开始时间': event.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                '结束时间': event.end_time.strftime('%Y-%m-%d %H:%M:%S'),
                '持续时间(秒)': round(event.duration_seconds, 2),
                '置信度': round(event.confidence, 2),
                '最大加速度(mg)': round(event.max_acceleration, 1),
                '最大高度变化': round(event.max_height_change, 1)
            })
    
    print(f"\n💾 有效滞空事件已导出到: {csv_filename}")


def compare_scenarios_for_valid_events(filename: str) -> None:
    """比较不同场景配置对有效滞空事件检测的影响"""
    print(f"\n{'='*60}")
    print(f"不同场景配置的有效滞空事件对比")
    print(f"{'='*60}")
    
    scenarios = ['freestyle', 'alpine', 'snowboard', 'cross_country']
    results = []
    
    for scenario in scenarios:
        config = get_config_for_scenario(scenario)
        calculator = SkiingAirtimeCalculator(**config)
        
        sensor_data = calculator.load_sensor_data(filename)
        if not sensor_data:
            continue
            
        airtime_events = calculator.detect_airtime_events(sensor_data)
        valid_events = [e for e in airtime_events if e.confidence > 0.5]
        valid_airtime = sum(e.duration_seconds for e in valid_events)
        
        results.append({
            'scenario': scenario,
            'total_events': len(airtime_events),
            'valid_events': len(valid_events),
            'valid_airtime': valid_airtime,
            'valid_rate': len(valid_events)/len(airtime_events)*100 if airtime_events else 0
        })
    
    # 显示对比表格
    print(f"{'场景':<15} {'总事件':<8} {'有效事件':<8} {'有效率(%)':<10} {'有效时长(s)':<12}")
    print("-" * 65)
    for result in results:
        print(f"{result['scenario'].upper():<15} {result['total_events']:<8} "
              f"{result['valid_events']:<8} {result['valid_rate']:<10.1f} "
              f"{result['valid_airtime']:<12.2f}")
    
    # 推荐最佳场景
    if results:
        best_scenario = max(results, key=lambda x: x['valid_events'])
        print(f"\n🎯 推荐场景: {best_scenario['scenario'].upper()}")
        print(f"   理由: 检测到最多的有效滞空事件 ({best_scenario['valid_events']} 次)")


def main():
    """主函数"""
    print("🎿 有效滞空事件专项分析工具")
    print("有效标准: 置信度 > 0.5")
    print("=" * 60)
    
    # 查找数据文件
    import glob
    data_files = glob.glob('*.txt')
    data_files = [f for f in data_files if not any(skip in f.lower() for skip in ['readme', 'config', 'example'])]
    
    if not data_files:
        print("❌ 未找到数据文件")
        return
    
    print(f"找到 {len(data_files)} 个数据文件")
    
    all_valid_events = []
    
    for filename in data_files:
        try:
            result = analyze_valid_airtime_events(filename)
            if result and result.get('valid_events_list'):
                all_valid_events.extend(result['valid_events_list'])
                
                # 导出CSV
                export_valid_events_to_csv(result['valid_events_list'], filename)
                
                # 如果只有一个文件，进行场景对比
                if len(data_files) == 1:
                    compare_scenarios_for_valid_events(filename)
                    
        except Exception as e:
            print(f"❌ 分析文件 {filename} 时出错: {e}")
    
    # 总结
    if all_valid_events:
        print(f"\n{'='*60}")
        print(f"📊 总体有效滞空事件汇总")
        print(f"{'='*60}")
        
        total_valid_duration = sum(e.duration_seconds for e in all_valid_events)
        avg_confidence = sum(e.confidence for e in all_valid_events) / len(all_valid_events)
        
        print(f"总有效滞空事件: {len(all_valid_events)} 次")
        print(f"总有效滞空时间: {total_valid_duration:.2f} 秒")
        print(f"平均置信度: {avg_confidence:.2f}")
        print(f"平均持续时间: {total_valid_duration/len(all_valid_events):.2f} 秒")
        
        # 按置信度分级统计
        high_conf = len([e for e in all_valid_events if e.confidence >= 0.8])
        medium_conf = len([e for e in all_valid_events if 0.6 <= e.confidence < 0.8])
        low_conf = len([e for e in all_valid_events if 0.5 <= e.confidence < 0.6])
        
        print(f"\n置信度分布:")
        print(f"  高置信度 (≥0.8): {high_conf} 次 ({high_conf/len(all_valid_events)*100:.1f}%)")
        print(f"  中置信度 (0.6-0.8): {medium_conf} 次 ({medium_conf/len(all_valid_events)*100:.1f}%)")
        print(f"  低置信度 (0.5-0.6): {low_conf} 次 ({low_conf/len(all_valid_events)*100:.1f}%)")
    
    print(f"\n✅ 有效滞空事件分析完成！")


if __name__ == "__main__":
    main()
