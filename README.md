# 滑雪滞空时间计算算法

基于传感器数据（加速度计、陀螺仪、磁力计等）计算滑雪过程中的滞空时间的Python算法。

## 功能特点

- 🎿 **多传感器融合**: 结合加速度计、陀螺仪、磁力计数据进行综合分析
- ⏱️ **精确时间计算**: 毫秒级精度的滞空时间检测
- 🎯 **智能过滤**: 自动过滤噪声和误检，提供置信度评估
- 🏔️ **多场景适配**: 支持自由式、高山、单板、越野等不同滑雪场景
- 📊 **详细统计**: 提供完整的滞空事件统计和分析报告
- ⚙️ **灵活配置**: 可根据实际需求调整检测参数

## 数据格式

算法支持以下格式的传感器数据：

```
[2025-03-27 12:28:17 038] x: 0, y: 3, z: -83, P: 3, ax: -270, ay: -501, az: 461, gx: 4473, gy: -341, gz: -3918, mx: 5015, my: 4531, mz: -13097, t: 0, h: 27860, s: 0, fs: 0, lo: 90.07438044177501, la: 47.1811986161664, gh: 0, ca:(null), cs:(null), bt:0
```

### 数据字段说明

| 字段 | 说明 | 单位 |
|------|------|------|
| 时间戳 | 数据采集时间 | YYYY-MM-DD HH:MM:SS |
| 毫秒 | 时间戳的毫秒部分 | ms |
| x, y, z | 位置坐标 | - |
| P | 位置指示器 | - |
| ax, ay, az | 三轴加速度 | mg (毫克) |
| gx, gy, gz | 三轴角速度 | 度/秒 |
| mx, my, mz | 三轴磁场强度 | - |
| t | 温度 | - |
| h | 高度数据 | 海拔高度×10 (米) |
| s | 速度 | - |
| fs | 采样频率 | - |
| lo, la | 经纬度 | 度 |
| gh | GPS高度 | - |

## 安装依赖

```bash
pip install numpy matplotlib datetime
```

## 快速开始

### 基本使用

```python
from skiing_airtime_calculator import SkiingAirtimeCalculator

# 创建计算器实例
calculator = SkiingAirtimeCalculator()

# 加载传感器数据
sensor_data = calculator.load_sensor_data('新疆.txt')

# 检测滞空事件
airtime_events = calculator.detect_airtime_events(sensor_data)

# 分析统计数据
stats = calculator.analyze_airtime_statistics(airtime_events)

print(f"检测到 {stats['total_events']} 次滞空事件")
print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
```

### 自定义配置

```python
from skiing_airtime_calculator import SkiingAirtimeCalculator
from config import get_config_for_scenario

# 使用预设的自由式滑雪配置
config = get_config_for_scenario('freestyle')
calculator = SkiingAirtimeCalculator(**config)

# 或者自定义参数
calculator = SkiingAirtimeCalculator(
    gravity_threshold=800,      # 重力偏差阈值 (mg)
    min_airtime_ms=100,         # 最小滞空时间 (毫秒)
    max_airtime_ms=5000,        # 最大滞空时间 (毫秒)
    angular_velocity_threshold=1000,  # 角速度阈值 (度/秒)
    height_change_threshold=1.0       # 高度变化阈值
)
```

## 算法原理详解

### 🔬 滞空检测的物理基础

滑雪滞空检测基于以下物理原理：

1. **重力变化**: 滑雪者离开地面时，传感器感受到的重力加速度会发生显著变化
2. **失重状态**: 真正的滞空期间，Z轴加速度会接近0（自由落体状态）
3. **运动模式**: 滞空通常伴随复杂的三维旋转运动
4. **高度变化**: 滞空期间会有明显的高度起伏

### 🎯 核心检测算法

#### 1. Z轴重力检测（主要判断条件）

```python
# 使用Z轴加速度检测重力变化（更准确）
z_gravity_deviation = abs(abs(data.az) - standard_gravity)

# 检测失重状态（Z轴加速度接近0）
z_weightless = abs(data.az) < (standard_gravity * 0.2)

# 检测Z轴加速度突变（起跳或落地）
z_spike = abs(current_az - previous_az) > (standard_gravity * 0.8)
```

**为什么使用Z轴而不是总加速度？**
- ✅ **物理准确性**: 重力主要作用在垂直方向（Z轴）
- ✅ **减少误判**: X、Y轴包含水平运动（转弯、侧向力），容易误判
- ✅ **失重检测**: 真正滞空时Z轴加速度接近0

#### 2. 角速度分析（辅助判断条件）

```python
angular_velocity = sqrt(gx² + gy² + gz²)
high_angular = angular_velocity > angular_velocity_threshold
```

**角速度的作用**:
- 🔄 **空中动作**: 滞空期间通常有旋转、翻转等动作
- 🎿 **运动特征**: 区分滞空和普通滑行
- ⚖️ **权重调节**: 与Z轴检测结合，提高准确性

#### 3. 综合判断逻辑

```python
# 多重条件组合判断
is_airborne = (
    (z_gravity_deviation > gravity_threshold and
     angular_velocity > angular_velocity_threshold * 0.5) or  # Z轴偏差+适度角速度
    (z_weightless and angular_velocity > angular_velocity_threshold * 0.3) or  # 失重+轻微旋转
    (z_spike and angular_velocity > angular_velocity_threshold * 0.7)  # Z轴突变+较高角速度
)
```

### 📊 检测流程详解

```mermaid
graph TD
    A[传感器数据输入] --> B[数据解析与预处理]
    B --> C[Z轴加速度分析]
    C --> D[角速度计算]
    D --> E[多重条件判断]
    E --> F{满足滞空条件?}
    F -->|是| G[记录滞空开始]
    F -->|否| H[继续监测]
    G --> I[持续监测滞空状态]
    I --> J{滞空结束?}
    J -->|否| I
    J -->|是| K[记录滞空结束]
    K --> L[时间窗口过滤]
    L --> M[运动模式分析]
    M --> N[置信度计算]
    N --> O[输出滞空事件]
    H --> P[下一个数据点]
    P --> C
```

### 🔍 滞空高度计算

#### 高度数据源
- **数据字段**: 使用传感器数据中的`h`字段
- **数据含义**: `h = 海拔高度(米) × 10`
- **计算公式**: `滞空高度(米) = (max(h) - min(h)) / 10`

#### 计算示例
```python
# 滞空期间的h字段数据: [5417, 5419, 5421, 5418, 5416]
max_h = 5421
min_h = 5416
滞空高度 = (5421 - 5416) / 10 = 0.5米
```

### ⚙️ 关键参数详解

| 参数 | 默认值 | 作用 | 调整建议 |
|------|--------|------|----------|
| **gravity_threshold** | 600mg | Z轴重力偏差阈值 | 值越小越敏感，容易误判；值越大越严格，可能漏检 |
| **angular_velocity_threshold** | 800°/s | 角速度阈值 | 自由式滑雪可降低；越野滑雪可提高 |
| **min_airtime_ms** | 150ms | 最小滞空时间 | 过滤短时间噪声，建议100-300ms |
| **max_airtime_ms** | 3000ms | 最大滞空时间 | 过滤异常长时间事件，建议2-8秒 |
| **weightless_ratio** | 0.15 | 失重判断比例 | Z轴加速度<15%重力时认为失重 |
| **consistency_window** | 3 | 一致性检查窗口 | 连续多少个点满足条件才认为有效 |

### 🎯 置信度评估算法

置信度基于多个因素综合计算：

#### 1. 持续时间评分 (0-0.4)
```python
if 200ms <= duration <= 3000ms:
    duration_score = 0.4
elif duration < 200ms:
    duration_score = duration / 200 * 0.4
else:
    duration_score = max(0, 0.4 - (duration - 3000) / 5000 * 0.4)
```

#### 2. Z轴模式评分 (0-0.3)
```python
# 失重状态评分
weightless_ratio = weightless_points / total_points
z_pattern_score += min(0.15, weightless_ratio * 0.3)

# Z轴突变评分（起跳/落地特征）
if spike_count >= 2:  # 起跳和落地
    z_pattern_score += 0.15
```

#### 3. 角速度评分 (0-0.3)
```python
# 角速度变化评分
angular_variance = variance(angular_velocities)
if angular_variance > high_threshold:
    angular_score = 0.3
elif angular_variance > medium_threshold:
    angular_score = 0.2
```

#### 4. 最终置信度
```python
final_confidence = (duration_score + z_pattern_score + angular_score) / 1.0
final_confidence = min(1.0, final_confidence)
```

### 🏷️ 有效滞空事件标准

- **置信度 > 0.5**: 定义为有效滞空事件
- **置信度 > 0.7**: 定义为高质量滞空事件
- **置信度 > 0.85**: 定义为优秀滞空事件

### 🔧 算法优化特性

#### 1. 一致性过滤
- **目的**: 减少单点噪声干扰
- **方法**: 要求连续多个数据点满足滞空条件
- **效果**: 显著减少误判

#### 2. 运动模式分析
- **加速度模式**: 检查是否有起跳/落地特征
- **角速度模式**: 分析空中旋转动作
- **高度模式**: 验证高度变化合理性

#### 3. 智能阈值调整
- **自适应**: 根据数据特征自动调整部分阈值
- **场景优化**: 不同滑雪场景使用不同参数组合

### 🎯 算法判断逻辑总结

#### 滞空判断的核心依据

**主要判断条件（必须满足其一）**:

1. **Z轴重力异常 + 角速度变化**
   ```
   Z轴重力偏差 > 阈值 AND 角速度 > 阈值×0.5
   ```
   - 适用于: 明显的跳跃动作
   - 特征: 重力明显变化，伴随旋转

2. **失重状态 + 轻微旋转**
   ```
   Z轴加速度 < 重力×0.2 AND 角速度 > 阈值×0.3
   ```
   - 适用于: 自由落体阶段
   - 特征: 接近失重，有空中动作

3. **Z轴突变 + 高角速度**
   ```
   Z轴加速度突变 > 重力×0.8 AND 角速度 > 阈值×0.7
   ```
   - 适用于: 起跳/落地瞬间
   - 特征: 加速度剧变，强烈旋转

**辅助验证条件**:
- ✅ **时间合理性**: 150ms ≤ 持续时间 ≤ 3000ms
- ✅ **一致性检查**: 连续3个数据点满足条件
- ✅ **高度变化**: 基于h字段的高度起伏
- ✅ **运动模式**: 符合跳跃的物理特征

**最终确认标准**:
- 🎯 **置信度 ≥ 0.5**: 认定为有效滞空事件
- 🏆 **置信度 ≥ 0.7**: 认定为高质量滞空事件
- ⭐ **置信度 ≥ 0.85**: 认定为优秀滞空事件

#### 为什么这样判断？

1. **物理准确性**: 基于真实的物理现象（重力、失重、加速度变化）
2. **多重验证**: 不依赖单一指标，综合多个传感器数据
3. **噪声抑制**: 通过一致性检查和时间窗口过滤噪声
4. **场景适应**: 不同的判断条件适应不同的滑雪动作
5. **置信度量化**: 提供可信度评分，便于结果筛选

## 不同滑雪场景的配置

### 自由式滑雪 (Freestyle)
- 特点: 跳跃多，动作复杂
- 配置: 较低的重力阈值，较长的最大滞空时间

### 高山滑雪 (Alpine)
- 特点: 速度快，跳跃少
- 配置: 较高的重力阈值，较短的最大滞空时间

### 单板滑雪 (Snowboard)
- 特点: 技巧动作多，旋转频繁
- 配置: 中等重力阈值，较低的角速度阈值

### 越野滑雪 (Cross Country)
- 特点: 平地为主，很少滞空
- 配置: 高重力阈值，短滞空时间

## 输出结果

### 📊 统计信息
- **总滞空事件数量**: 检测到的所有滞空事件
- **有效滞空事件**: 置信度>0.5的事件数量
- **高质量事件**: 置信度>0.7的事件数量
- **总滞空时间**: 所有有效事件的累计时间
- **平均滞空时间**: 有效事件的平均持续时间
- **最长/最短滞空时间**: 时间范围统计
- **滞空频率**: 每分钟的滞空次数
- **滞空占比**: 滞空时间占总时间的百分比

### 📋 详细事件信息

每个滞空事件包含以下信息：

#### 时间信息
- **开始时间戳**: `YYYY-MM-DD HH:MM:SS.mmm` (精确到毫秒)
- **结束时间戳**: `YYYY-MM-DD HH:MM:SS.mmm` (使用传感器真实毫秒值)
- **持续时间**: 精确到毫秒的滞空时长

#### 物理参数
- **滞空高度**: 基于高度传感器h字段计算，单位米
- **最大加速度**: 滞空期间的最大总加速度值
- **Z轴加速度变化**: 垂直方向的加速度变化模式

#### 质量评估
- **置信度评分**: 0-1的可信度评分
- **事件等级**: 优秀(≥0.9) / 良好(≥0.8) / 中等(≥0.7) / 一般(≥0.5)

### 📄 输出格式示例

#### 控制台输出
```
🎿 优化的滑雪滞空检测器测试
============================================================
检测到 1 个滞空事件

📊 检测结果:
总检测事件: 1 次
有效事件 (置信度>0.5): 1 次
总滞空时间: 0.80 秒
有效滞空时间: 0.80 秒

🏆 高质量滞空事件 (置信度≥0.85): 1 次

详细列表:
序号 开始时间戳              结束时间戳              持续时间(s)  滞空高度(m)  置信度
-------------------------------------------------------------------------------------------
1    <USER> <GROUP>:54:52.020 2025-02-18 19:54:53.010 0.797        0.400        0.952
```

#### CSV导出格式
```csv
序号,开始时间戳,结束时间戳,持续时间(秒),滞空高度(米),最大加速度(mg),置信度,等级
1,2025-02-18 19:54:52.020,2025-02-18 19:54:53.010,0.797,0.400,1245.6,0.952,优秀
```

#### JSON导出格式
```json
{
  "metadata": {
    "export_time": "2025-01-08T10:30:00",
    "total_events": 1,
    "description": "滑雪滞空事件时间戳数据"
  },
  "events": [
    {
      "id": 1,
      "timestamps": {
        "start": {
          "formatted": "2025-02-18 19:54:52.020",
          "unix": 1708261492.020,
          "unix_ms": 1708261492020
        },
        "end": {
          "formatted": "2025-02-18 19:54:53.010",
          "unix": 1708261493.010,
          "unix_ms": 1708261493010
        }
      },
      "duration": {
        "seconds": 0.797,
        "milliseconds": 797
      },
      "metrics": {
        "airtime_height_meters": 0.400,
        "max_acceleration_mg": 1245.6,
        "confidence": 0.952
      }
    }
  ]
}
```

## 性能优化建议

1. **数据预处理**: 对于大文件，建议先进行数据清洗和采样
2. **参数调优**: 根据实际滑雪场景调整检测参数
3. **批量处理**: 对于多个文件，使用批量处理模式
4. **内存管理**: 处理大文件时注意内存使用

## 常见问题

### Q: 为什么检测到的滞空事件很少？
A: 可能是检测阈值设置过高，尝试降低 `gravity_threshold` 和 `angular_velocity_threshold`

### Q: 检测到很多误报怎么办？
A: 可以提高阈值或增加 `min_airtime_ms` 来过滤短时间的噪声事件

### Q: 算法是如何判断滞空的？
A: 主要基于三个核心条件：
1. **Z轴重力异常**: 垂直方向加速度偏离正常重力值
2. **失重状态检测**: Z轴加速度接近0（自由落体特征）
3. **运动模式分析**: 结合角速度变化和高度起伏
需要满足其中一个主要条件，并通过时间窗口和一致性验证。

### Q: 为什么使用Z轴而不是总加速度？
A: 因为：
- 重力主要作用在垂直方向（Z轴）
- X、Y轴包含水平运动（转弯、侧向力），容易造成误判
- Z轴能更准确地反映失重和重力变化特征

### Q: 滞空高度是如何计算的？
A: 使用传感器数据中的h字段：
- h字段 = 海拔高度(米) × 10
- 滞空高度 = (滞空期间max(h) - min(h)) / 10
- 这比使用坐标差值更准确，反映真实的高度变化

### Q: 置信度是如何计算的？
A: 综合多个因素：
- **持续时间评分**(40%): 200ms-3000ms为最佳范围
- **Z轴模式评分**(30%): 失重状态、加速度突变等
- **角速度评分**(30%): 旋转动作的强度和变化
最终置信度 = 各项评分的加权平均

### Q: 如何提高检测精度？
A:
1. **参数调优**: 根据滑雪类型选择合适的场景配置
2. **置信度过滤**: 使用≥0.7的高置信度事件
3. **传感器校准**: 确保传感器数据准确
4. **数据质量**: 检查数据完整性和采样率
5. **视频验证**: 对比实际视频验证检测结果

### Q: 不同滑雪类型如何选择参数？
A:
- **自由式滑雪**: 降低阈值，增加敏感度（更多空中动作）
- **高山滑雪**: 使用默认参数（平衡精度和稳定性）
- **单板滑雪**: 注重角速度检测（更多旋转动作）
- **越野滑雪**: 提高阈值，减少误判（很少滞空）

### Q: 检测结果包含哪些信息？
A: 每个滞空事件包含：
- **精确时间戳**: 开始/结束时间（精确到毫秒）
- **持续时间**: 滞空时长（秒和毫秒）
- **滞空高度**: 基于高度传感器的真实高度变化
- **置信度评分**: 0-1的可信度评估
- **物理参数**: 最大加速度、角速度变化等

### Q: 支持实时检测吗？
A: 当前版本主要用于离线分析，实时检测需要对算法进行流式处理改造

### Q: 如何处理数据解析失败？
A:
1. 检查数据格式是否匹配（支持多种格式）
2. 确认文件编码为UTF-8
3. 查看解析失败的具体行数和错误信息
4. 使用debug工具分析数据格式差异

## 扩展功能

- [ ] 实时滞空检测
- [ ] 图形化界面
- [ ] 更多传感器支持
- [ ] 机器学习优化
- [ ] 云端数据分析

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
