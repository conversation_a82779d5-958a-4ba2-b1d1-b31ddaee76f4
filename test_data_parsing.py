#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析测试工具
测试不同数据格式的解析能力
"""

import re
from datetime import datetime


def test_parse_new_format():
    """测试新数据格式的解析"""
    
    # 新格式示例
    sample_line = "[2025-07-02 19:11:32 016] x: 5, y: 2, z: -10, P: 4, ax: -70, ay: -398, az: 1266, gx: -6463, gy: -2972, gz: -5511, mx: -740, my: 105, mz: -12719, t: 4, h: 1787, s: 0, fs: (null), as:0.000000, lo: 113.2251216812952, la: 23.43101086901214, gh: 0, ca:(null), cs:(null), bt:10"
    
    print("🔍 测试新数据格式解析")
    print("=" * 60)
    print(f"样本数据: {sample_line[:80]}...")
    
    # 新的正则表达式
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
    
    match = re.match(pattern, sample_line.strip())
    
    if match:
        print("✅ 解析成功！")
        groups = match.groups()
        print(f"提取的字段数量: {len(groups)}")
        
        print(f"\n📊 解析结果:")
        field_names = [
            'timestamp', 'milliseconds', 'x', 'y', 'z', 'P',
            'ax', 'ay', 'az', 'gx', 'gy', 'gz',
            'mx', 'my', 'mz', 't', 'h', 's', 'fs', 'as',
            'longitude', 'latitude', 'gh'
        ]
        
        for i, (name, value) in enumerate(zip(field_names, groups)):
            print(f"  {name}: {value}")
            
        return True
    else:
        print("❌ 解析失败！")
        
        # 尝试分析哪里不匹配
        print("\n🔍 调试信息:")
        
        # 简化的模式测试
        simple_patterns = [
            r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\]',  # 时间戳部分
            r'x: (-?\d+), y: (-?\d+), z: (-?\d+)',  # 坐标部分
            r'ax: (-?\d+), ay: (-?\d+), az: (-?\d+)',  # 加速度部分
            r'fs: (\(null\)|-?\d+)',  # fs字段
            r'as:(-?\d+\.?\d*)',  # as字段
        ]
        
        for i, pattern in enumerate(simple_patterns):
            if re.search(pattern, sample_line):
                print(f"  ✅ 模式 {i+1} 匹配")
            else:
                print(f"  ❌ 模式 {i+1} 不匹配: {pattern}")
        
        return False


def test_parse_old_format():
    """测试旧数据格式的解析"""
    
    # 旧格式示例
    sample_line = "[2025-03-30 15:35:08 574] x: 2, y: 1, z: 60, P: 1, ax: -46, ay: 16, az: 883, gx: 347, gy: 122, gz: -12, mx: (null), my: (null), mz: (null), t: 12, h: 23101, s: 0, fs: 0, lo: 90.05966188574553, la: 47.18950562619312, gh: 0, ca:(null), cs:(null), bt:0"
    
    print("\n🔍 测试旧数据格式解析")
    print("=" * 60)
    print(f"样本数据: {sample_line[:80]}...")
    
    # 旧的正则表达式（不包含as字段）
    old_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
    
    match = re.match(old_pattern, sample_line.strip())
    
    if match:
        print("✅ 旧格式解析成功！")
        return True
    else:
        print("❌ 旧格式解析失败！")
        return False


def create_universal_parser():
    """创建通用解析器"""
    
    print("\n🛠️ 创建通用解析器")
    print("=" * 60)
    
    def parse_sensor_line(line):
        """通用传感器数据解析函数"""
        
        # 尝试新格式（包含as字段）
        new_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
        
        match = re.match(new_pattern, line.strip())
        if match:
            return {'format': 'new', 'groups': match.groups()}
        
        # 尝试旧格式（不包含as字段）
        old_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
        
        match = re.match(old_pattern, line.strip())
        if match:
            return {'format': 'old', 'groups': match.groups()}
        
        return None
    
    return parse_sensor_line


def test_files():
    """测试实际文件"""
    
    print("\n📁 测试实际文件")
    print("=" * 60)
    
    parser = create_universal_parser()
    
    # 测试文件列表
    test_files = ['0.txt', '20250330153942712068-济洲平花.txt']
    
    for filename in test_files:
        print(f"\n测试文件: {filename}")
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:5]  # 只测试前5行
                
            success_count = 0
            for i, line in enumerate(lines, 1):
                if line.strip():
                    result = parser(line)
                    if result:
                        print(f"  第{i}行: ✅ {result['format']}格式")
                        success_count += 1
                    else:
                        print(f"  第{i}行: ❌ 解析失败")
                        print(f"    内容: {line[:60]}...")
            
            print(f"  成功率: {success_count}/{len(lines)}")
            
        except FileNotFoundError:
            print(f"  ❌ 文件不存在: {filename}")
        except Exception as e:
            print(f"  ❌ 读取错误: {e}")


def main():
    """主函数"""
    print("🔧 数据解析测试工具")
    print("=" * 60)
    
    # 测试新格式
    new_success = test_parse_new_format()
    
    # 测试旧格式
    old_success = test_parse_old_format()
    
    # 测试实际文件
    test_files()
    
    print(f"\n📋 总结:")
    print(f"新格式解析: {'✅' if new_success else '❌'}")
    print(f"旧格式解析: {'✅' if old_success else '❌'}")
    
    if new_success and old_success:
        print(f"💡 建议: 使用通用解析器支持两种格式")
    else:
        print(f"⚠️  需要修复解析正则表达式")


if __name__ == "__main__":
    main()
