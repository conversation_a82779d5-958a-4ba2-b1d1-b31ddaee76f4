#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳调试工具
验证滞空事件的时间戳计算是否正确
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from datetime import datetime


def debug_timestamp_calculation():
    """调试时间戳计算"""
    print("🔍 时间戳计算调试工具")
    print("=" * 60)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间范围: {sensor_data[0].timestamp} - {sensor_data[-1].timestamp}")
    
    # 显示前几个数据点的时间戳信息
    print(f"\n📅 前5个数据点的时间戳信息:")
    print(f"{'序号':<4} {'Python时间戳':<23} {'毫秒字段':<8} {'组合时间戳':<23}")
    print("-" * 70)
    
    for i, data in enumerate(sensor_data[:5], 1):
        combined_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
        print(f"{i:<4} {data.timestamp.strftime('%Y-%m-%d %H:%M:%S'):<23} {data.milliseconds:<8} {combined_timestamp:<23}")
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    if not airtime_events:
        print("\n❌ 未检测到滞空事件")
        return
    
    print(f"\n🎿 检测到 {len(airtime_events)} 个滞空事件")
    
    # 详细分析每个事件的时间戳
    for i, event in enumerate(airtime_events, 1):
        print(f"\n📋 事件 {i} 详细分析:")
        print(f"   事件开始时间: {event.start_time}")
        print(f"   事件结束时间: {event.end_time}")
        print(f"   持续时间(ms): {event.duration_ms}")
        print(f"   持续时间(s): {event.duration_seconds:.3f}")
        
        # 查找对应的传感器数据点
        start_data = None
        end_data = None
        
        for data in sensor_data:
            if data.timestamp == event.start_time:
                start_data = data
            if data.timestamp == event.end_time:
                end_data = data
        
        if start_data and end_data:
            print(f"   开始数据点毫秒: {start_data.milliseconds}")
            print(f"   结束数据点毫秒: {end_data.milliseconds}")
            
            # 手动计算持续时间验证
            time_diff = (end_data.timestamp - start_data.timestamp).total_seconds() * 1000
            ms_diff = end_data.milliseconds - start_data.milliseconds
            calculated_duration = int(time_diff + ms_diff)
            
            print(f"   手动计算:")
            print(f"     时间差(ms): {time_diff}")
            print(f"     毫秒差: {ms_diff}")
            print(f"     总持续时间: {calculated_duration}ms")
            print(f"   事件存储的持续时间: {event.duration_ms}ms")
            print(f"   计算是否一致: {'✅' if calculated_duration == event.duration_ms else '❌'}")
            
            # 生成正确的时间戳
            start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
            end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
            
            print(f"   正确的开始时间戳: {start_timestamp}")
            print(f"   正确的结束时间戳: {end_timestamp}")
            
        else:
            print(f"   ⚠️  警告: 找不到对应的传感器数据点")
            if not start_data:
                print(f"      缺少开始时间对应的数据点")
            if not end_data:
                print(f"      缺少结束时间对应的数据点")


def test_timestamp_methods():
    """测试不同的时间戳获取方法"""
    print(f"\n🧪 测试时间戳获取方法")
    print("=" * 60)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        return
    
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    if not airtime_events:
        return
    
    event = airtime_events[0]  # 测试第一个事件
    
    print(f"测试事件:")
    print(f"   开始时间: {event.start_time}")
    print(f"   结束时间: {event.end_time}")
    print(f"   持续时间: {event.duration_ms}ms")
    
    # 方法1：原来的方法（可能有问题）
    try:
        start_old = detector._get_real_timestamp(sensor_data, event.start_time)
        end_old = detector._get_real_timestamp(sensor_data, event.end_time)
        print(f"\n方法1 (原方法):")
        print(f"   开始时间戳: {start_old}")
        print(f"   结束时间戳: {end_old}")
        print(f"   是否相同: {'❌ 相同' if start_old == end_old else '✅ 不同'}")
    except:
        print(f"\n方法1 (原方法): ❌ 方法不存在")
    
    # 方法2：新方法
    start_new, end_new = detector._get_event_timestamps(sensor_data, event)
    print(f"\n方法2 (新方法):")
    print(f"   开始时间戳: {start_new}")
    print(f"   结束时间戳: {end_new}")
    print(f"   是否相同: {'❌ 相同' if start_new == end_new else '✅ 不同'}")
    
    # 验证时间戳的合理性
    if start_new != end_new:
        try:
            start_dt = datetime.strptime(start_new, '%Y-%m-%d %H:%M:%S.%f')
            end_dt = datetime.strptime(end_new, '%Y-%m-%d %H:%M:%S.%f')
            timestamp_duration = (end_dt - start_dt).total_seconds() * 1000
            
            print(f"\n时间戳验证:")
            print(f"   时间戳计算的持续时间: {timestamp_duration:.0f}ms")
            print(f"   事件存储的持续时间: {event.duration_ms}ms")
            print(f"   差异: {abs(timestamp_duration - event.duration_ms):.0f}ms")
            print(f"   是否合理: {'✅' if abs(timestamp_duration - event.duration_ms) < 100 else '❌'}")
        except Exception as e:
            print(f"   时间戳解析错误: {e}")


def main():
    """主函数"""
    debug_timestamp_calculation()
    test_timestamp_methods()
    
    print(f"\n💡 总结:")
    print(f"   如果开始和结束时间戳相同，说明时间戳获取方法有问题")
    print(f"   正确的时间戳应该反映真实的滞空持续时间")
    print(f"   建议使用精确匹配的方法获取时间戳")


if __name__ == "__main__":
    main()
