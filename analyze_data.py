#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动检测和分析滑雪数据文件的工具脚本
"""

import os
import sys
from typing import List
from skiing_airtime_calculator import SkiingAirtimeCalculator
from config import get_config_for_scenario


def find_data_files(directory: str = '.') -> List[str]:
    """查找目录中的传感器数据文件"""
    import glob
    
    # 常见的数据文件模式
    patterns = [
        '*.txt',
        '*judy*.txt', 
        '*新疆*.txt',
        '*sensor*.txt',
        '*data*.txt',
        '*滑雪*.txt'
    ]
    
    data_files = []
    for pattern in patterns:
        files = glob.glob(os.path.join(directory, pattern))
        # 过滤掉一些明显不是数据文件的文件
        for file in files:
            filename = os.path.basename(file).lower()
            if not any(skip in filename for skip in ['readme', 'config', 'example', 'test']):
                data_files.append(file)
    
    # 去重并按文件大小排序（大文件可能包含更多数据）
    data_files = list(set(data_files))
    data_files.sort(key=lambda x: os.path.getsize(x), reverse=True)
    
    return data_files


def analyze_file(filename: str, scenario: str = 'alpine') -> dict:
    """分析单个数据文件"""
    print(f"\n{'='*60}")
    print(f"分析文件: {filename}")
    print(f"文件大小: {os.path.getsize(filename)/1024:.1f} KB")
    print(f"{'='*60}")
    
    # 获取配置
    config = get_config_for_scenario(scenario)
    calculator = SkiingAirtimeCalculator(**config)
    
    # 加载数据
    sensor_data = calculator.load_sensor_data(filename)
    
    if not sensor_data:
        print("❌ 无法加载数据或数据为空")
        return {}
    
    # 基本数据信息
    print(f"📊 数据基本信息:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间范围: {sensor_data[0].timestamp} 到 {sensor_data[-1].timestamp}")
    
    total_duration = (sensor_data[-1].timestamp - sensor_data[0].timestamp).total_seconds()
    print(f"   总时长: {total_duration:.1f} 秒 ({total_duration/60:.1f} 分钟)")
    print(f"   平均采样率: {len(sensor_data)/total_duration:.1f} Hz")
    
    # 传感器数据统计
    accelerations = [data.total_acceleration() for data in sensor_data]
    angular_velocities = [data.total_angular_velocity() for data in sensor_data]
    z_positions = [data.z for data in sensor_data]
    
    print(f"\n🔍 传感器数据统计:")
    print(f"   加速度范围: {min(accelerations):.1f} - {max(accelerations):.1f} mg")
    print(f"   平均加速度: {sum(accelerations)/len(accelerations):.1f} mg")
    print(f"   角速度范围: {min(angular_velocities):.1f} - {max(angular_velocities):.1f} 度/秒")
    print(f"   Z轴位置范围: {min(z_positions)} - {max(z_positions)}")
    
    # 检测滞空事件
    print(f"\n🎿 滞空事件检测 (使用 {scenario.upper()} 配置):")
    airtime_events = calculator.detect_airtime_events(sensor_data)
    stats = calculator.analyze_airtime_statistics(airtime_events)
    
    print(f"   检测到滞空事件: {stats['total_events']} 次")
    print(f"   🎯 有效滞空事件 (置信度>0.5): {stats['valid_events']} 次")
    if stats['total_events'] > 0:
        print(f"   总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        print(f"   有效滞空时间: {stats['valid_airtime_seconds']:.2f} 秒")
        print(f"   平均滞空时间: {stats['average_airtime_seconds']:.2f} 秒")
        print(f"   有效平均滞空时间: {stats['valid_average_airtime_seconds']:.2f} 秒")
        print(f"   最长滞空时间: {stats['max_airtime_seconds']:.2f} 秒")
        print(f"   最短滞空时间: {stats['min_airtime_seconds']:.2f} 秒")
        print(f"   高置信度事件 (>0.7): {stats['high_confidence_events']} 次")
        print(f"   有效滞空频率: {stats['valid_events']/total_duration*60:.1f} 次/分钟")
        print(f"   有效滞空占比: {stats['valid_airtime_seconds']/total_duration*100:.1f}%")

        # 显示前3个有效滞空事件
        valid_events = [e for e in airtime_events if e.confidence > 0.5]
        if valid_events:
            print(f"\n   🏆 前3个有效滞空事件 (置信度>0.5):")
            for i, event in enumerate(valid_events[:3], 1):
                print(f"      {i}. {event.start_time.strftime('%H:%M:%S')} - "
                      f"{event.end_time.strftime('%H:%M:%S')}, "
                      f"持续 {event.duration_seconds:.2f}s, "
                      f"置信度 {event.confidence:.2f}")
    
    return {
        'filename': filename,
        'data_points': len(sensor_data),
        'duration_seconds': total_duration,
        'airtime_events': stats['total_events'],
        'valid_airtime_events': stats['valid_events'],
        'total_airtime': stats['total_airtime_seconds'],
        'valid_airtime': stats['valid_airtime_seconds'],
        'high_confidence_events': stats['high_confidence_events']
    }


def compare_scenarios(filename: str) -> None:
    """比较不同场景配置的检测结果"""
    print(f"\n{'='*60}")
    print(f"场景配置对比分析: {os.path.basename(filename)}")
    print(f"{'='*60}")
    
    scenarios = ['freestyle', 'alpine', 'snowboard', 'cross_country']
    results = []
    
    for scenario in scenarios:
        config = get_config_for_scenario(scenario)
        calculator = SkiingAirtimeCalculator(**config)
        
        sensor_data = calculator.load_sensor_data(filename)
        if not sensor_data:
            continue
            
        airtime_events = calculator.detect_airtime_events(sensor_data)
        stats = calculator.analyze_airtime_statistics(airtime_events)
        
        results.append({
            'scenario': scenario,
            'events': stats['total_events'],
            'valid_events': stats['valid_events'],
            'total_time': stats['total_airtime_seconds'],
            'valid_time': stats['valid_airtime_seconds'],
            'high_confidence': stats['high_confidence_events']
        })
    
    # 显示对比表格
    print(f"{'场景':<15} {'总事件':<8} {'有效事件':<8} {'有效时长(s)':<12} {'高置信度':<10}")
    print("-" * 65)
    for result in results:
        print(f"{result['scenario'].upper():<15} {result['events']:<8} "
              f"{result['valid_events']:<8} {result['valid_time']:<12.2f} "
              f"{result['high_confidence']:<10}")


def main():
    """主函数"""
    print("🎿 滑雪滞空数据自动分析工具")
    print("=" * 60)
    
    # 查找数据文件
    print("🔍 正在查找数据文件...")
    data_files = find_data_files()
    
    if not data_files:
        print("❌ 未找到任何数据文件")
        print("请确保当前目录下有 .txt 格式的传感器数据文件")
        return
    
    print(f"✅ 找到 {len(data_files)} 个数据文件:")
    for i, file in enumerate(data_files, 1):
        size_kb = os.path.getsize(file) / 1024
        print(f"   {i}. {os.path.basename(file)} ({size_kb:.1f} KB)")
    
    # 分析每个文件
    all_results = []
    for file in data_files:
        try:
            result = analyze_file(file)
            if result:
                all_results.append(result)
        except Exception as e:
            print(f"❌ 分析文件 {file} 时出错: {e}")
    
    # 如果有多个文件，显示汇总
    if len(all_results) > 1:
        print(f"\n{'='*60}")
        print("📈 所有文件汇总")
        print(f"{'='*60}")
        
        total_events = sum(r['airtime_events'] for r in all_results)
        total_valid_events = sum(r['valid_airtime_events'] for r in all_results)
        total_airtime = sum(r['total_airtime'] for r in all_results)
        total_valid_airtime = sum(r['valid_airtime'] for r in all_results)
        total_duration = sum(r['duration_seconds'] for r in all_results)

        print(f"总文件数: {len(all_results)}")
        print(f"总数据点: {sum(r['data_points'] for r in all_results)}")
        print(f"总记录时长: {total_duration/60:.1f} 分钟")
        print(f"总滞空事件: {total_events} 次")
        print(f"🎯 总有效滞空事件: {total_valid_events} 次")
        print(f"总滞空时间: {total_airtime:.2f} 秒")
        print(f"有效滞空时间: {total_valid_airtime:.2f} 秒")
        print(f"有效滞空频率: {total_valid_events/(total_duration/60):.1f} 次/分钟")
        print(f"有效滞空占比: {total_valid_airtime/total_duration*100:.1f}%")
    
    # 如果只有一个文件，进行场景对比
    if len(data_files) == 1:
        compare_scenarios(data_files[0])
    
    print(f"\n✅ 分析完成！")
    
    # 提供使用建议
    if all_results:
        best_file = max(all_results, key=lambda x: x['valid_airtime_events'])
        print(f"\n💡 使用建议:")
        print(f"   - 有效滞空事件最多的文件: {os.path.basename(best_file['filename'])}")
        print(f"   - 有效滞空事件定义: 置信度 > 0.5")
        print(f"   - 建议根据您的滑雪类型选择合适的场景配置")
        print(f"   - 可以使用 visualizer.py 生成可视化图表")
        print(f"   - 可以调整配置参数来优化检测效果")


if __name__ == "__main__":
    main()
