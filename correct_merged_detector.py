#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全按照原始代码合并的滑雪滞空检测器
不修改任何算法逻辑，只是将三个文件合并
"""

import re
import math
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import numpy as np


@dataclass
class SensorData:
    """传感器数据结构"""
    timestamp: datetime
    milliseconds: int
    # 位置坐标 (可能是相对位置)
    x: float
    y: float  
    z: float
    P: int  # 可能是压力或位置指示器
    
    # 加速度计数据 (mg 单位)
    ax: float  # X轴加速度
    ay: float  # Y轴加速度
    az: float  # Z轴加速度
    
    # 陀螺仪数据 (度/秒)
    gx: float  # X轴角速度
    gy: float  # Y轴角速度
    gz: float  # Z轴角速度
    
    # 磁力计数据
    mx: float  # X轴磁场
    my: float  # Y轴磁场
    mz: float  # Z轴磁场
    
    # 其他传感器数据
    temperature: float  # 温度
    humidity: float     # 湿度
    speed: float        # 速度
    fs: float          # 可能是采样频率
    longitude: float   # 经度
    latitude: float    # 纬度
    gps_height: float  # GPS高度
    
    def total_acceleration(self) -> float:
        """计算总加速度大小"""
        return math.sqrt(self.ax**2 + self.ay**2 + self.az**2)
    
    def total_angular_velocity(self) -> float:
        """计算总角速度大小"""
        return math.sqrt(self.gx**2 + self.gy**2 + self.gz**2)
    
    def acceleration_magnitude_without_gravity(self) -> float:
        """计算去除重力后的加速度大小（假设重力主要在Z轴）"""
        # 假设静止时Z轴加速度约为1000mg（1g）
        gravity_compensated_az = self.az - 1000
        return math.sqrt(self.ax**2 + self.ay**2 + gravity_compensated_az**2)


@dataclass
class AirtimeEvent:
    """滞空事件数据结构"""
    start_time: datetime
    end_time: datetime
    duration_ms: int
    max_height_change: float
    max_acceleration: float
    confidence: float
    max_rotation_speed: float = 0
    avg_rotation_speed: float = 0
    rotation_detected: bool = False
    
    @property
    def duration_seconds(self) -> float:
        """获取持续时间（秒）"""
        return self.duration_ms / 1000.0


class SkiingAirtimeCalculator:
    """滑雪滞空时间计算器基类"""
    
    def __init__(self, 
                 gravity_threshold: float = 800,
                 min_airtime_ms: int = 200,
                 max_airtime_ms: int = 5000,
                 angular_velocity_threshold: float = 1000,
                 confidence_threshold: float = 0.5):
        """
        初始化滞空时间计算器
        
        Args:
            gravity_threshold: 重力异常检测阈值 (mg)
            min_airtime_ms: 最小滞空时间 (毫秒)
            max_airtime_ms: 最大滞空时间 (毫秒)
            angular_velocity_threshold: 角速度阈值 (度/秒)
            confidence_threshold: 置信度阈值
        """
        self.gravity_threshold = gravity_threshold
        self.min_airtime_ms = min_airtime_ms
        self.max_airtime_ms = max_airtime_ms
        self.angular_velocity_threshold = angular_velocity_threshold
        self.confidence_threshold = confidence_threshold
        
        # 物理常数
        self.standard_gravity = 1000  # 标准重力加速度 (mg)
        self.earth_rotation_rate = 15  # 地球自转角速度 (度/小时)
        
        # 滑雪特定参数
        self.typical_jump_height = 2.0  # 典型跳跃高度 (米)
        self.typical_airtime = 1.0      # 典型滞空时间 (秒)
    
    def parse_sensor_line(self, line: str) -> Optional[SensorData]:
        """解析单行传感器数据"""
        try:
            # 处理null值的辅助函数
            def parse_value(value_str, default=0.0):
                if value_str == '(null)':
                    return default
                return float(value_str)

            # 尝试格式1：0.txt格式（不包含fs和as字段）
            pattern_0txt = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'

            match = re.match(pattern_0txt, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=float(groups[12]),
                    my=float(groups[13]),
                    mz=float(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),  # h字段
                    speed=float(groups[17]),
                    fs=0.0,  # 0.txt格式没有fs字段，设为默认值
                    longitude=float(groups[18]),
                    latitude=float(groups[19]),
                    gps_height=float(groups[20])
                )

            # 尝试格式2：包含fs和as字段的格式
            pattern_with_fs_as = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'

            match = re.match(pattern_with_fs_as, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    # groups[19] 是as字段，暂时跳过
                    longitude=float(groups[20]),
                    latitude=float(groups[21]),
                    gps_height=float(groups[22])
                )

            # 尝试旧格式（不包含as字段）
            old_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'

            match = re.match(old_pattern, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    longitude=float(groups[19]),
                    latitude=float(groups[20]),
                    gps_height=float(groups[21])
                )

            # 两种格式都不匹配
            return None

        except (ValueError, IndexError) as e:
            print(f"解析数据行失败: {line[:50]}... 错误: {e}")
            return None
    
    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """从文件加载传感器数据"""
        sensor_data = []
        failed_lines = 0

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        data = self.parse_sensor_line(line)
                        if data:
                            sensor_data.append(data)
                        else:
                            failed_lines += 1
                            if line_num <= 10:  # 只对前10行显示警告
                                print(f"第{line_num}行解析失败: {line[:50]}...")

        except FileNotFoundError:
            print(f"文件未找到: {filename}")
            return []
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return []

        print(f"成功加载 {len(sensor_data)} 条传感器数据")
        if failed_lines > 0:
            print(f"解析失败的行数: {failed_lines}")
        return sensor_data


class OptimizedAirtimeDetector(SkiingAirtimeCalculator):
    """优化的滑雪滞空检测器"""

    def __init__(self,
                 gravity_threshold: float = 600,        # Z轴重力偏差阈值
                 angular_velocity_threshold: float = 800,  # 角速度阈值
                 min_airtime_ms: int = 150,             # 最小滞空时间
                 max_airtime_ms: int = 3000,            # 最大滞空时间
                 weightless_ratio: float = 0.15,       # 失重判断比例（15%重力）
                 z_spike_ratio: float = 0.6,           # Z轴突变判断比例
                 consistency_window: int = 3):          # 一致性检查窗口

        super().__init__(gravity_threshold, min_airtime_ms, max_airtime_ms,
                        angular_velocity_threshold, 0.5)

        self.weightless_ratio = weightless_ratio
        self.z_spike_ratio = z_spike_ratio
        self.consistency_window = consistency_window

    def _smooth_z_acceleration(self, sensor_data: List[SensorData], window_size: int = 3) -> List[float]:
        """平滑Z轴加速度数据"""
        z_values = [data.az for data in sensor_data]

        if len(z_values) < window_size:
            return z_values

        smoothed = []
        for i in range(len(z_values)):
            start = max(0, i - window_size // 2)
            end = min(len(z_values), i + window_size // 2 + 1)
            smoothed.append(sum(z_values[start:end]) / (end - start))

        return smoothed

    def _detect_z_axis_patterns(self, sensor_data: List[SensorData]) -> List[dict]:
        """检测Z轴加速度模式"""
        smoothed_z = self._smooth_z_acceleration(sensor_data)
        patterns = []

        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]

            # 1. 重力偏差检测
            z_gravity_deviation = abs(abs(z_smooth) - self.standard_gravity)
            gravity_anomaly = z_gravity_deviation > self.gravity_threshold

            # 2. 失重状态检测（Z轴加速度很小）
            weightless_threshold = self.standard_gravity * self.weightless_ratio
            is_weightless = abs(z_smooth) < weightless_threshold

            # 3. Z轴突变检测（起跳/落地特征）
            z_spike = False
            if i > 0:
                z_change = abs(z_smooth - smoothed_z[i-1])
                spike_threshold = self.standard_gravity * self.z_spike_ratio
                z_spike = z_change > spike_threshold

            # 4. 角速度检测
            angular_velocity = data.total_angular_velocity()
            high_angular = angular_velocity > self.angular_velocity_threshold

            patterns.append({
                'gravity_anomaly': gravity_anomaly,
                'is_weightless': is_weightless,
                'z_spike': z_spike,
                'high_angular': high_angular,
                'z_deviation': z_gravity_deviation,
                'angular_velocity': angular_velocity,
                'z_value': z_smooth
            })

        return patterns

    def _apply_consistency_filter(self, conditions: List[bool]) -> List[bool]:
        """应用一致性过滤，减少单点噪声"""
        if len(conditions) < self.consistency_window:
            return conditions

        filtered = []
        for i in range(len(conditions)):
            start = max(0, i - self.consistency_window // 2)
            end = min(len(conditions), i + self.consistency_window // 2 + 1)

            window_conditions = conditions[start:end]
            # 窗口内超过60%的点满足条件才认为有效
            true_ratio = sum(window_conditions) / len(window_conditions)
            filtered.append(true_ratio >= 0.6)

        return filtered

    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """优化的滞空事件检测"""
        if len(sensor_data) < 5:
            return []

        print("使用优化的Z轴重力检测算法...")

        # 检测Z轴模式
        patterns = self._detect_z_axis_patterns(sensor_data)

        # 定义多种滞空判断条件
        conditions = []
        for pattern in patterns:
            # 条件1: 重力异常 + 高角速度
            condition1 = pattern['gravity_anomaly'] and pattern['high_angular']

            # 条件2: 失重状态 + 中等角速度
            condition2 = pattern['is_weightless'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.4

            # 条件3: Z轴突变 + 较高角速度（起跳/落地瞬间）
            condition3 = pattern['z_spike'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.6

            # 综合判断
            is_airborne = condition1 or condition2 or condition3
            conditions.append(is_airborne)

        # 应用一致性过滤
        filtered_conditions = self._apply_consistency_filter(conditions)

        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i

            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False

                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])

                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        # 计算事件特征
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        confidence = self._calculate_optimized_confidence(airtime_segment)

                        # 计算旋转指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                        # 创建事件
                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=sensor_data[i].timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected']
                        )

                        # 保存索引信息用于后续处理
                        event.start_data_index = airtime_start_idx
                        event.end_data_index = i

                        airtime_events.append(event)

        print(f"优化算法检测到 {len(airtime_events)} 个滞空事件")
        return airtime_events

    def _calculate_duration_ms(self, start_data: SensorData, end_data: SensorData) -> int:
        """计算两个数据点之间的持续时间（毫秒）"""
        time_diff = (end_data.timestamp - start_data.timestamp).total_seconds()
        ms_diff = end_data.milliseconds - start_data.milliseconds
        return int(time_diff * 1000 + ms_diff)

    def _calculate_max_height_change(self, airtime_segment: List[SensorData]) -> float:
        """计算滞空期间的最大高度变化"""
        # 使用湿度传感器数据作为高度代理，除以10获取真实高度值
        heights = [data.humidity / 10.0 for data in airtime_segment]
        return max(heights) - min(heights)

    def _calculate_optimized_confidence(self, airtime_segment: List[SensorData]) -> float:
        """计算优化的置信度"""
        if not airtime_segment:
            return 0.0

        # 失重状态评分（30%权重）
        weightless_count = 0
        for data in airtime_segment:
            if abs(data.az) < (self.weightless_ratio * self.standard_gravity):
                weightless_count += 1

        weightless_score = min(0.3, (weightless_count / len(airtime_segment)) * 0.3)

        # Z轴突变模式评分（20%权重）
        spike_count = 0
        smoothed_z = self._smooth_z_acceleration(airtime_segment)
        for i in range(1, len(smoothed_z)):
            z_change = abs(smoothed_z[i] - smoothed_z[i-1])
            if z_change > (self.z_spike_ratio * self.standard_gravity):
                spike_count += 1

        if spike_count >= 2:  # 至少2次突变（起跳+落地）
            z_pattern_score = 0.2
        elif spike_count >= 1:
            z_pattern_score = 0.1
        else:
            z_pattern_score = 0.0

        # 重力异常一致性评分（20%权重）
        gravity_anomaly_count = 0
        for data in airtime_segment:
            z_deviation = abs(abs(data.az) - self.standard_gravity)
            if z_deviation > self.gravity_threshold:
                gravity_anomaly_count += 1

        gravity_score = min(0.2, (gravity_anomaly_count / len(airtime_segment)) * 0.2)

        # 角速度一致性评分（30%权重）
        high_angular_count = 0
        for data in airtime_segment:
            if data.total_angular_velocity() > self.angular_velocity_threshold:
                high_angular_count += 1

        angular_score = min(0.3, (high_angular_count / len(airtime_segment)) * 0.3)

        return weightless_score + z_pattern_score + gravity_score + angular_score

    def _calculate_rotation_metrics(self, event_data: List[SensorData]) -> Dict[str, float]:
        """计算旋转相关指标"""
        if not event_data:
            return {'max_rotation_speed': 0, 'avg_rotation_speed': 0, 'rotation_detected': False}

        # 计算Z轴角速度（主要旋转轴）
        z_angular_velocities = [abs(data.gz) for data in event_data]

        max_rotation_speed = max(z_angular_velocities)
        avg_rotation_speed = sum(z_angular_velocities) / len(z_angular_velocities)

        # 判断是否检测到明显旋转
        rotation_detected = avg_rotation_speed > self.angular_velocity_threshold * 0.5

        return {
            'max_rotation_speed': max_rotation_speed,
            'avg_rotation_speed': avg_rotation_speed,
            'rotation_detected': rotation_detected
        }


class ImprovedBoundaryDetector(OptimizedAirtimeDetector):
    """改进的边界检测器"""

    def __init__(self):
        super().__init__()

        # 多级阈值设计
        self.gravity_threshold_strict = 600      # 严格阈值（核心滞空）
        self.gravity_threshold_loose = 300       # 宽松阈值（边界扩展）
        self.angular_threshold_strict = 800      # 严格角速度阈值
        self.angular_threshold_loose = 400       # 宽松角速度阈值

        # 边界扩展参数
        self.pre_jump_window = 8                 # 起跳前检测窗口（数据点）
        self.post_landing_window = 8             # 落地后检测窗口（数据点）
        self.boundary_extension_threshold = 0.3  # 边界扩展评分阈值

        # 渐进式检测参数
        self.gradient_threshold = 200            # 加速度梯度阈值
        self.momentum_threshold = 0.6            # 动量变化阈值

    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空事件检测"""
        if len(sensor_data) < 10:
            return []

        print("🚀 使用改进的边界检测算法...")

        # 第一步：使用原算法检测核心滞空区间
        core_events = super().detect_airtime_events(sensor_data)
        print(f"   核心滞空区间: {len(core_events)} 个")

        if not core_events:
            return []

        # 第二步：对每个核心区间进行边界扩展
        improved_events = []

        for i, event in enumerate(core_events):
            print(f"\n🔍 优化事件 {i+1}:")

            # 获取核心区间的索引
            core_start_idx = getattr(event, 'start_data_index', None)
            core_end_idx = getattr(event, 'end_data_index', None)

            if core_start_idx is None or core_end_idx is None:
                # 如果没有索引信息，尝试通过时间戳查找
                core_start_idx, core_end_idx = self._find_event_indices(sensor_data, event)

            if core_start_idx is None or core_end_idx is None:
                print(f"   ⚠️ 无法找到事件索引，跳过优化")
                improved_events.append(event)
                continue

            print(f"   核心区间: 索引 {core_start_idx} ~ {core_end_idx}")

            # 检测改进的起跳点
            improved_start_idx = self.detect_takeoff_preparation(sensor_data, core_start_idx)
            print(f"   起跳点扩展: {core_start_idx} → {improved_start_idx} (扩展 {core_start_idx - improved_start_idx} 点)")

            # 检测改进的落地点
            improved_end_idx = self.detect_landing_completion(sensor_data, core_end_idx)
            print(f"   落地点扩展: {core_end_idx} → {improved_end_idx} (扩展 {improved_end_idx - core_end_idx} 点)")

            # 创建改进的事件
            improved_event = self._create_improved_event(
                sensor_data, improved_start_idx, improved_end_idx, event)

            improved_events.append(improved_event)

            print(f"   改进后持续时间: {improved_event.duration_seconds:.3f}秒 "
                  f"(原: {event.duration_seconds:.3f}秒)")

        print(f"\n✅ 边界优化完成，共 {len(improved_events)} 个改进事件")

        # 第三步：合并重叠事件
        merged_events = self.merge_overlapping_events(improved_events, sensor_data)
        print(f"🔗 合并重叠事件后，剩余 {len(merged_events)} 个事件")

        return merged_events


# 添加一个简单的main函数来测试
def main():
    """测试改进的边界检测器"""
    print("🎿 改进的滞空边界检测器测试")
    print("=" * 60)

    # 创建改进检测器
    detector = ImprovedBoundaryDetector()

    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')

    if sensor_data:
        print(f"📊 数据信息:")
        print(f"   数据点数量: {len(sensor_data)}")
        print(f"   时间范围: {sensor_data[0].timestamp} ~ {sensor_data[-1].timestamp}")

        # 检测滞空事件
        events = detector.detect_airtime_events(sensor_data)

        # 过滤高置信度事件
        high_confidence_events = [event for event in events if event.confidence >= 0.85]

        if not high_confidence_events:
            print("未检测到高置信度滞空事件")
            print(f"检测到 {len(events)} 个滞空事件，但置信度都低于0.85")
            if events:
                print("显示所有检测到的事件:")
                # 显示所有事件，不管置信度
                high_confidence_events = events

        if high_confidence_events:
            # 输出表格
            print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'置信度':<6}")
            print("-" * 80)

            for i, event in enumerate(high_confidence_events, 1):
                start_time_short = event.start_time.strftime('%H:%M:%S.%f')[:-3]
                end_time_short = event.end_time.strftime('%H:%M:%S.%f')[:-3]

                print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
                      f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
                      f"{event.confidence:<6.3f}")


if __name__ == "__main__":
    main()
