#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滑雪滞空计算算法使用示例
演示如何使用不同的配置和功能
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator
from config import get_config_for_scenario, create_custom_config
from visualizer import AirtimeVisualizer


def example_basic_usage():
    """示例1: 基本使用"""
    print("=" * 50)
    print("示例1: 基本使用")
    print("=" * 50)
    
    # 创建计算器（使用默认参数）
    calculator = SkiingAirtimeCalculator()
    
    # 加载数据
    sensor_data = calculator.load_sensor_data('20250330131632725205-红下 judy.txt')
    
    if sensor_data:
        # 检测滞空事件
        airtime_events = calculator.detect_airtime_events(sensor_data)
        
        # 分析统计数据
        stats = calculator.analyze_airtime_statistics(airtime_events)
        
        # 输出结果
        print(f"检测到 {stats['total_events']} 次滞空事件")
        print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        print(f"平均滞空时间: {stats['average_airtime_seconds']:.2f} 秒")
        print(f"高置信度事件: {stats['high_confidence_events']} 次")
    
    print()


def example_scenario_configs():
    """示例2: 使用不同场景的预设配置"""
    print("=" * 50)
    print("示例2: 不同滑雪场景配置对比")
    print("=" * 50)
    
    scenarios = ['freestyle', 'alpine', 'snowboard', 'cross_country']
    
    for scenario in scenarios:
        print(f"\n--- {scenario.upper()} 场景 ---")
        
        # 获取场景配置
        config = get_config_for_scenario(scenario)
        calculator = SkiingAirtimeCalculator(**config)
        
        # 加载数据
        sensor_data = calculator.load_sensor_data('20250330131632725205-红下 judy.txt')
        
        if sensor_data:
            # 检测滞空事件
            airtime_events = calculator.detect_airtime_events(sensor_data)
            stats = calculator.analyze_airtime_statistics(airtime_events)
            
            print(f"配置参数: {config}")
            print(f"检测到事件: {stats['total_events']} 次")
            print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
            print(f"高置信度事件: {stats['high_confidence_events']} 次")
    
    print()


def example_custom_config():
    """示例3: 自定义配置"""
    print("=" * 50)
    print("示例3: 自定义配置")
    print("=" * 50)
    
    # 创建自定义配置
    custom_config = create_custom_config(
        gravity_threshold=600,      # 更敏感的重力阈值
        min_airtime_ms=150,         # 稍长的最小滞空时间
        max_airtime_ms=3000,        # 较短的最大滞空时间
        angular_velocity_threshold=800,  # 更敏感的角速度阈值
        height_change_threshold=0.8      # 高度变化阈值
    )
    
    print(f"自定义配置: {custom_config}")
    
    # 使用自定义配置
    calculator = SkiingAirtimeCalculator(**custom_config)
    
    # 加载数据
    sensor_data = calculator.load_sensor_data('新疆.txt')
    
    if sensor_data:
        # 检测滞空事件
        airtime_events = calculator.detect_airtime_events(sensor_data)
        stats = calculator.analyze_airtime_statistics(airtime_events)
        
        print(f"检测到事件: {stats['total_events']} 次")
        print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        print(f"平均滞空时间: {stats['average_airtime_seconds']:.2f} 秒")
        print(f"高置信度事件: {stats['high_confidence_events']} 次")
        
        # 显示前5个高置信度事件
        high_confidence_events = [e for e in airtime_events if e.confidence > 0.7]
        if high_confidence_events:
            print(f"\n前5个高置信度事件:")
            for i, event in enumerate(high_confidence_events[:5], 1):
                print(f"  {i}. {event.start_time.strftime('%H:%M:%S')} - "
                      f"{event.end_time.strftime('%H:%M:%S')}, "
                      f"持续 {event.duration_seconds:.2f}s, "
                      f"置信度 {event.confidence:.2f}")
    
    print()


def example_filtering_events():
    """示例4: 事件过滤和分析"""
    print("=" * 50)
    print("示例4: 事件过滤和分析")
    print("=" * 50)
    
    calculator = SkiingAirtimeCalculator()
    sensor_data = calculator.load_sensor_data('新疆.txt')
    
    if sensor_data:
        airtime_events = calculator.detect_airtime_events(sensor_data)
        
        # 按不同条件过滤事件
        all_events = airtime_events
        long_events = [e for e in airtime_events if e.duration_seconds > 1.0]
        high_confidence_events = [e for e in airtime_events if e.confidence > 0.8]
        high_acceleration_events = [e for e in airtime_events if e.max_acceleration > 1500]
        
        print(f"所有事件: {len(all_events)} 次")
        print(f"长时间事件 (>1秒): {len(long_events)} 次")
        print(f"高置信度事件 (>0.8): {len(high_confidence_events)} 次")
        print(f"高加速度事件 (>1500mg): {len(high_acceleration_events)} 次")
        
        # 分析最长的事件
        if all_events:
            longest_event = max(all_events, key=lambda x: x.duration_seconds)
            print(f"\n最长滞空事件:")
            print(f"  时间: {longest_event.start_time.strftime('%H:%M:%S')} - "
                  f"{longest_event.end_time.strftime('%H:%M:%S')}")
            print(f"  持续时间: {longest_event.duration_seconds:.2f} 秒")
            print(f"  最大加速度: {longest_event.max_acceleration:.1f} mg")
            print(f"  高度变化: {longest_event.max_height_change:.1f}")
            print(f"  置信度: {longest_event.confidence:.2f}")
        
        # 分析最高置信度的事件
        if all_events:
            most_confident_event = max(all_events, key=lambda x: x.confidence)
            print(f"\n最高置信度事件:")
            print(f"  时间: {most_confident_event.start_time.strftime('%H:%M:%S')} - "
                  f"{most_confident_event.end_time.strftime('%H:%M:%S')}")
            print(f"  持续时间: {most_confident_event.duration_seconds:.2f} 秒")
            print(f"  置信度: {most_confident_event.confidence:.2f}")
    
    print()


def example_data_analysis():
    """示例5: 深入数据分析"""
    print("=" * 50)
    print("示例5: 深入数据分析")
    print("=" * 50)
    
    calculator = SkiingAirtimeCalculator()
    sensor_data = calculator.load_sensor_data('新疆.txt')
    
    if sensor_data:
        print(f"传感器数据分析:")
        print(f"  数据点数量: {len(sensor_data)}")
        print(f"  时间跨度: {sensor_data[-1].timestamp - sensor_data[0].timestamp}")
        
        # 计算传感器数据统计
        accelerations = [data.total_acceleration() for data in sensor_data]
        angular_velocities = [data.total_angular_velocity() for data in sensor_data]
        z_positions = [data.z for data in sensor_data]
        
        print(f"  加速度范围: {min(accelerations):.1f} - {max(accelerations):.1f} mg")
        print(f"  平均加速度: {sum(accelerations)/len(accelerations):.1f} mg")
        print(f"  角速度范围: {min(angular_velocities):.1f} - {max(angular_velocities):.1f} 度/秒")
        print(f"  Z轴位置范围: {min(z_positions)} - {max(z_positions)}")
        
        # 检测滞空事件
        airtime_events = calculator.detect_airtime_events(sensor_data)
        
        if airtime_events:
            # 时间分布分析
            durations = [e.duration_seconds for e in airtime_events]
            confidences = [e.confidence for e in airtime_events]
            
            print(f"\n滞空事件分析:")
            print(f"  事件数量: {len(airtime_events)}")
            print(f"  持续时间分布:")
            print(f"    < 0.5秒: {len([d for d in durations if d < 0.5])} 次")
            print(f"    0.5-1秒: {len([d for d in durations if 0.5 <= d < 1.0])} 次")
            print(f"    1-2秒: {len([d for d in durations if 1.0 <= d < 2.0])} 次")
            print(f"    > 2秒: {len([d for d in durations if d >= 2.0])} 次")
            
            print(f"  置信度分布:")
            print(f"    < 0.5: {len([c for c in confidences if c < 0.5])} 次")
            print(f"    0.5-0.7: {len([c for c in confidences if 0.5 <= c < 0.7])} 次")
            print(f"    0.7-0.9: {len([c for c in confidences if 0.7 <= c < 0.9])} 次")
            print(f"    > 0.9: {len([c for c in confidences if c >= 0.9])} 次")
    
    print()


def example_with_visualization():
    """示例6: 带可视化的完整分析"""
    print("=" * 50)
    print("示例6: 带可视化的完整分析")
    print("=" * 50)
    
    # 创建计算器和可视化器
    calculator = SkiingAirtimeCalculator()
    visualizer = AirtimeVisualizer(output_dir='example_output')
    
    # 加载数据
    sensor_data = calculator.load_sensor_data('新疆.txt')
    
    if sensor_data:
        # 检测滞空事件
        airtime_events = calculator.detect_airtime_events(sensor_data)
        stats = calculator.analyze_airtime_statistics(airtime_events)
        
        print(f"分析结果:")
        print(f"  检测到 {stats['total_events']} 次滞空事件")
        print(f"  总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        
        # 生成可视化（注释掉以避免在无GUI环境中出错）
        # print("正在生成可视化图表...")
        # visualizer.plot_sensor_data_overview(sensor_data, airtime_events)
        # visualizer.plot_airtime_events(airtime_events)
        
        # 生成报告
        visualizer.generate_report(sensor_data, airtime_events, stats)
        print("详细报告已生成")
    
    print()


def main():
    """运行所有示例"""
    print("滑雪滞空计算算法使用示例")
    print("=" * 60)
    
    # 运行各个示例
    example_basic_usage()
    example_scenario_configs()
    example_custom_config()
    example_filtering_events()
    example_data_analysis()
    example_with_visualization()
    
    print("所有示例运行完成！")
    print("\n使用建议:")
    print("1. 根据您的滑雪类型选择合适的场景配置")
    print("2. 根据实际情况调整检测参数")
    print("3. 使用置信度过滤低质量事件")
    print("4. 结合可视化工具分析结果")


if __name__ == "__main__":
    main()
