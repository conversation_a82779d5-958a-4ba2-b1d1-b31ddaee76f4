#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的滞空判断过程分析
逐步展示算法如何判断滞空事件
"""

from ultimate_airtime_detector import UltimateAirtimeDetector
from improved_airtime_detector_v2 import AirtimeScore
import numpy as np


def analyze_detection_process():
    """详细分析滞空检测过程"""
    print("🔍 详细滞空判断过程分析")
    print("=" * 100)
    
    detector = UltimateAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间跨度: {sensor_data[0].timestamp} ~ {sensor_data[-1].timestamp}")
    print(f"   采样频率: 约 {len(sensor_data) / ((sensor_data[-1].timestamp - sensor_data[0].timestamp).total_seconds()):.1f} Hz")
    
    # 第1步：数据平滑
    print(f"\n🔧 第1步：数据平滑处理")
    print("-" * 50)
    
    smoothed_data = detector.smooth_data(sensor_data, detector.window_size)
    print(f"   滑动窗口大小: {detector.window_size} 个数据点")
    print(f"   平滑前数据点: {len(sensor_data)}")
    print(f"   平滑后数据点: {len(smoothed_data)}")
    
    # 显示平滑效果示例
    print(f"\n   平滑效果示例 (前5个数据点):")
    print(f"   {'索引':<4} {'原始az':<8} {'平滑az':<8} {'原始gx':<8} {'平滑gx':<8}")
    for i in range(5):
        print(f"   {i:<4} {sensor_data[i].az:<8.0f} {smoothed_data[i].az:<8.0f} "
              f"{sensor_data[i].gx:<8.0f} {smoothed_data[i].gx:<8.0f}")
    
    # 第2步：计算评分
    print(f"\n📊 第2步：计算滞空评分")
    print("-" * 50)
    
    scores = detector.calculate_airtime_scores(smoothed_data)
    print(f"   重力权重: {detector.gravity_weight}")
    print(f"   角速度权重: {detector.angular_weight}")
    print(f"   高度权重: {detector.height_weight}")
    print(f"   综合评分阈值: {detector.airtime_score_threshold}")
    
    # 显示评分计算示例
    print(f"\n   评分计算示例 (前10个数据点):")
    print(f"   {'索引':<4} {'重力偏差':<8} {'重力评分':<8} {'角速度':<8} {'角速度评分':<8} {'综合评分':<8} {'是否滞空':<8}")
    print(f"   {'-'*4} {'-'*8} {'-'*8} {'-'*8} {'-'*8} {'-'*8} {'-'*8}")
    
    for i in range(10):
        data = smoothed_data[i]
        score = scores[i]
        gravity_dev = abs(abs(data.az) - detector.standard_gravity)
        angular_vel = data.total_angular_velocity()
        
        print(f"   {i:<4} {gravity_dev:<8.0f} {score.gravity_score:<8.2f} "
              f"{angular_vel:<8.0f} {score.angular_score:<8.2f} "
              f"{score.combined_score:<8.2f} {'是' if score.is_airborne else '否':<8}")
    
    # 第3步：找到候选区间
    print(f"\n🎯 第3步：寻找滞空候选区间")
    print("-" * 50)
    
    candidates = detector.find_airtime_candidates(scores)
    print(f"   最小持续数据点: {detector.min_duration_points}")
    print(f"   找到候选区间: {len(candidates)} 个")
    
    for i, (start, end) in enumerate(candidates, 1):
        duration_points = end - start + 1
        start_time = smoothed_data[start].timestamp.strftime('%H:%M:%S.%f')[:-3]
        end_time = smoothed_data[end].timestamp.strftime('%H:%M:%S.%f')[:-3]
        print(f"   候选区间{i}: 索引{start}-{end} ({duration_points}点), 时间{start_time}-{end_time}")
    
    # 第4步：合并过近区间
    print(f"\n🔗 第4步：合并过近的候选区间")
    print("-" * 50)
    
    merged_candidates = detector.merge_close_candidates(candidates)
    print(f"   合并间隔阈值: {detector.merge_gap_points} 个数据点")
    print(f"   合并前区间数: {len(candidates)}")
    print(f"   合并后区间数: {len(merged_candidates)}")
    
    for i, (start, end) in enumerate(merged_candidates, 1):
        duration_points = end - start + 1
        start_time = smoothed_data[start].timestamp.strftime('%H:%M:%S.%f')[:-3]
        end_time = smoothed_data[end].timestamp.strftime('%H:%M:%S.%f')[:-3]
        print(f"   合并区间{i}: 索引{start}-{end} ({duration_points}点), 时间{start_time}-{end_time}")
    
    # 第5步：边界优化
    print(f"\n🎯 第5步：边界优化")
    print("-" * 50)
    
    optimized_candidates = detector.optimize_boundaries(merged_candidates, smoothed_data)
    print(f"   边界优化前: {len(merged_candidates)} 个区间")
    print(f"   边界优化后: {len(optimized_candidates)} 个区间")
    
    for i, (start, end) in enumerate(optimized_candidates, 1):
        duration_points = end - start + 1
        start_time = smoothed_data[start].timestamp.strftime('%H:%M:%S.%f')[:-3]
        end_time = smoothed_data[end].timestamp.strftime('%H:%M:%S.%f')[:-3]
        duration_ms = detector._calculate_duration_ms(smoothed_data[start], smoothed_data[end])
        print(f"   优化区间{i}: 索引{start}-{end} ({duration_points}点), 时间{start_time}-{end_time}, 持续{duration_ms}ms")
    
    # 第6步：创建事件和验证
    print(f"\n✅ 第6步：创建事件和质量验证")
    print("-" * 50)
    
    events = []
    for i, (start_idx, end_idx) in enumerate(optimized_candidates, 1):
        start_data = smoothed_data[start_idx]
        end_data = smoothed_data[end_idx]
        duration_ms = detector._calculate_duration_ms(start_data, end_data)
        
        print(f"\n   事件{i}验证:")
        print(f"     持续时间: {duration_ms}ms")
        print(f"     时间范围检查: {detector.min_airtime_ms}ms ≤ {duration_ms}ms ≤ {detector.max_airtime_ms}ms")
        
        if detector.min_airtime_ms <= duration_ms <= detector.max_airtime_ms:
            print(f"     ✅ 通过时间范围检查")
            
            # 计算特征
            airtime_segment = smoothed_data[start_idx:end_idx+1]
            max_height_change = detector._calculate_max_height_change(airtime_segment)
            max_acceleration = max(d.total_acceleration() for d in airtime_segment)
            
            print(f"     最大高度变化: {max_height_change:.3f}m")
            print(f"     最大加速度: {max_acceleration:.0f}mg")
            
            # 计算置信度
            segment_scores = scores[start_idx:end_idx+1]
            avg_score = sum(s.combined_score for s in segment_scores) / len(segment_scores)
            confidence = min(1.0, avg_score * 1.2)
            
            print(f"     平均评分: {avg_score:.3f}")
            print(f"     置信度: {confidence:.3f}")
            
            # 计算转动信息
            yaw_angles = [data.z for data in airtime_segment]
            cumulative_rotation = 0.0
            for j in range(1, len(yaw_angles)):
                angle_change = yaw_angles[j] - yaw_angles[j-1]
                if angle_change > 180:
                    angle_change -= 360
                elif angle_change < -180:
                    angle_change += 360
                cumulative_rotation += angle_change
            
            rotation_angle = abs(cumulative_rotation)
            rotation_speed = rotation_angle / 360.0 / (duration_ms/1000.0)
            
            print(f"     转动角度: {rotation_angle:.1f}°")
            print(f"     转动速度: {rotation_speed:.2f}圈/s")
            print(f"     转动方向: {'顺时针' if cumulative_rotation > 50 else '逆时针' if cumulative_rotation < -50 else '无明显转动'}")
            
            print(f"     ✅ 事件创建成功")
            events.append({
                'index': i,
                'start_idx': start_idx,
                'end_idx': end_idx,
                'duration_ms': duration_ms,
                'confidence': confidence,
                'height': max_height_change,
                'rotation_angle': rotation_angle,
                'rotation_speed': rotation_speed
            })
        else:
            print(f"     ❌ 未通过时间范围检查")
    
    # 第7步：后处理验证
    print(f"\n🔍 第7步：后处理和物理验证")
    print("-" * 50)
    
    final_events = []
    for event in events:
        print(f"\n   事件{event['index']}物理验证:")
        
        # 持续时间验证
        duration_sec = event['duration_ms'] / 1000.0
        duration_valid = 0.1 <= duration_sec <= 3.0
        print(f"     持续时间验证: {duration_sec:.3f}s (0.1-3.0s) - {'✅' if duration_valid else '❌'}")
        
        # 高度验证
        height_valid = 0.05 <= event['height'] <= 5.0
        print(f"     高度验证: {event['height']:.3f}m (0.05-5.0m) - {'✅' if height_valid else '❌'}")
        
        # 转速验证
        speed_valid = event['rotation_speed'] <= 200
        print(f"     转速验证: {event['rotation_speed']:.2f}圈/s (≤200圈/s) - {'✅' if speed_valid else '❌'}")
        
        if duration_valid and height_valid and speed_valid:
            print(f"     ✅ 通过所有物理验证")
            final_events.append(event)
        else:
            print(f"     ❌ 未通过物理验证")
    
    # 最终结果
    print(f"\n🏆 最终检测结果")
    print("=" * 100)
    print(f"   初始数据点: {len(sensor_data)}")
    print(f"   候选区间: {len(candidates)}")
    print(f"   合并后区间: {len(merged_candidates)}")
    print(f"   优化后区间: {len(optimized_candidates)}")
    print(f"   创建的事件: {len(events)}")
    print(f"   最终有效事件: {len(final_events)}")
    
    if final_events:
        print(f"\n   最终事件详情:")
        for event in final_events:
            start_time = smoothed_data[event['start_idx']].timestamp.strftime('%H:%M:%S.%f')[:-3]
            end_time = smoothed_data[event['end_idx']].timestamp.strftime('%H:%M:%S.%f')[:-3]
            print(f"     事件{event['index']}: {start_time}-{end_time}, "
                  f"{event['duration_ms']/1000:.3f}s, {event['height']:.2f}m, "
                  f"置信度{event['confidence']:.3f}, {event['rotation_angle']:.1f}°, "
                  f"{event['rotation_speed']:.2f}圈/s")


def main():
    """主函数"""
    analyze_detection_process()


if __name__ == "__main__":
    main()
