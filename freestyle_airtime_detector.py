#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自由式滑雪专用滞空检测器
针对自由式滑雪的特点进行专门优化，提升滞空识别准确率
"""

import numpy as np
from typing import List, <PERSON><PERSON>
from skiing_airtime_calculator import SkiingAirtimeCalculator, SensorData, AirtimeEvent
from datetime import datetime, timedelta


class FreestyleAirtimeDetector(SkiingAirtimeCalculator):
    """自由式滑雪专用滞空检测器"""
    
    def __init__(self):
        # 自由式滑雪优化参数（更敏感的设置）
        super().__init__(
            gravity_threshold=300,          # 更敏感的重力阈值
            angular_velocity_threshold=400, # 更低的角速度阈值
            min_airtime_ms=80,             # 更短的最小滞空时间
            max_airtime_ms=10000,          # 更长的最大滞空时间
            height_change_threshold=0.2    # 更敏感的高度变化阈值
        )
        
        # 自由式滑雪专用参数
        self.pre_jump_detection_window = 5      # 起跳前检测窗口
        self.landing_detection_window = 5       # 落地后检测窗口
        self.rotation_intensity_threshold = 1500  # 旋转强度阈值
        self.flip_detection_threshold = 2000    # 翻转检测阈值
        self.air_stability_threshold = 0.3      # 空中稳定性阈值
        self.continuity_window = 2              # 连续性检查窗口
        self.min_continuous_points = 2          # 最少连续满足条件的点数
        
    def _detect_pre_jump_pattern(self, data_window: List[SensorData]) -> float:
        """检测起跳前的动作模式"""
        if len(data_window) < 3:
            return 0.0
        
        # 分析起跳前的加速度变化模式
        z_accelerations = [data.az for data in data_window]
        
        # 检测压缩-释放模式（蹲下-起跳）
        compression_score = 0.0
        for i in range(1, len(z_accelerations)):
            if z_accelerations[i] > z_accelerations[i-1] + 200:  # 向上加速度增加
                compression_score += 0.3
        
        # 检测角速度预备动作
        angular_velocities = [data.total_angular_velocity() for data in data_window]
        if max(angular_velocities) > 300:  # 预备旋转
            compression_score += 0.2
        
        return min(compression_score, 1.0)
    
    def _detect_landing_pattern(self, data_window: List[SensorData]) -> float:
        """检测落地模式"""
        if len(data_window) < 3:
            return 0.0
        
        # 分析落地时的冲击模式
        z_accelerations = [data.az for data in data_window]
        
        # 检测冲击-缓冲模式
        impact_score = 0.0
        max_impact = max(abs(az) for az in z_accelerations)
        
        if max_impact > 1500:  # 强烈冲击
            impact_score += 0.4
        elif max_impact > 1000:  # 中等冲击
            impact_score += 0.2
        
        # 检测冲击后的稳定化
        if len(z_accelerations) >= 3:
            later_variance = np.var(z_accelerations[-3:])
            if later_variance < 50000:  # 落地后趋于稳定
                impact_score += 0.3
        
        return min(impact_score, 1.0)
    
    def _analyze_air_tricks(self, airtime_segment: List[SensorData]) -> dict:
        """分析空中技巧动作"""
        if len(airtime_segment) < 3:
            return {'rotation_score': 0, 'flip_score': 0, 'stability_score': 0}
        
        # 提取角速度数据
        gx_values = [data.gx for data in airtime_segment]
        gy_values = [data.gy for data in airtime_segment]
        gz_values = [data.gz for data in airtime_segment]
        
        # 旋转分析（主要看Z轴旋转）
        gz_max = max(abs(gz) for gz in gz_values)
        rotation_score = min(gz_max / self.rotation_intensity_threshold, 1.0)
        
        # 翻转分析（主要看X、Y轴旋转）
        gx_max = max(abs(gx) for gx in gx_values)
        gy_max = max(abs(gy) for gy in gy_values)
        flip_intensity = max(gx_max, gy_max)
        flip_score = min(flip_intensity / self.flip_detection_threshold, 1.0)
        
        # 空中稳定性分析
        total_angular_velocities = [data.total_angular_velocity() for data in airtime_segment]
        angular_variance = np.var(total_angular_velocities)
        stability_score = 1.0 - min(angular_variance / 1000000, 1.0)
        
        return {
            'rotation_score': rotation_score,
            'flip_score': flip_score,
            'stability_score': stability_score,
            'max_rotation': gz_max,
            'max_flip': flip_intensity
        }
    
    def _calculate_freestyle_confidence(self, airtime_segment: List[SensorData], 
                                      duration_ms: int, pre_jump_data: List[SensorData],
                                      landing_data: List[SensorData]) -> float:
        """计算自由式滑雪专用置信度"""
        
        # 基础置信度
        base_confidence = self._calculate_confidence(airtime_segment, duration_ms)
        
        # 自由式特征评分
        freestyle_score = 0.0
        
        # 1. 起跳模式评分 (20%)
        pre_jump_score = self._detect_pre_jump_pattern(pre_jump_data)
        freestyle_score += pre_jump_score * 0.2
        
        # 2. 落地模式评分 (20%)
        landing_score = self._detect_landing_pattern(landing_data)
        freestyle_score += landing_score * 0.2
        
        # 3. 空中技巧评分 (30%)
        trick_analysis = self._analyze_air_tricks(airtime_segment)
        trick_score = (trick_analysis['rotation_score'] * 0.4 + 
                      trick_analysis['flip_score'] * 0.4 + 
                      trick_analysis['stability_score'] * 0.2)
        freestyle_score += trick_score * 0.3
        
        # 4. 持续时间适应性评分 (15%)
        duration_seconds = duration_ms / 1000.0
        if 0.2 <= duration_seconds <= 4.0:  # 自由式滑雪的理想滞空时间
            duration_score = 1.0
        elif duration_seconds < 0.2:
            duration_score = duration_seconds / 0.2
        else:
            duration_score = max(0, 1.0 - (duration_seconds - 4.0) / 4.0)
        freestyle_score += duration_score * 0.15
        
        # 5. 高度变化评分 (15%)
        height_change = self._calculate_max_height_change(airtime_segment)
        if height_change > 0.5:  # 明显的高度变化
            height_score = min(height_change / 3.0, 1.0)
        else:
            height_score = height_change / 0.5 * 0.5
        freestyle_score += height_score * 0.15
        
        # 综合置信度 (基础60% + 自由式特征40%)
        final_confidence = base_confidence * 0.6 + freestyle_score * 0.4
        
        return min(final_confidence, 1.0)
    
    def _check_continuity(self, sensor_data: List[SensorData], start_idx: int, window_size: int) -> bool:
        """检查连续性：确保有足够的连续点满足滞空条件"""
        if start_idx + window_size > len(sensor_data):
            return False

        continuous_count = 0
        for i in range(start_idx, min(start_idx + window_size, len(sensor_data))):
            data = sensor_data[i]

            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()

            # 检测Z轴突变
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)

            # 判断是否满足滞空条件
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold) or
                (z_weightless and angular_velocity > 100) or
                (z_spike and angular_velocity > 200) or
                (angular_velocity > self.angular_velocity_threshold) or
                (z_gravity_deviation > 200 and angular_velocity > 300)
            )

            if is_airborne:
                continuous_count += 1

        return continuous_count >= self.min_continuous_points

    def detect_freestyle_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """自由式滑雪专用滞空事件检测"""
        if len(sensor_data) < 10:
            return []

        print("🎿 使用自由式滑雪专用检测算法...")

        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0
        continuous_airtime_count = 0  # 连续滞空计数

        for i, data in enumerate(sensor_data):
            # 自由式滑雪的滞空判断条件（更敏感）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)  # 更宽松的失重判断
            angular_velocity = data.total_angular_velocity()
            
            # 检测Z轴突变（起跳/落地特征）
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)  # 更敏感的突变检测
            
            # 自由式滑雪的综合判断（更宽松的条件）
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold) or  # 重力偏差
                (z_weightless and angular_velocity > 100) or       # 失重+轻微旋转
                (z_spike and angular_velocity > 200) or           # 突变+中等角速度
                (angular_velocity > self.angular_velocity_threshold) or  # 角速度超过阈值
                (z_gravity_deviation > 200 and angular_velocity > 300)   # 中等重力偏差+角速度
            )
            
            if is_airborne and not in_airtime:
                # 检查连续性：确保不是单点噪声
                if self._check_continuity(sensor_data, i, self.continuity_window):
                    in_airtime = True
                    airtime_start = data
                    airtime_start_idx = i
                    continuous_airtime_count = 1
                    print(f"🛫 滞空开始: {data.timestamp.strftime('%H:%M:%S.%f')[:-3]}")

            elif is_airborne and in_airtime:
                # 继续滞空
                continuous_airtime_count += 1

            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                print(f"🛬 滞空结束: {data.timestamp.strftime('%H:%M:%S.%f')[:-3]}, 连续点数: {continuous_airtime_count}")

                if airtime_start and continuous_airtime_count >= self.min_continuous_points:
                    duration_ms = self._calculate_duration_ms(airtime_start, data)
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        
                        # 获取起跳前和落地后的数据
                        pre_jump_start = max(0, airtime_start_idx - self.pre_jump_detection_window)
                        pre_jump_data = sensor_data[pre_jump_start:airtime_start_idx]
                        
                        landing_end = min(len(sensor_data), i + self.landing_detection_window)
                        landing_data = sensor_data[i:landing_end]
                        
                        # 计算滞空特征
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        
                        # 自由式专用置信度计算
                        confidence = self._calculate_freestyle_confidence(
                            airtime_segment, duration_ms, pre_jump_data, landing_data)
                        
                        # 分析空中技巧
                        trick_analysis = self._analyze_air_tricks(airtime_segment)

                        # 计算旋转相关指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=data.timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected']
                        )
                        
                        airtime_events.append(event)
                        
                        print(f"🎪 自由式滞空: {airtime_start.timestamp.strftime('%H:%M:%S')} - "
                              f"{data.timestamp.strftime('%H:%M:%S')}, "
                              f"持续{duration_ms}ms, 高度{max_height_change:.2f}m, "
                              f"置信度{confidence:.3f}, 旋转{trick_analysis['max_rotation']:.0f}°/s")
                
                airtime_start = None
                airtime_start_idx = 0
                continuous_airtime_count = 0

        # 处理文件结束时仍在滞空的情况
        if in_airtime and airtime_start and continuous_airtime_count >= self.min_continuous_points:
            print(f"🛬 文件结束时仍在滞空，强制结束")
            last_data = sensor_data[-1]
            duration_ms = self._calculate_duration_ms(airtime_start, last_data)

            if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                airtime_segment = sensor_data[airtime_start_idx:]

                # 获取起跳前数据
                pre_jump_start = max(0, airtime_start_idx - self.pre_jump_detection_window)
                pre_jump_data = sensor_data[pre_jump_start:airtime_start_idx]
                landing_data = []  # 没有落地数据

                max_height_change = self._calculate_max_height_change(airtime_segment)
                max_acceleration = max(d.total_acceleration() for d in airtime_segment)

                confidence = self._calculate_freestyle_confidence(
                    airtime_segment, duration_ms, pre_jump_data, landing_data)

                trick_analysis = self._analyze_air_tricks(airtime_segment)

                # 计算旋转相关指标
                rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                event = AirtimeEvent(
                    start_time=airtime_start.timestamp,
                    end_time=last_data.timestamp,
                    duration_ms=duration_ms,
                    max_height_change=max_height_change,
                    max_acceleration=max_acceleration,
                    confidence=confidence,
                    max_rotation_speed=rotation_metrics['max_rotation_speed'],
                    avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                    rotation_detected=rotation_metrics['rotation_detected']
                )

                airtime_events.append(event)

                print(f"🎪 自由式滞空: {airtime_start.timestamp.strftime('%H:%M:%S')} - "
                      f"{last_data.timestamp.strftime('%H:%M:%S')}, "
                      f"持续{duration_ms}ms, 高度{max_height_change:.2f}m, "
                      f"置信度{confidence:.3f}, 旋转{trick_analysis['max_rotation']:.0f}°/s")
        
        print(f"🎿 自由式检测完成，发现 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def get_freestyle_quality_events(self, airtime_events: List[AirtimeEvent],
                                   min_confidence: float = 0.6) -> List[AirtimeEvent]:
        """获取高质量的自由式滞空事件"""
        return [e for e in airtime_events if e.confidence >= min_confidence]

    def analyze_sensor_data_characteristics(self, sensor_data: List[SensorData]) -> dict:
        """分析传感器数据特征，帮助调试"""
        if not sensor_data:
            return {}

        # 提取各种数据
        z_accelerations = [abs(data.az) for data in sensor_data]
        z_gravity_deviations = [abs(abs(data.az) - self.standard_gravity) for data in sensor_data]
        angular_velocities = [data.total_angular_velocity() for data in sensor_data]
        height_values = [data.humidity for data in sensor_data]

        # 统计分析
        analysis = {
            'data_points': len(sensor_data),
            'z_acceleration_range': (min(z_accelerations), max(z_accelerations)),
            'z_gravity_deviation_range': (min(z_gravity_deviations), max(z_gravity_deviations)),
            'angular_velocity_range': (min(angular_velocities), max(angular_velocities)),
            'height_range': (min(height_values), max(height_values)),
            'max_z_deviation': max(z_gravity_deviations),
            'max_angular_velocity': max(angular_velocities),
            'height_change': max(height_values) - min(height_values)
        }

        # 检查是否满足各种条件
        gravity_triggers = sum(1 for dev in z_gravity_deviations if dev > self.gravity_threshold)
        angular_triggers = sum(1 for av in angular_velocities if av > self.angular_velocity_threshold)
        weightless_points = sum(1 for az in z_accelerations if az < self.standard_gravity * 0.25)

        analysis.update({
            'gravity_threshold_triggers': gravity_triggers,
            'angular_threshold_triggers': angular_triggers,
            'weightless_points': weightless_points,
            'trigger_rate': (gravity_triggers + angular_triggers) / len(sensor_data) * 100
        })

        return analysis

    def _get_real_timestamp(self, sensor_data: List[SensorData], target_time) -> str:
        """获取真实的毫秒时间戳"""
        # 找到最接近目标时间的传感器数据点
        closest_data = None
        min_diff = float('inf')

        for data in sensor_data:
            diff = abs((data.timestamp - target_time).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_data = data

        if closest_data:
            # 使用传感器数据中的真实毫秒值
            return f"{closest_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{closest_data.milliseconds:03d}"
        else:
            # 如果找不到，使用默认格式
            return target_time.strftime('%Y-%m-%d %H:%M:%S.000')


def main():
    """测试自由式滑雪检测器"""
    print("🎪 自由式滑雪专用滞空检测器")
    print("=" * 60)
    
    # 创建自由式检测器
    detector = FreestyleAirtimeDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('0.txt')
    
    if sensor_data:
        # 分析数据特征
        print(f"\n🔍 数据特征分析:")
        analysis = detector.analyze_sensor_data_characteristics(sensor_data)
        print(f"   数据点数量: {analysis['data_points']}")
        print(f"   Z轴加速度范围: {analysis['z_acceleration_range'][0]:.0f} - {analysis['z_acceleration_range'][1]:.0f} mg")
        print(f"   重力偏差范围: {analysis['z_gravity_deviation_range'][0]:.0f} - {analysis['z_gravity_deviation_range'][1]:.0f} mg")
        print(f"   角速度范围: {analysis['angular_velocity_range'][0]:.0f} - {analysis['angular_velocity_range'][1]:.0f} °/s")
        print(f"   高度变化: {analysis['height_change']:.1f}")
        print(f"   最大重力偏差: {analysis['max_z_deviation']:.0f} mg (阈值: {detector.gravity_threshold} mg)")
        print(f"   最大角速度: {analysis['max_angular_velocity']:.0f} °/s (阈值: {detector.angular_velocity_threshold} °/s)")
        print(f"   重力阈值触发点: {analysis['gravity_threshold_triggers']} / {analysis['data_points']}")
        print(f"   角速度阈值触发点: {analysis['angular_threshold_triggers']} / {analysis['data_points']}")
        print(f"   失重状态点: {analysis['weightless_points']} / {analysis['data_points']}")
        print(f"   触发率: {analysis['trigger_rate']:.1f}%")

        # 使用自由式专用检测
        freestyle_events = detector.detect_freestyle_airtime_events(sensor_data)
        
        # 分析结果
        stats = detector.analyze_airtime_statistics(freestyle_events)
        
        print(f"\n📊 自由式滑雪检测结果:")
        print(f"总检测事件: {stats['total_events']} 次")
        print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
        print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        print(f"有效滞空时间: {stats['valid_airtime_seconds']:.2f} 秒")
        
        # 获取高质量事件
        quality_events = detector.get_freestyle_quality_events(freestyle_events, 0.6)
        
        print(f"\n🏆 高质量自由式滞空事件 (置信度≥0.6): {len(quality_events)} 次")
        
        if quality_events:
            print(f"\n详细列表:")
            print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'旋转':<6} {'最大转速(°/s)':<12}")
            print("-" * 115)
            for i, event in enumerate(quality_events, 1):
                # 获取精确的开始和结束时间戳（直接查找匹配的数据点）
                start_timestamp = None
                end_timestamp = None

                # 查找开始时间对应的传感器数据
                for data in sensor_data:
                    if data.timestamp == event.start_time:
                        start_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                        break

                # 查找结束时间对应的传感器数据
                for data in sensor_data:
                    if data.timestamp == event.end_time:
                        end_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                        break

                # 如果找不到精确匹配，使用事件时间本身
                if not start_timestamp:
                    start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
                if not end_timestamp:
                    end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')

                rotation_indicator = "🌀" if event.rotation_detected else "—"
                print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                      f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                      f"{event.confidence:<8.3f} {rotation_indicator:<6} {event.max_rotation_speed:<12.0f}")
        
        print(f"\n💡 自由式滑雪优化特点:")
        print(f"   ✅ 更敏感的重力阈值 (400mg)")
        print(f"   ✅ 更低的角速度阈值 (600°/s)")
        print(f"   ✅ 更短的最小滞空时间 (100ms)")
        print(f"   ✅ 起跳前动作模式检测")
        print(f"   ✅ 落地冲击模式分析")
        print(f"   ✅ 空中技巧动作识别")
        print(f"   ✅ 自由式专用置信度算法")


if __name__ == "__main__":
    main()
