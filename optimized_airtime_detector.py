#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的滑雪滞空检测器
基于Z轴重力分析和物理原理的精确检测
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator, SensorData, AirtimeEvent
from typing import List
import numpy as np


class OptimizedAirtimeDetector(SkiingAirtimeCalculator):
    """优化的滑雪滞空检测器"""
    
    def __init__(self, 
                 gravity_threshold: float = 600,        # Z轴重力偏差阈值
                 angular_velocity_threshold: float = 800,  # 角速度阈值
                 min_airtime_ms: int = 150,             # 最小滞空时间
                 max_airtime_ms: int = 3000,            # 最大滞空时间
                 weightless_ratio: float = 0.15,       # 失重判断比例（15%重力）
                 z_spike_ratio: float = 0.6,           # Z轴突变判断比例
                 consistency_window: int = 3):          # 一致性检查窗口
        
        super().__init__(gravity_threshold, min_airtime_ms, max_airtime_ms, 
                        angular_velocity_threshold, 0.5)
        
        self.weightless_ratio = weightless_ratio
        self.z_spike_ratio = z_spike_ratio
        self.consistency_window = consistency_window
    
    def _smooth_z_acceleration(self, sensor_data: List[SensorData], window_size: int = 3) -> List[float]:
        """平滑Z轴加速度数据"""
        z_values = [data.az for data in sensor_data]
        
        if len(z_values) < window_size:
            return z_values
        
        smoothed = []
        for i in range(len(z_values)):
            start = max(0, i - window_size // 2)
            end = min(len(z_values), i + window_size // 2 + 1)
            smoothed.append(sum(z_values[start:end]) / (end - start))
        
        return smoothed
    
    def _detect_z_axis_patterns(self, sensor_data: List[SensorData]) -> List[dict]:
        """检测Z轴加速度模式"""
        smoothed_z = self._smooth_z_acceleration(sensor_data)
        patterns = []
        
        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]
            
            # 1. 重力偏差检测
            z_gravity_deviation = abs(abs(z_smooth) - self.standard_gravity)
            gravity_anomaly = z_gravity_deviation > self.gravity_threshold
            
            # 2. 失重状态检测（Z轴加速度很小）
            weightless_threshold = self.standard_gravity * self.weightless_ratio
            is_weightless = abs(z_smooth) < weightless_threshold
            
            # 3. Z轴突变检测（起跳/落地特征）
            z_spike = False
            if i > 0:
                z_change = abs(z_smooth - smoothed_z[i-1])
                spike_threshold = self.standard_gravity * self.z_spike_ratio
                z_spike = z_change > spike_threshold
            
            # 4. 角速度检测
            angular_velocity = data.total_angular_velocity()
            high_angular = angular_velocity > self.angular_velocity_threshold
            
            patterns.append({
                'gravity_anomaly': gravity_anomaly,
                'is_weightless': is_weightless,
                'z_spike': z_spike,
                'high_angular': high_angular,
                'z_deviation': z_gravity_deviation,
                'angular_velocity': angular_velocity,
                'z_value': z_smooth
            })
        
        return patterns
    
    def _apply_consistency_filter(self, conditions: List[bool]) -> List[bool]:
        """应用一致性过滤，减少单点噪声"""
        if len(conditions) < self.consistency_window:
            return conditions
        
        filtered = []
        for i in range(len(conditions)):
            start = max(0, i - self.consistency_window // 2)
            end = min(len(conditions), i + self.consistency_window // 2 + 1)
            
            window_conditions = conditions[start:end]
            # 窗口内超过60%的点满足条件才认为有效
            true_ratio = sum(window_conditions) / len(window_conditions)
            filtered.append(true_ratio >= 0.6)
        
        return filtered
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """优化的滞空事件检测"""
        if len(sensor_data) < 5:
            return []
        
        print("使用优化的Z轴重力检测算法...")
        
        # 检测Z轴模式
        patterns = self._detect_z_axis_patterns(sensor_data)
        
        # 定义多种滞空判断条件
        conditions = []
        for pattern in patterns:
            # 条件1: 重力异常 + 高角速度
            condition1 = pattern['gravity_anomaly'] and pattern['high_angular']
            
            # 条件2: 失重状态 + 中等角速度
            condition2 = pattern['is_weightless'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.4
            
            # 条件3: Z轴突变 + 较高角速度（起跳/落地瞬间）
            condition3 = pattern['z_spike'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.6
            
            # 综合判断
            is_airborne = condition1 or condition2 or condition3
            conditions.append(is_airborne)
        
        # 应用一致性过滤
        filtered_conditions = self._apply_consistency_filter(conditions)
        
        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0
        
        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
                
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        
                        # 计算滞空特征
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        
                        # 增强的置信度计算
                        confidence = self._calculate_enhanced_confidence(
                            airtime_segment, duration_ms, patterns[airtime_start_idx:i+1])

                        # 计算旋转相关指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                        # 创建带有数据点索引信息的事件
                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=sensor_data[i].timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected']
                        )

                        # 添加数据点索引信息（用于精确时间戳）
                        event.start_data_index = airtime_start_idx
                        event.end_data_index = i
                        
                        airtime_events.append(event)
                
                airtime_start = None
                airtime_start_idx = 0
        
        print(f"优化算法检测到 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def _calculate_enhanced_confidence(self, segment: List[SensorData], 
                                     duration_ms: int, patterns: List[dict]) -> float:
        """增强的置信度计算"""
        if not segment or not patterns:
            return 0.0
        
        # 基础置信度
        base_confidence = self._calculate_confidence(segment, duration_ms)
        
        # Z轴模式评分
        z_pattern_score = 0.0
        
        # 1. 失重状态评分
        weightless_count = sum(1 for p in patterns if p['is_weightless'])
        weightless_ratio = weightless_count / len(patterns)
        z_pattern_score += min(0.3, weightless_ratio * 0.5)
        
        # 2. Z轴突变评分
        spike_count = sum(1 for p in patterns if p['z_spike'])
        if spike_count >= 2:  # 起跳和落地
            z_pattern_score += 0.2
        elif spike_count >= 1:
            z_pattern_score += 0.1
        
        # 3. 重力异常评分
        gravity_anomaly_count = sum(1 for p in patterns if p['gravity_anomaly'])
        gravity_ratio = gravity_anomaly_count / len(patterns)
        z_pattern_score += min(0.2, gravity_ratio * 0.4)
        
        # 4. 角速度一致性评分
        high_angular_count = sum(1 for p in patterns if p['high_angular'])
        angular_ratio = high_angular_count / len(patterns)
        z_pattern_score += min(0.3, angular_ratio * 0.6)
        
        # 综合置信度
        final_confidence = (base_confidence + z_pattern_score) / 2
        return min(1.0, final_confidence)
    
    def get_high_quality_events(self, airtime_events: List[AirtimeEvent],
                               min_confidence: float = 0.6) -> List[AirtimeEvent]:
        """获取高质量滞空事件"""
        return [e for e in airtime_events if e.confidence >= min_confidence]

    def _calculate_rotation_metrics(self, airtime_segment: List[SensorData]) -> dict:
        """计算旋转相关指标（使用偏航角方法）"""
        if len(airtime_segment) < 2:
            return {
                'max_rotation_speed': 0.0,
                'avg_rotation_speed': 0.0,
                'rotation_detected': False,
                'rotation_type': 'none',
                'rotation_angle': 0.0
            }

        # 计算总时间
        start_time = airtime_segment[0].timestamp
        end_time = airtime_segment[-1].timestamp
        total_time_seconds = (end_time - start_time).total_seconds()

        # 添加毫秒精度
        start_ms = airtime_segment[0].milliseconds
        end_ms = airtime_segment[-1].milliseconds
        total_time_seconds += (end_ms - start_ms) / 1000.0

        if total_time_seconds <= 0:
            return {
                'max_rotation_speed': 0.0,
                'avg_rotation_speed': 0.0,
                'rotation_detected': False,
                'rotation_type': 'none',
                'rotation_angle': 0.0
            }

        # 使用偏航角（z字段）计算转体角度
        yaw_angles = [data.z for data in airtime_segment]

        # 计算累积旋转角度（处理跨越360°的情况）
        cumulative_rotation = 0.0
        for i in range(1, len(yaw_angles)):
            angle_change = yaw_angles[i] - yaw_angles[i-1]

            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360

            cumulative_rotation += angle_change

        # 转体角度（绝对值）
        abs_z_angle = abs(cumulative_rotation)

        # 只识别转体动作（基于偏航角）
        rotation_type = 'none'
        primary_angle = 0.0
        avg_rotation_speed = 0.0
        rotation_detected = False

        # 基于偏航角的转体判断条件
        min_spin_angle = 50  # 至少50度才算转体（用户设置）

        # 转体判断条件（简化，只基于偏航角）：
        # 1. Z轴角度 > 50° (基本转体角度)
        # 2. 持续时间 > 0.1秒 (基本持续时间)
        is_real_spin = (
            abs_z_angle > min_spin_angle and                           # 条件1: 角度足够大
            total_time_seconds > 0.1                                  # 条件2: 持续时间足够
        )

        if is_real_spin:
            rotation_type = 'spin'      # Z轴旋转（转体）
            primary_angle = abs_z_angle
            rotation_detected = True

            # 只有转体动作才计算旋转速度（转换为圈/秒）
            avg_rotation_speed = (abs_z_angle / 360.0) / total_time_seconds

        # 非转体动作（前后翻、侧翻、或不满足转体条件）转速直接为0

        return {
            'max_rotation_speed': avg_rotation_speed,     # 修改：只有转体才有转速，否则为0
            'avg_rotation_speed': avg_rotation_speed,     # 只有转体动作才计算转速
            'rotation_detected': rotation_detected,       # 只有转体动作才为True
            'rotation_type': rotation_type,              # 只有'spin'或'none'
            'rotation_angle': primary_angle,             # 只有转体角度，其他为0
            'x_angle': 0.0,                              # 不再使用X轴角度
            'y_angle': 0.0,                              # 不再使用Y轴角度
            'z_angle': abs_z_angle,                      # Z轴旋转角度（转体角度）
            'total_time': total_time_seconds             # 总时间
        }

    def _get_exact_timestamp(self, sensor_data: List[SensorData], target_time) -> str:
        """获取精确的时间戳（直接匹配，不使用最接近算法）"""
        # 直接查找完全匹配的时间戳
        for data in sensor_data:
            if data.timestamp == target_time:
                return f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"

        # 如果找不到完全匹配，查找最接近的（但要确保不是同一个点）
        closest_data = None
        min_diff = float('inf')

        for data in sensor_data:
            diff = abs((data.timestamp - target_time).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_data = data

        if closest_data:
            return f"{closest_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{closest_data.milliseconds:03d}"
        else:
            # 如果都找不到，使用目标时间本身
            return target_time.strftime('%Y-%m-%d %H:%M:%S.000')

    def _get_event_timestamps_by_index(self, sensor_data: List[SensorData], event) -> tuple:
        """通过索引获取事件的精确时间戳"""
        # 重新检测滞空事件，记录索引
        in_airtime = False
        airtime_start_idx = None
        airtime_end_idx = None

        for i, data in enumerate(sensor_data):
            # 使用相同的检测逻辑
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()

            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)

            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and
                 angular_velocity > self.angular_velocity_threshold * 0.5) or
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)
            )

            if is_airborne and not in_airtime:
                in_airtime = True
                airtime_start_idx = i
            elif not is_airborne and in_airtime:
                in_airtime = False
                airtime_end_idx = i - 1

                # 检查是否是我们要找的事件
                if (airtime_start_idx is not None and
                    sensor_data[airtime_start_idx].timestamp == event.start_time):
                    break

        # 使用找到的索引获取精确时间戳
        if airtime_start_idx is not None and airtime_end_idx is not None:
            start_data = sensor_data[airtime_start_idx]
            end_data = sensor_data[airtime_end_idx]

            start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
            end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"

            return start_timestamp, end_timestamp

        # 备用方法：直接查找匹配的时间戳
        return self._get_event_timestamps_fallback(sensor_data, event)

    def _get_event_timestamps_fallback(self, sensor_data: List[SensorData], event) -> tuple:
        """备用方法：直接查找匹配的时间戳"""
        start_timestamp = None
        end_timestamp = None

        # 查找开始时间对应的传感器数据
        for data in sensor_data:
            if data.timestamp == event.start_time:
                start_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                break

        # 查找结束时间对应的传感器数据
        for data in sensor_data:
            if data.timestamp == event.end_time:
                end_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                break

        # 如果找不到，使用事件时间本身
        if not start_timestamp:
            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
        if not end_timestamp:
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')

        return start_timestamp, end_timestamp


def main():
    """测试优化的滞空检测器"""
    print("🎿 优化的滑雪滞空检测器测试")
    print("=" * 60)
    
    # 创建优化检测器
    detector = OptimizedAirtimeDetector(
        gravity_threshold=600,      # Z轴重力偏差阈值
        angular_velocity_threshold=700,  # 角速度阈值
        min_airtime_ms=150,         # 最小滞空时间
        max_airtime_ms=3000,        # 最大滞空时间
        weightless_ratio=0.15,      # 失重判断比例
        z_spike_ratio=0.6,          # Z轴突变比例
        consistency_window=3        # 一致性窗口
    )
    
    # 加载数据
    sensor_data = detector.load_sensor_data('0.txt')   #20250330153942712068-济洲平花.txt
    
    if sensor_data:
        # 检测滞空事件
        airtime_events = detector.detect_airtime_events(sensor_data)
        stats = detector.analyze_airtime_statistics(airtime_events)
        
        print(f"\n📊 检测结果:")
        print(f"总检测事件: {stats['total_events']} 次")
        print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
        print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
        print(f"有效滞空时间: {stats['valid_airtime_seconds']:.2f} 秒")
        
        # 获取高质量事件
        high_quality_events = detector.get_high_quality_events(airtime_events, 0.85)
        
        print(f"\n🏆 高质量滞空事件 (置信度≥0.85): {len(high_quality_events)} 次")
        
        if high_quality_events:
            print(f"\n详细列表:")
            print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'转体角度(°)':<12} {'转体速度(圈/s)':<14} {'转动方向':<12}")
            print("-" * 135)
            for i, event in enumerate(high_quality_events, 1):
                # 使用事件中记录的数据点索引获取精确时间戳
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    # 使用记录的索引获取精确时间戳
                    start_data = sensor_data[event.start_data_index]
                    end_data = sensor_data[event.end_data_index]

                    start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
                    end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
                else:
                    # 备用方法：直接查找匹配的数据点
                    start_timestamp = None
                    end_timestamp = None

                    for data in sensor_data:
                        if data.timestamp == event.start_time and not start_timestamp:
                            start_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                        if data.timestamp == event.end_time and not end_timestamp:
                            end_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"

                    if not start_timestamp:
                        start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
                    if not end_timestamp:
                        end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')

                # 获取精确的转动信息（基于偏航角）
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
                    yaw_angles = [data.z for data in airtime_segment]

                    # 计算累积旋转角度
                    cumulative_rotation = 0.0
                    for j in range(1, len(yaw_angles)):
                        angle_change = yaw_angles[j] - yaw_angles[j-1]

                        # 处理角度跨越360°边界的情况
                        if angle_change > 180:
                            angle_change -= 360
                        elif angle_change < -180:
                            angle_change += 360

                        cumulative_rotation += angle_change

                    # 转动角度和速度
                    rotation_angle = abs(cumulative_rotation)
                    rotation_speed = rotation_angle / 360.0 / event.duration_seconds if event.duration_seconds > 0 else 0

                    # 判断转动方向
                    if cumulative_rotation > 50:
                        rotation_direction = 'clockwise'
                    elif cumulative_rotation < -50:
                        rotation_direction = 'counterclockwise'
                    else:
                        rotation_direction = 'none'
                        rotation_angle = 0.0
                        rotation_speed = 0.0
                else:
                    rotation_angle = 0.0
                    rotation_speed = 0.0
                    rotation_direction = 'none'

                print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                      f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                      f"{event.confidence:<8.3f} {rotation_angle:<12.1f} "
                      f"{rotation_speed:<14.2f} {rotation_direction:<12}")
        
        print(f"\n💡 建议:")
        print(f"1. 对比视频验证这些高质量事件的准确性")
        print(f"2. 可以调整 gravity_threshold 和 angular_velocity_threshold")
        print(f"3. 可以调整 weightless_ratio 来改变失重检测敏感度")


if __name__ == "__main__":
    main()
