#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试0.txt文件解析问题
"""

import re
from datetime import datetime


def analyze_0txt_format():
    """分析0.txt文件的格式"""
    print("🔍 分析0.txt文件格式")
    print("=" * 60)
    
    # 读取样本行
    with open('0.txt', 'r', encoding='utf-8') as f:
        sample_line = f.readline().strip()
    
    print(f"样本行: {sample_line}")
    print()
    
    # 分析字段
    # [2025-02-18 19:54:48 029] x: -1, y: 12, z: -50, P: 12, ax: -212, ay: -59, az: -1072, gx: -201, gy: -964, gz: 646, mx: -1284, my: -1042, mz: -4244, t: -8, h: 5417, s: 2, lo: 43.415545, la: 126.621503, gh: 605, ca:(null), cs:(null)
    
    # 手动解析字段
    fields = []
    parts = sample_line.split(', ')
    
    print("字段分析:")
    for i, part in enumerate(parts):
        print(f"  {i+1:2d}. {part}")
        if ':' in part:
            field_name = part.split(':')[0].strip()
            field_value = part.split(':')[1].strip()
            fields.append((field_name, field_value))
    
    print(f"\n识别的字段数量: {len(fields)}")
    
    # 构建正确的正则表达式
    print(f"\n构建正确的正则表达式:")
    
    # 0.txt格式的正确模式
    correct_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'
    
    print(f"正确的模式: {correct_pattern}")
    
    # 测试匹配
    match = re.match(correct_pattern, sample_line)
    
    if match:
        print("✅ 匹配成功！")
        groups = match.groups()
        print(f"提取的组数: {len(groups)}")
        
        field_names = [
            'timestamp', 'milliseconds', 'x', 'y', 'z', 'P',
            'ax', 'ay', 'az', 'gx', 'gy', 'gz',
            'mx', 'my', 'mz', 't', 'h', 's',
            'longitude', 'latitude', 'gh'
        ]
        
        print("\n提取的值:")
        for i, (name, value) in enumerate(zip(field_names, groups)):
            print(f"  {name}: {value}")
            
        return correct_pattern
    else:
        print("❌ 匹配失败！")
        
        # 尝试简化的模式
        simple_patterns = [
            r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\]',  # 时间戳
            r'x: (-?\d+), y: (-?\d+), z: (-?\d+)',  # 坐标
            r'ax: (-?\d+), ay: (-?\d+), az: (-?\d+)',  # 加速度
            r'mx: (-?\d+), my: (-?\d+), mz: (-?\d+)',  # 磁力计
            r't: (-?\d+), h: (-?\d+), s: (-?\d+)',  # 温度、高度、速度
            r'lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)',  # 位置
            r'ca:\(null\), cs:\(null\)'  # 结尾
        ]
        
        print("\n测试各部分:")
        for i, pattern in enumerate(simple_patterns):
            if re.search(pattern, sample_line):
                print(f"  ✅ 部分 {i+1} 匹配")
            else:
                print(f"  ❌ 部分 {i+1} 不匹配: {pattern}")
        
        return None


def create_0txt_parser():
    """创建0.txt专用解析器"""
    print(f"\n🛠️ 创建0.txt专用解析器")
    print("=" * 60)
    
    # 0.txt格式的正确模式
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'
    
    def parse_0txt_line(line):
        match = re.match(pattern, line.strip())
        if match:
            groups = match.groups()
            return {
                'timestamp': datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                'milliseconds': int(groups[1]),
                'x': float(groups[2]),
                'y': float(groups[3]),
                'z': float(groups[4]),
                'P': int(groups[5]),
                'ax': float(groups[6]),
                'ay': float(groups[7]),
                'az': float(groups[8]),
                'gx': float(groups[9]),
                'gy': float(groups[10]),
                'gz': float(groups[11]),
                'mx': float(groups[12]),
                'my': float(groups[13]),
                'mz': float(groups[14]),
                'temperature': float(groups[15]),
                'humidity': float(groups[16]),  # h字段
                'speed': float(groups[17]),
                'longitude': float(groups[18]),
                'latitude': float(groups[19]),
                'gps_height': float(groups[20])
            }
        return None
    
    return parse_0txt_line


def test_0txt_parsing():
    """测试0.txt文件解析"""
    print(f"\n📁 测试0.txt文件解析")
    print("=" * 60)
    
    parser = create_0txt_parser()
    
    try:
        with open('0.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()[:10]  # 测试前10行
        
        success_count = 0
        for i, line in enumerate(lines, 1):
            if line.strip():
                result = parser(line)
                if result:
                    print(f"第{i}行: ✅ 解析成功")
                    if i == 1:  # 显示第一行的详细结果
                        print(f"  时间: {result['timestamp']}")
                        print(f"  高度h: {result['humidity']}")
                        print(f"  加速度: ax={result['ax']}, ay={result['ay']}, az={result['az']}")
                    success_count += 1
                else:
                    print(f"第{i}行: ❌ 解析失败")
                    print(f"  内容: {line[:60]}...")
        
        print(f"\n成功率: {success_count}/{len(lines)} ({success_count/len(lines)*100:.1f}%)")
        
        if success_count == len(lines):
            print("✅ 所有行解析成功！可以更新主解析器")
        else:
            print("⚠️  部分行解析失败，需要进一步调试")
            
    except FileNotFoundError:
        print("❌ 文件0.txt不存在")
    except Exception as e:
        print(f"❌ 读取错误: {e}")


def generate_updated_parser_code():
    """生成更新的解析器代码"""
    print(f"\n📝 生成更新的解析器代码")
    print("=" * 60)
    
    print("需要在skiing_airtime_calculator.py中添加以下模式:")
    print()
    print("# 0.txt格式（不包含fs和as字段）")
    print("pattern_0txt = r'\\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\\.?\d*), la: (-?\d+\\.?\d*), gh: (-?\d+), ca:\\(null\\), cs:\\(null\\)'")
    print()
    print("然后在解析函数中添加对这种格式的支持。")


def main():
    """主函数"""
    print("🔧 0.txt文件解析调试工具")
    print("=" * 60)
    
    # 分析格式
    correct_pattern = analyze_0txt_format()
    
    # 创建解析器
    create_0txt_parser()
    
    # 测试解析
    test_0txt_parsing()
    
    # 生成代码
    generate_updated_parser_code()


if __name__ == "__main__":
    main()
