#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载
"""

import re
import os
from datetime import datetime

# 简单测试数据加载
def test_load():
    filename = '720.txt'
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"错误: 文件 '{filename}' 不存在")
        return
    
    print(f"文件 '{filename}' 存在")
    
    # 读取前几行
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 3:  # 只读前3行
                    break
                print(f"第{i+1}行: {line.strip()}")
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    test_load()
