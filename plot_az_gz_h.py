#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制传感器数据中az、gz、h的变化曲线
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import re
from datetime import datetime
from typing import List, Tuple

# 设置字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


def parse_sensor_data(filename: str) -> Tuple[List[datetime], List[float], List[float], List[float]]:
    """
    解析传感器数据文件，提取时间戳、az、gz、h数据
    
    Returns:
        timestamps: 时间戳列表
        az_values: z轴加速度列表 (原始值，需要除以100获取真实加速度)
        gz_values: z轴角速度列表 (度/秒)
        h_values: 海拔高度列表
    """
    timestamps = []
    az_values = []
    gz_values = []
    h_values = []
    
    # 数据解析正则表达式
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                match = re.match(pattern, line)
                if match:
                    groups = match.groups()
                    
                    # 解析时间戳
                    timestamp = datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S')
                    timestamps.append(timestamp)
                    
                    # 解析传感器数据
                    az = float(groups[8])  # z轴加速度
                    gz = float(groups[11])  # z轴角速度
                    h = float(groups[16])   # 海拔高度
                    
                    az_values.append(az)
                    gz_values.append(gz)
                    h_values.append(h)
                else:
                    print(f"警告: 第{line_num}行数据格式不匹配: {line[:50]}...")
    
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return [], [], [], []
    except Exception as e:
        print(f"错误: 读取文件时发生异常: {e}")
        return [], [], [], []
    
    print(f"成功解析 {len(timestamps)} 条数据记录")
    return timestamps, az_values, gz_values, h_values


def plot_sensor_curves(timestamps: List[datetime], az_values: List[float], 
                      gz_values: List[float], h_values: List[float], 
                      save_path: str = None):
    """
    绘制az、gz、h的变化曲线
    """
    if not timestamps:
        print("没有数据可以绘制")
        return
    
    # 转换az值为真实加速度 (除以100)
    az_real = [az / 100.0 for az in az_values]

    # 转换h值为真实高度 (除以10)
    h_real = [h / 10.0 for h in h_values]
    
    # 创建子图
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle('传感器数据变化曲线 (az, gz, h)', fontsize=16, fontweight='bold')
    
    # 1. Z轴加速度图
    axes[0].plot(timestamps, az_real, 'b-', linewidth=1, alpha=0.8, label='Z轴加速度')
    axes[0].axhline(y=10, color='r', linestyle='--', alpha=0.5, label='标准重力 (+10 m/s²)')
    axes[0].axhline(y=-10, color='r', linestyle='--', alpha=0.5, label='标准重力 (-10 m/s²)')
    axes[0].axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    axes[0].set_ylabel('Z轴加速度 (m/s^2)')
    axes[0].set_title('Z轴加速度变化 (az)')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()

    # 添加统计信息
    az_mean = np.mean(az_real)
    az_std = np.std(az_real)
    axes[0].text(0.02, 0.98, f'均值: {az_mean:.2f} m/s^2\n标准差: {az_std:.2f} m/s^2',
                transform=axes[0].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. Z轴角速度图
    axes[1].plot(timestamps, gz_values, 'g-', linewidth=1, alpha=0.8, label='Z轴角速度')
    axes[1].axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    axes[1].set_ylabel('Z轴角速度 (度/秒)')
    axes[1].set_title('Z轴角速度变化 (gz) - 转体旋转')
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()
    
    # 添加统计信息
    gz_mean = np.mean(gz_values)
    gz_std = np.std(gz_values)
    gz_max = max(abs(min(gz_values)), abs(max(gz_values)))
    axes[1].text(0.02, 0.98, f'均值: {gz_mean:.1f} °/s\n标准差: {gz_std:.1f} °/s\n最大幅值: {gz_max:.1f} °/s', 
                transform=axes[1].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # 3. 海拔高度图
    axes[2].plot(timestamps, h_real, 'm-', linewidth=1, alpha=0.8, label='海拔高度')
    axes[2].set_ylabel('海拔高度 (米)')
    axes[2].set_xlabel('时间')
    axes[2].set_title('海拔高度变化 (h)')
    axes[2].grid(True, alpha=0.3)
    axes[2].legend()

    # 添加统计信息
    h_mean = np.mean(h_real)
    h_range = max(h_real) - min(h_real)
    axes[2].text(0.02, 0.98, f'平均高度: {h_mean:.1f}米\n高度变化: {h_range:.1f}米\n最高: {max(h_real):.1f}米\n最低: {min(h_real):.1f}米',
                transform=axes[2].transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='plum', alpha=0.8))
    
    # 格式化时间轴
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        ax.xaxis.set_major_locator(mdates.SecondLocator(interval=30))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = 'sensor_az_gz_h_curves.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"图表已保存到: {save_path}")


def print_data_summary(timestamps: List[datetime], az_values: List[float], 
                      gz_values: List[float], h_values: List[float]):
    """打印数据摘要信息"""
    if not timestamps:
        return
    
    print("\n" + "="*60)
    print("数据摘要信息")
    print("="*60)
    
    # 基本信息
    print(f"数据点数量: {len(timestamps)}")
    print(f"时间范围: {timestamps[0]} 到 {timestamps[-1]}")
    total_duration = (timestamps[-1] - timestamps[0]).total_seconds()
    print(f"总时长: {total_duration:.1f} 秒 ({total_duration/60:.1f} 分钟)")
    print(f"平均采样率: {len(timestamps)/total_duration:.1f} Hz")
    
    # Z轴加速度统计 (转换为真实值)
    az_real = [az / 100.0 for az in az_values]
    print(f"\nZ轴加速度 (az):")
    print(f"  范围: {min(az_real):.2f} 到 {max(az_real):.2f} m/s²")
    print(f"  均值: {np.mean(az_real):.2f} m/s²")
    print(f"  标准差: {np.std(az_real):.2f} m/s²")
    
    # Z轴角速度统计
    print(f"\nZ轴角速度 (gz):")
    print(f"  范围: {min(gz_values):.1f} 到 {max(gz_values):.1f} 度/秒")
    print(f"  均值: {np.mean(gz_values):.1f} 度/秒")
    print(f"  标准差: {np.std(gz_values):.1f} 度/秒")
    print(f"  最大旋转速度: {max(abs(min(gz_values)), abs(max(gz_values))):.1f} 度/秒")
    
    # 海拔高度统计 (转换为真实值)
    h_real = [h / 10.0 for h in h_values]
    print(f"\n海拔高度 (h):")
    print(f"  范围: {min(h_real):.1f} 到 {max(h_real):.1f} 米")
    print(f"  均值: {np.mean(h_real):.1f} 米")
    print(f"  高度变化: {max(h_real) - min(h_real):.1f} 米")


def main():
    """主函数"""
    filename = '0.txt'
    
    print(f"正在读取数据文件: {filename}")
    timestamps, az_values, gz_values, h_values = parse_sensor_data(filename)
    
    if not timestamps:
        print("没有成功读取到数据")
        return
    
    # 打印数据摘要
    print_data_summary(timestamps, az_values, gz_values, h_values)
    
    # 绘制曲线图
    print("\n正在生成图表...")
    plot_sensor_curves(timestamps, az_values, gz_values, h_values)
    
    print("\n图表生成完成！")


if __name__ == "__main__":
    main()
