#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试水平距离计算
"""

from improved_boundary_detector import ImprovedBoundaryDetector

def main():
    detector = ImprovedBoundaryDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    print(f"加载了 {len(sensor_data)} 条数据")
    
    # 检查前几个数据点的速度属性
    print("\n前5个数据点的速度:")
    for i, data in enumerate(sensor_data[:5]):
        print(f"  数据点{i+1}: speed = {data.speed}")
    
    # 测试水平距离计算
    test_segment = sensor_data[35:90]
    duration = 1.679
    
    try:
        horizontal_distance = detector._calculate_horizontal_distance(test_segment, duration)
        print(f"\n水平距离计算结果: {horizontal_distance}")
    except Exception as e:
        print(f"\n错误: {e}")

if __name__ == "__main__":
    main()
