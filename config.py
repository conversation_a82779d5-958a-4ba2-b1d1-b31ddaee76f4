#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滑雪滞空计算器配置文件
可以根据不同的滑雪场景和传感器特性调整参数
"""

# 基础检测参数
class AirtimeDetectionConfig:
    """滞空检测配置"""
    
    # 重力相关参数
    STANDARD_GRAVITY_MG = 1000          # 标准重力加速度 (毫克)
    GRAVITY_THRESHOLD_MG = 800          # 重力偏差阈值 (毫克)
    
    # 时间相关参数
    MIN_AIRTIME_MS = 100               # 最小滞空时间 (毫秒)
    MAX_AIRTIME_MS = 10000             # 最大滞空时间 (毫秒)
    
    # 角速度相关参数
    ANGULAR_VELOCITY_THRESHOLD = 1000   # 角速度阈值 (度/秒)
    
    # 高度变化相关参数
    HEIGHT_CHANGE_THRESHOLD = 0.5       # 高度变化阈值
    
    # 置信度计算参数
    CONFIDENCE_DURATION_MIN = 200       # 置信度计算的最小持续时间
    CONFIDENCE_DURATION_MAX = 3000      # 置信度计算的最大持续时间
    VALID_CONFIDENCE_THRESHOLD = 0.5    # 有效滞空事件阈值
    HIGH_CONFIDENCE_THRESHOLD = 0.7     # 高置信度事件阈值


# 不同滑雪场景的预设配置
class SkiingScenarios:
    """不同滑雪场景的配置预设"""
    
    # 自由式滑雪 (跳跃较多)
    FREESTYLE = {
        'gravity_threshold': 600,
        'min_airtime_ms': 150,
        'max_airtime_ms': 8000,
        'angular_velocity_threshold': 800,
        'height_change_threshold': 1.0
    }
    
    # 高山滑雪 (速度快，跳跃少)
    ALPINE = {
        'gravity_threshold': 1000,
        'min_airtime_ms': 200,
        'max_airtime_ms': 3000,
        'angular_velocity_threshold': 1200,
        'height_change_threshold': 0.3
    }
    
    # 单板滑雪 (技巧动作多)
    SNOWBOARD = {
        'gravity_threshold': 700,
        'min_airtime_ms': 100,
        'max_airtime_ms': 6000,
        'angular_velocity_threshold': 900,
        'height_change_threshold': 0.8
    }
    
    # 越野滑雪 (平地为主)
    CROSS_COUNTRY = {
        'gravity_threshold': 1200,
        'min_airtime_ms': 300,
        'max_airtime_ms': 2000,
        'angular_velocity_threshold': 1500,
        'height_change_threshold': 0.2
    }


# 传感器校准参数
class SensorCalibration:
    """传感器校准参数"""
    
    # 加速度计校准
    ACCELEROMETER_SCALE = 1.0           # 加速度计缩放因子
    ACCELEROMETER_OFFSET_X = 0          # X轴偏移
    ACCELEROMETER_OFFSET_Y = 0          # Y轴偏移
    ACCELEROMETER_OFFSET_Z = 0          # Z轴偏移
    
    # 陀螺仪校准
    GYROSCOPE_SCALE = 1.0               # 陀螺仪缩放因子
    GYROSCOPE_OFFSET_X = 0              # X轴偏移
    GYROSCOPE_OFFSET_Y = 0              # Y轴偏移
    GYROSCOPE_OFFSET_Z = 0              # Z轴偏移
    
    # 磁力计校准
    MAGNETOMETER_SCALE = 1.0            # 磁力计缩放因子
    MAGNETOMETER_OFFSET_X = 0           # X轴偏移
    MAGNETOMETER_OFFSET_Y = 0           # Y轴偏移
    MAGNETOMETER_OFFSET_Z = 0           # Z轴偏移


# 数据处理参数
class DataProcessingConfig:
    """数据处理配置"""
    
    # 滤波参数
    ENABLE_LOW_PASS_FILTER = True       # 启用低通滤波
    LOW_PASS_CUTOFF_FREQ = 10.0         # 低通滤波截止频率 (Hz)
    
    ENABLE_HIGH_PASS_FILTER = False     # 启用高通滤波
    HIGH_PASS_CUTOFF_FREQ = 0.1         # 高通滤波截止频率 (Hz)
    
    # 数据平滑参数
    ENABLE_SMOOTHING = True             # 启用数据平滑
    SMOOTHING_WINDOW_SIZE = 5           # 平滑窗口大小
    
    # 异常值检测
    ENABLE_OUTLIER_DETECTION = True     # 启用异常值检测
    OUTLIER_THRESHOLD_SIGMA = 3.0       # 异常值检测阈值 (标准差倍数)


# 输出配置
class OutputConfig:
    """输出配置"""
    
    # 报告格式
    REPORT_FORMAT = 'detailed'          # 'simple', 'detailed', 'json'
    
    # 图表输出
    ENABLE_PLOTS = True                 # 启用图表输出
    PLOT_FORMAT = 'png'                 # 图表格式: 'png', 'pdf', 'svg'
    PLOT_DPI = 300                      # 图表分辨率
    
    # 数据导出
    EXPORT_RAW_DATA = False             # 导出原始数据
    EXPORT_PROCESSED_DATA = True        # 导出处理后数据
    EXPORT_EVENTS_DATA = True           # 导出事件数据
    
    # 文件路径
    OUTPUT_DIR = 'output'               # 输出目录
    REPORT_FILENAME = 'airtime_report'  # 报告文件名
    DATA_FILENAME = 'airtime_data'      # 数据文件名


# 调试配置
class DebugConfig:
    """调试配置"""
    
    DEBUG_MODE = False                  # 调试模式
    VERBOSE_OUTPUT = True               # 详细输出
    LOG_LEVEL = 'INFO'                  # 日志级别: 'DEBUG', 'INFO', 'WARNING', 'ERROR'
    
    # 性能监控
    ENABLE_PROFILING = False            # 启用性能分析
    PROFILE_OUTPUT_FILE = 'profile.txt' # 性能分析输出文件


def get_config_for_scenario(scenario: str) -> dict:
    """根据滑雪场景获取配置"""
    scenarios = {
        'freestyle': SkiingScenarios.FREESTYLE,
        'alpine': SkiingScenarios.ALPINE,
        'snowboard': SkiingScenarios.SNOWBOARD,
        'cross_country': SkiingScenarios.CROSS_COUNTRY
    }
    
    return scenarios.get(scenario.lower(), SkiingScenarios.ALPINE)


def create_custom_config(**kwargs) -> dict:
    """创建自定义配置"""
    default_config = {
        'gravity_threshold': AirtimeDetectionConfig.GRAVITY_THRESHOLD_MG,
        'min_airtime_ms': AirtimeDetectionConfig.MIN_AIRTIME_MS,
        'max_airtime_ms': AirtimeDetectionConfig.MAX_AIRTIME_MS,
        'angular_velocity_threshold': AirtimeDetectionConfig.ANGULAR_VELOCITY_THRESHOLD,
        'height_change_threshold': AirtimeDetectionConfig.HEIGHT_CHANGE_THRESHOLD
    }
    
    # 用传入的参数覆盖默认配置
    default_config.update(kwargs)
    return default_config


# 配置验证函数
def validate_config(config: dict) -> bool:
    """验证配置参数的有效性"""
    required_keys = [
        'gravity_threshold',
        'min_airtime_ms', 
        'max_airtime_ms',
        'angular_velocity_threshold',
        'height_change_threshold'
    ]
    
    # 检查必需的键
    for key in required_keys:
        if key not in config:
            print(f"配置缺少必需的参数: {key}")
            return False
    
    # 检查参数范围
    if config['gravity_threshold'] <= 0:
        print("gravity_threshold 必须大于 0")
        return False
        
    if config['min_airtime_ms'] <= 0:
        print("min_airtime_ms 必须大于 0")
        return False
        
    if config['max_airtime_ms'] <= config['min_airtime_ms']:
        print("max_airtime_ms 必须大于 min_airtime_ms")
        return False
        
    if config['angular_velocity_threshold'] <= 0:
        print("angular_velocity_threshold 必须大于 0")
        return False
        
    if config['height_change_threshold'] < 0:
        print("height_change_threshold 必须大于等于 0")
        return False
    
    return True


if __name__ == "__main__":
    # 测试配置
    print("=== 滑雪滞空计算器配置测试 ===")
    
    # 测试不同场景的配置
    scenarios = ['freestyle', 'alpine', 'snowboard', 'cross_country']
    
    for scenario in scenarios:
        config = get_config_for_scenario(scenario)
        print(f"\n{scenario.upper()} 配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # 验证配置
        is_valid = validate_config(config)
        print(f"  配置有效性: {'✓' if is_valid else '✗'}")
    
    # 测试自定义配置
    print("\n自定义配置测试:")
    custom_config = create_custom_config(
        gravity_threshold=900,
        min_airtime_ms=150,
        max_airtime_ms=4000
    )
    
    for key, value in custom_config.items():
        print(f"  {key}: {value}")
    
    is_valid = validate_config(custom_config)
    print(f"  配置有效性: {'✓' if is_valid else '✗'}")
