#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的旋转检测器
使用角度积分而非角速度来计算旋转
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import AirtimeEvent, SensorData
from typing import List
from dataclasses import dataclass
import math


@dataclass
class ImprovedAirtimeEvent(AirtimeEvent):
    """改进的滞空事件，包含准确的旋转信息"""
    rotation_angle: float = 0.0           # 总旋转角度（度）
    rotation_speed_deg_per_sec: float = 0.0  # 平均旋转速度（度/秒）
    rotation_direction: str = "none"      # 旋转方向
    start_data_index: int = 0             # 开始数据点索引
    end_data_index: int = 0               # 结束数据点索引


class ImprovedRotationDetector(OptimizedAirtimeDetector):
    """改进的旋转检测器"""
    
    def _calculate_rotation_by_angle_integration(self, airtime_segment: List[SensorData]) -> dict:
        """通过角度积分计算真实旋转"""
        if len(airtime_segment) < 2:
            return {
                'rotation_angle': 0.0,
                'rotation_speed': 0.0,
                'rotation_detected': False,
                'rotation_direction': 'none',
                'max_angular_velocity': 0.0
            }
        
        # 计算总时间
        start_time = airtime_segment[0].timestamp
        end_time = airtime_segment[-1].timestamp
        total_time_seconds = (end_time - start_time).total_seconds()
        
        # 添加毫秒精度
        start_ms = airtime_segment[0].milliseconds
        end_ms = airtime_segment[-1].milliseconds
        total_time_seconds += (end_ms - start_ms) / 1000.0
        
        if total_time_seconds <= 0:
            return {
                'rotation_angle': 0.0,
                'rotation_speed': 0.0,
                'rotation_detected': False,
                'rotation_direction': 'none',
                'max_angular_velocity': 0.0
            }
        
        # 计算平均时间间隔
        dt = total_time_seconds / (len(airtime_segment) - 1)
        
        # 使用Z轴角速度积分计算旋转角度
        z_angle = 0.0
        max_gz = 0.0
        
        for i in range(1, len(airtime_segment)):
            # 梯形积分法
            gz_current = airtime_segment[i].gz
            gz_previous = airtime_segment[i-1].gz
            gz_avg = (gz_current + gz_previous) / 2
            
            # 积分计算角度变化
            z_angle += gz_avg * dt
            
            # 记录最大角速度
            max_gz = max(max_gz, abs(gz_current))
        
        # 计算平均旋转速度
        avg_rotation_speed = abs(z_angle) / total_time_seconds if total_time_seconds > 0 else 0
        
        # 判断旋转方向
        if z_angle > 30:
            direction = 'clockwise'
        elif z_angle < -30:
            direction = 'counterclockwise'
        else:
            direction = 'none'
        
        # 判断是否有明显旋转（超过30度认为是有意的旋转动作）
        rotation_detected = abs(z_angle) > 30
        
        return {
            'rotation_angle': abs(z_angle),
            'rotation_speed': avg_rotation_speed,
            'rotation_detected': rotation_detected,
            'rotation_direction': direction,
            'max_angular_velocity': max_gz,
            'total_time': total_time_seconds
        }
    
    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[ImprovedAirtimeEvent]:
        """检测滞空事件并计算改进的旋转信息"""
        if len(sensor_data) < 10:
            return []

        print("🎿 使用改进的旋转检测算法...")

        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, data in enumerate(sensor_data):
            # 使用基础的滞空检测逻辑
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            z_weightless = abs(data.az) < (self.standard_gravity * 0.25)
            angular_velocity = data.total_angular_velocity()

            # 检测Z轴突变
            z_spike = False
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.6)

            # 综合判断
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and
                 angular_velocity > self.angular_velocity_threshold * 0.5) or
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)
            )
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = data
                airtime_start_idx = i

            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        
                        # 计算滞空特征
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        
                        # 基础置信度计算
                        confidence = self._calculate_confidence(airtime_segment, duration_ms)
                        
                        # 改进的旋转计算
                        rotation_metrics = self._calculate_rotation_by_angle_integration(airtime_segment)
                        
                        event = ImprovedAirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=sensor_data[i].timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_angular_velocity'],  # 保持兼容性
                            avg_rotation_speed=rotation_metrics['rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected'],
                            rotation_angle=rotation_metrics['rotation_angle'],
                            rotation_speed_deg_per_sec=rotation_metrics['rotation_speed'],
                            rotation_direction=rotation_metrics['rotation_direction'],
                            start_data_index=airtime_start_idx,
                            end_data_index=i
                        )
                        
                        airtime_events.append(event)
                
                airtime_start = None
                airtime_start_idx = 0
        
        print(f"🎿 改进检测完成，发现 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def get_precise_timestamps(self, sensor_data: List[SensorData], event: ImprovedAirtimeEvent) -> tuple:
        """获取精确的时间戳"""
        start_data = sensor_data[event.start_data_index]
        end_data = sensor_data[event.end_data_index]
        
        start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
        end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
        
        return start_timestamp, end_timestamp


def main():
    """主函数"""
    print("🌀 改进的旋转检测器测试")
    print("=" * 80)
    
    detector = ImprovedRotationDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 使用改进的检测方法
    improved_events = detector.detect_improved_airtime_events(sensor_data)
    
    if not improved_events:
        print("❌ 未检测到滞空事件")
        return
    
    # 分析结果
    stats = detector.analyze_airtime_statistics(improved_events)
    
    print(f"\n📊 检测结果:")
    print(f"总检测事件: {stats['total_events']} 次")
    print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
    print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
    
    # 获取高质量事件
    quality_events = [e for e in improved_events if e.confidence >= 0.6]
    
    print(f"\n🏆 高质量滞空事件 (置信度≥0.6): {len(quality_events)} 次")
    
    if quality_events:
        print(f"\n详细列表:")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'旋转角度(°)':<12} {'旋转速度(°/s)':<14} {'方向':<12}")
        print("-" * 140)
        
        for i, event in enumerate(quality_events, 1):
            start_timestamp, end_timestamp = detector.get_precise_timestamps(sensor_data, event)
            
            print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {event.rotation_angle:<12.1f} "
                  f"{event.rotation_speed_deg_per_sec:<14.1f} {event.rotation_direction:<12}")
    
    # 旋转分析
    print(f"\n🌀 旋转分析:")
    rotation_events = [e for e in quality_events if e.rotation_detected]
    no_rotation_events = [e for e in quality_events if not e.rotation_detected]
    
    print(f"   有旋转的事件: {len(rotation_events)} 次")
    print(f"   无旋转的事件: {len(no_rotation_events)} 次")
    
    if rotation_events:
        avg_angle = sum(e.rotation_angle for e in rotation_events) / len(rotation_events)
        avg_speed = sum(e.rotation_speed_deg_per_sec for e in rotation_events) / len(rotation_events)
        print(f"   平均旋转角度: {avg_angle:.1f}°")
        print(f"   平均旋转速度: {avg_speed:.1f}°/s")
        
        # 旋转方向统计
        clockwise = len([e for e in rotation_events if e.rotation_direction == 'clockwise'])
        counterclockwise = len([e for e in rotation_events if e.rotation_direction == 'counterclockwise'])
        print(f"   顺时针旋转: {clockwise} 次")
        print(f"   逆时针旋转: {counterclockwise} 次")
    
    print(f"\n💡 改进说明:")
    print(f"   ✅ 使用角度积分代替瞬时角速度")
    print(f"   ✅ 基于总旋转角度判断是否有旋转动作")
    print(f"   ✅ 计算平均旋转速度而非最大值")
    print(f"   ✅ 识别旋转方向（顺时针/逆时针）")
    print(f"   ✅ 过滤传感器噪声，只识别真正的旋转动作")


if __name__ == "__main__":
    main()
