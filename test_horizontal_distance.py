#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水平距离计算功能
"""

from improved_boundary_detector import ImprovedBoundaryDetector

def test_horizontal_distance():
    """测试水平距离计算"""
    detector = ImprovedBoundaryDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    
    if sensor_data:
        print(f"数据点数量: {len(sensor_data)}")
        
        # 检查前几个数据点的速度
        print("\n前10个数据点的速度:")
        for i, data in enumerate(sensor_data[:10]):
            print(f"  数据点{i+1}: s = {data.s}")
        
        # 测试水平距离计算
        test_segment = sensor_data[35:90]  # 滞空段
        duration = 1.679
        
        horizontal_distance = detector._calculate_horizontal_distance(test_segment, duration)
        print(f"\n滞空段水平距离计算结果: {horizontal_distance}")
        
        # 检查滞空段的速度分布
        speeds = [data.s for data in test_segment]
        non_zero_speeds = [s for s in speeds if s != 0]
        
        print(f"滞空段总数据点: {len(test_segment)}")
        print(f"非零速度数量: {len(non_zero_speeds)}")
        print(f"速度范围: {min(speeds)} ~ {max(speeds)}")
        
        if non_zero_speeds:
            print(f"非零速度: {non_zero_speeds}")
        else:
            print("所有速度都为0，水平距离应为null")

if __name__ == "__main__":
    test_horizontal_distance()
