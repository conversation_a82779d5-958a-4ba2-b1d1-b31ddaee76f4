#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Z轴重力分析工具
对比总加速度和Z轴加速度在滞空检测中的差异
"""

import matplotlib.pyplot as plt
import numpy as np
from skiing_airtime_calculator import SkiingAirtimeCalculator
from typing import List

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ZAxisGravityAnalyzer:
    """Z轴重力分析器"""
    
    def __init__(self):
        self.standard_gravity = 980  # mg
    
    def analyze_gravity_patterns(self, filename: str):
        """分析重力模式"""
        calculator = SkiingAirtimeCalculator()
        sensor_data = calculator.load_sensor_data(filename)
        
        if not sensor_data:
            print("无法加载数据")
            return
        
        print(f"分析 {len(sensor_data)} 个数据点的重力模式")
        
        # 提取各种加速度数据
        timestamps = [data.timestamp for data in sensor_data]
        
        # 原方法：总加速度
        total_accelerations = [data.total_acceleration() for data in sensor_data]
        total_gravity_deviations = [abs(acc - self.standard_gravity) for acc in total_accelerations]
        
        # 改进方法：Z轴加速度
        z_accelerations = [abs(data.az) for data in sensor_data]
        z_gravity_deviations = [abs(abs(data.az) - self.standard_gravity) for data in sensor_data]
        
        # 水平加速度
        horizontal_accelerations = [(data.ax**2 + data.ay**2)**0.5 for data in sensor_data]
        
        # 各轴分量
        ax_values = [data.ax for data in sensor_data]
        ay_values = [data.ay for data in sensor_data]
        az_values = [data.az for data in sensor_data]
        
        # 统计分析
        print(f"\n📊 统计分析:")
        print(f"总加速度范围: {min(total_accelerations):.1f} - {max(total_accelerations):.1f} mg")
        print(f"Z轴加速度范围: {min(az_values):.1f} - {max(az_values):.1f} mg")
        print(f"水平加速度范围: {min(horizontal_accelerations):.1f} - {max(horizontal_accelerations):.1f} mg")
        
        print(f"\n🎯 重力偏差分析:")
        print(f"总加速度重力偏差范围: {min(total_gravity_deviations):.1f} - {max(total_gravity_deviations):.1f} mg")
        print(f"Z轴重力偏差范围: {min(z_gravity_deviations):.1f} - {max(z_gravity_deviations):.1f} mg")
        
        # 阈值分析
        thresholds = [500, 800, 1000, 1200, 1500]
        
        print(f"\n🔍 不同阈值下的触发点数量:")
        print(f"{'阈值(mg)':<10} {'总加速度':<10} {'Z轴加速度':<10} {'差异':<10}")
        print("-" * 45)
        
        for threshold in thresholds:
            total_triggers = sum(1 for dev in total_gravity_deviations if dev > threshold)
            z_triggers = sum(1 for dev in z_gravity_deviations if dev > threshold)
            difference = total_triggers - z_triggers
            
            print(f"{threshold:<10} {total_triggers:<10} {z_triggers:<10} {difference:<10}")
        
        # 检测失重状态（Z轴加速度接近0）
        weightless_threshold = self.standard_gravity * 0.3  # 30%重力以下认为失重
        weightless_points = [abs(data.az) < weightless_threshold for data in sensor_data]
        weightless_count = sum(weightless_points)
        
        print(f"\n🪶 失重状态分析:")
        print(f"Z轴加速度 < {weightless_threshold:.1f}mg 的点数: {weightless_count} / {len(sensor_data)}")
        print(f"失重状态占比: {weightless_count/len(sensor_data)*100:.1f}%")
        
        return {
            'timestamps': timestamps,
            'total_accelerations': total_accelerations,
            'total_gravity_deviations': total_gravity_deviations,
            'z_accelerations': z_accelerations,
            'z_gravity_deviations': z_gravity_deviations,
            'horizontal_accelerations': horizontal_accelerations,
            'ax_values': ax_values,
            'ay_values': ay_values,
            'az_values': az_values,
            'weightless_points': weightless_points
        }
    
    def compare_detection_methods(self, filename: str):
        """对比不同检测方法的结果"""
        print(f"\n{'='*60}")
        print(f"对比总加速度 vs Z轴加速度检测方法")
        print(f"{'='*60}")
        
        # 原方法检测
        print(f"\n🔸 原方法（总加速度）:")
        original_calculator = SkiingAirtimeCalculator(
            gravity_threshold=800,
            angular_velocity_threshold=1000
        )
        
        sensor_data = original_calculator.load_sensor_data(filename)
        if sensor_data:
            # 临时修改检测逻辑为原来的总加速度方法
            original_events = self._detect_with_total_acceleration(sensor_data, original_calculator)
            print(f"检测到事件: {len(original_events)} 次")
            
            if original_events:
                total_duration = sum(e.duration_seconds for e in original_events)
                print(f"总滞空时间: {total_duration:.2f} 秒")
                print(f"平均滞空时间: {total_duration/len(original_events):.2f} 秒")
        
        # 改进方法检测
        print(f"\n🔹 改进方法（Z轴加速度）:")
        improved_calculator = SkiingAirtimeCalculator(
            gravity_threshold=800,
            angular_velocity_threshold=1000
        )
        
        if sensor_data:
            improved_events = improved_calculator.detect_airtime_events(sensor_data)
            print(f"检测到事件: {len(improved_events)} 次")
            
            if improved_events:
                total_duration = sum(e.duration_seconds for e in improved_events)
                print(f"总滞空时间: {total_duration:.2f} 秒")
                print(f"平均滞空时间: {total_duration/len(improved_events):.2f} 秒")
                
                # 显示检测到的事件
                print(f"\n检测到的滞空事件:")
                for i, event in enumerate(improved_events[:10], 1):
                    print(f"  {i}. {event.start_time.strftime('%H:%M:%S')} - "
                          f"{event.end_time.strftime('%H:%M:%S')}, "
                          f"持续 {event.duration_seconds:.2f}s, "
                          f"置信度 {event.confidence:.2f}")
        
        # 分析差异
        if sensor_data:
            print(f"\n📈 方法对比:")
            original_count = len(original_events) if 'original_events' in locals() else 0
            improved_count = len(improved_events) if 'improved_events' in locals() else 0
            
            print(f"原方法检测事件数: {original_count}")
            print(f"改进方法检测事件数: {improved_count}")
            print(f"减少误判: {original_count - improved_count} 次")
            print(f"误判减少率: {(original_count - improved_count)/original_count*100:.1f}%" if original_count > 0 else "N/A")
    
    def _detect_with_total_acceleration(self, sensor_data, calculator):
        """使用原来的总加速度方法检测（用于对比）"""
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0
        
        for i, data in enumerate(sensor_data):
            # 原方法：使用总加速度
            total_acc = data.total_acceleration()
            gravity_deviation = abs(total_acc - calculator.standard_gravity)
            angular_velocity = data.total_angular_velocity()
            
            is_airborne = (
                gravity_deviation > calculator.gravity_threshold or
                angular_velocity > calculator.angular_velocity_threshold
            )
            
            if is_airborne and not in_airtime:
                in_airtime = True
                airtime_start = data
                airtime_start_idx = i
                
            elif not is_airborne and in_airtime:
                in_airtime = False
                
                if airtime_start:
                    duration_ms = calculator._calculate_duration_ms(airtime_start, data)
                    
                    if calculator.min_airtime_ms <= duration_ms <= calculator.max_airtime_ms:
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        max_height_change = calculator._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        confidence = calculator._calculate_confidence(airtime_segment, duration_ms)
                        
                        from skiing_airtime_calculator import AirtimeEvent
                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=data.timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence
                        )
                        
                        airtime_events.append(event)
                
                airtime_start = None
                airtime_start_idx = 0
        
        return airtime_events


def main():
    """主函数"""
    analyzer = ZAxisGravityAnalyzer()
    
    # 分析重力模式
    data = analyzer.analyze_gravity_patterns('20250330153942712068-济洲平花.txt')
    
    # 对比检测方法
    analyzer.compare_detection_methods('20250330153942712068-济洲平花.txt')
    
    print(f"\n💡 结论:")
    print(f"1. Z轴加速度更准确反映重力变化")
    print(f"2. 总加速度包含水平运动，容易误判")
    print(f"3. 失重检测（Z轴接近0）可以识别真正的滞空")
    print(f"4. 建议使用Z轴加速度 + 失重检测的组合方法")


if __name__ == "__main__":
    main()
