#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的滑雪滞空检测算法
基于多重条件和模式识别，减少误判
"""

import numpy as np
from typing import List, Tu<PERSON>
from skiing_airtime_calculator import SkiingAirtimeCalculator, SensorData, AirtimeEvent
from datetime import datetime, timedelta


class ImprovedAirtimeDetector(SkiingAirtimeCalculator):
    """改进的滑雪滞空检测器"""
    
    def __init__(self, 
                 # 更严格的阈值
                 gravity_threshold: float = 1200,      # 提高重力阈值
                 angular_velocity_threshold: float = 2000,  # 提高角速度阈值
                 min_airtime_ms: int = 200,            # 提高最小滞空时间
                 max_airtime_ms: int = 5000,           # 降低最大滞空时间
                 height_change_threshold: float = 2.0,  # 提高高度变化阈值
                 # 新增参数
                 acceleration_spike_threshold: float = 2000,  # 加速度突变阈值
                 consistency_window: int = 3,           # 一致性检查窗口
                 min_confidence_for_valid: float = 0.7):  # 提高有效事件的置信度要求
        
        super().__init__(gravity_threshold, min_airtime_ms, max_airtime_ms, 
                        angular_velocity_threshold, height_change_threshold)
        
        self.acceleration_spike_threshold = acceleration_spike_threshold
        self.consistency_window = consistency_window
        self.min_confidence_for_valid = min_confidence_for_valid
    
    def _smooth_data(self, values: List[float], window_size: int = 3) -> List[float]:
        """数据平滑处理"""
        if len(values) < window_size:
            return values
        
        smoothed = []
        for i in range(len(values)):
            start = max(0, i - window_size // 2)
            end = min(len(values), i + window_size // 2 + 1)
            smoothed.append(sum(values[start:end]) / (end - start))
        
        return smoothed
    
    def _detect_acceleration_spikes(self, sensor_data: List[SensorData]) -> List[bool]:
        """检测加速度突变（可能表示离地或着地）"""
        accelerations = [data.total_acceleration() for data in sensor_data]
        smoothed_acc = self._smooth_data(accelerations, 5)
        
        spikes = []
        for i in range(len(smoothed_acc)):
            if i == 0:
                spikes.append(False)
                continue
            
            # 检测加速度的突然变化
            acc_change = abs(smoothed_acc[i] - smoothed_acc[i-1])
            is_spike = acc_change > self.acceleration_spike_threshold
            spikes.append(is_spike)
        
        return spikes
    
    def _check_consistency(self, conditions: List[bool], window_size: int) -> List[bool]:
        """检查条件的一致性，减少单点噪声"""
        consistent = []
        for i in range(len(conditions)):
            start = max(0, i - window_size // 2)
            end = min(len(conditions), i + window_size // 2 + 1)
            
            # 窗口内大部分点都满足条件才认为是真正的滞空
            window_conditions = conditions[start:end]
            true_count = sum(window_conditions)
            consistent.append(true_count >= len(window_conditions) * 0.6)
        
        return consistent
    
    def _analyze_motion_pattern(self, segment: List[SensorData]) -> dict:
        """分析运动模式特征"""
        if len(segment) < 3:
            return {'is_jump_like': False, 'pattern_score': 0.0}
        
        # 提取特征
        accelerations = [data.total_acceleration() for data in segment]
        angular_velocities = [data.total_angular_velocity() for data in segment]
        z_positions = [data.z for data in segment]
        
        # 特征1: 加速度模式 - 真正的跳跃应该有明显的起跳和落地
        acc_variance = np.var(accelerations)
        acc_range = max(accelerations) - min(accelerations)
        
        # 特征2: 角速度模式 - 空中动作通常有旋转
        ang_variance = np.var(angular_velocities)
        max_angular = max(angular_velocities)
        
        # 特征3: 高度变化模式
        z_range = max(z_positions) - min(z_positions)
        
        # 特征4: 时间模式 - 合理的滞空时间
        duration_ms = len(segment) * 30  # 假设30ms采样间隔
        
        # 综合评分
        pattern_score = 0.0
        
        # 加速度变化评分 (0-0.3)
        if acc_range > 1500:
            pattern_score += 0.3
        elif acc_range > 1000:
            pattern_score += 0.2
        elif acc_range > 500:
            pattern_score += 0.1
        
        # 角速度评分 (0-0.3)
        if max_angular > 3000:
            pattern_score += 0.3
        elif max_angular > 2000:
            pattern_score += 0.2
        elif max_angular > 1500:
            pattern_score += 0.1
        
        # 高度变化评分 (0-0.2)
        if z_range > 10:
            pattern_score += 0.2
        elif z_range > 5:
            pattern_score += 0.1
        
        # 持续时间评分 (0-0.2)
        if 300 <= duration_ms <= 2000:
            pattern_score += 0.2
        elif 200 <= duration_ms <= 3000:
            pattern_score += 0.1
        
        is_jump_like = pattern_score >= 0.5
        
        return {
            'is_jump_like': is_jump_like,
            'pattern_score': pattern_score,
            'acc_range': acc_range,
            'max_angular': max_angular,
            'z_range': z_range,
            'duration_ms': duration_ms
        }
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空事件检测"""
        if len(sensor_data) < 10:
            return []
        
        print("使用改进算法检测滞空事件...")
        
        # 第一步：基础条件检测
        basic_conditions = []
        for data in sensor_data:
            total_acc = data.total_acceleration()
            gravity_deviation = abs(total_acc - self.standard_gravity)
            angular_velocity = data.total_angular_velocity()
            
            # 更严格的基础条件
            basic_airborne = (
                gravity_deviation > self.gravity_threshold and
                angular_velocity > self.angular_velocity_threshold
            )
            basic_conditions.append(basic_airborne)
        
        # 第二步：一致性检查
        consistent_conditions = self._check_consistency(basic_conditions, self.consistency_window)
        
        # 第三步：检测加速度突变
        acceleration_spikes = self._detect_acceleration_spikes(sensor_data)
        
        # 第四步：组合条件
        combined_conditions = []
        for i in range(len(sensor_data)):
            # 需要同时满足：基础条件 + 一致性 + (加速度突变 或 极高角速度)
            angular_vel = sensor_data[i].total_angular_velocity()
            combined = (
                consistent_conditions[i] and
                (acceleration_spikes[i] or angular_vel > self.angular_velocity_threshold * 1.5)
            )
            combined_conditions.append(combined)
        
        # 第五步：提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0
        
        for i, is_airborne in enumerate(combined_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
                
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                if airtime_start:
                    # 计算滞空持续时间
                    duration_ms = self._calculate_duration_ms(airtime_start, sensor_data[i])
                    
                    # 时间过滤
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        # 提取滞空片段
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        
                        # 运动模式分析
                        pattern_analysis = self._analyze_motion_pattern(airtime_segment)
                        
                        # 只有符合跳跃模式的才认为是真正的滞空
                        if pattern_analysis['is_jump_like']:
                            max_height_change = self._calculate_max_height_change(airtime_segment)
                            max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                            
                            # 重新计算置信度，加入模式分析结果
                            base_confidence = self._calculate_confidence(airtime_segment, duration_ms)
                            pattern_confidence = pattern_analysis['pattern_score']
                            final_confidence = (base_confidence + pattern_confidence) / 2
                            
                            event = AirtimeEvent(
                                start_time=airtime_start.timestamp,
                                end_time=sensor_data[i].timestamp,
                                duration_ms=duration_ms,
                                max_height_change=max_height_change,
                                max_acceleration=max_acceleration,
                                confidence=final_confidence
                            )
                            
                            airtime_events.append(event)
                            
                            print(f"检测到滞空事件: {airtime_start.timestamp.strftime('%H:%M:%S')} - "
                                  f"{sensor_data[i].timestamp.strftime('%H:%M:%S')}, "
                                  f"持续{duration_ms}ms, 置信度{final_confidence:.2f}, "
                                  f"模式评分{pattern_analysis['pattern_score']:.2f}")
                
                airtime_start = None
                airtime_start_idx = 0
        
        print(f"改进算法检测到 {len(airtime_events)} 个滞空事件")
        return airtime_events
    
    def get_valid_events(self, airtime_events: List[AirtimeEvent]) -> List[AirtimeEvent]:
        """获取高质量的有效滞空事件"""
        return [e for e in airtime_events if e.confidence >= self.min_confidence_for_valid]


def compare_algorithms():
    """对比原算法和改进算法的结果"""
    print("=" * 60)
    print("滑雪滞空检测算法对比")
    print("=" * 60)
    
    # 原算法
    print("\n🔸 原算法结果:")
    original_detector = SkiingAirtimeCalculator()
    sensor_data = original_detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if sensor_data:
        original_events = original_detector.detect_airtime_events(sensor_data)
        original_stats = original_detector.analyze_airtime_statistics(original_events)
        
        print(f"检测到事件: {original_stats['total_events']} 次")
        print(f"有效事件 (置信度>0.5): {original_stats['valid_events']} 次")
        print(f"总滞空时间: {original_stats['total_airtime_seconds']:.2f} 秒")
    
    # 改进算法
    print("\n🔹 改进算法结果:")
    improved_detector = ImprovedAirtimeDetector()
    
    if sensor_data:
        improved_events = improved_detector.detect_airtime_events(sensor_data)
        improved_stats = improved_detector.analyze_airtime_statistics(improved_events)
        
        print(f"检测到事件: {improved_stats['total_events']} 次")
        print(f"有效事件 (置信度>0.7): {len(improved_detector.get_valid_events(improved_events))} 次")
        print(f"总滞空时间: {improved_stats['total_airtime_seconds']:.2f} 秒")
        
        # 显示高质量事件
        valid_events = improved_detector.get_valid_events(improved_events)
        if valid_events:
            print(f"\n🏆 高质量滞空事件 (置信度≥0.7):")
            for i, event in enumerate(valid_events, 1):
                print(f"  {i}. {event.start_time.strftime('%H:%M:%S')} - "
                      f"{event.end_time.strftime('%H:%M:%S')}, "
                      f"持续 {event.duration_seconds:.2f}s, "
                      f"置信度 {event.confidence:.2f}")
    
    print(f"\n💡 建议:")
    print(f"1. 对比视频验证改进算法的准确性")
    print(f"2. 根据实际情况进一步调整阈值")
    print(f"3. 可以设置更严格的置信度要求")


def main():
    compare_algorithms()


if __name__ == "__main__":
    main()
