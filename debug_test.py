#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试
"""

try:
    from improved_boundary_detector import ImprovedBoundaryDetector
    print("✅ 成功导入 ImprovedBoundaryDetector")
    
    detector = ImprovedBoundaryDetector()
    print("✅ 成功创建检测器实例")
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    print(f"✅ 成功加载数据: {len(sensor_data)} 条记录")
    
    # 测试改进的检测
    print("🔍 开始检测...")
    improved_events = detector.detect_improved_airtime_events(sensor_data)
    print(f"✅ 检测完成: {len(improved_events)} 个事件")
    
    # 测试水平距离计算
    if improved_events and hasattr(improved_events[0], 'start_data_index'):
        event = improved_events[0]
        airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
        horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
        print(f"✅ 水平距离计算: {horizontal_distance}")
    
    print("✅ 所有测试通过")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
