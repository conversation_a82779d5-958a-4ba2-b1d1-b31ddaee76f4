#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的滑雪滞空检测器 - 只输出高置信度事件
"""

from improved_boundary_detector import ImprovedBoundaryDetector

def main():
    """简化的滑雪滞空检测器"""
    # 创建检测器
    detector = ImprovedBoundaryDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    
    if not sensor_data:
        print("无法加载数据文件")
        return
    
    # 检测滞空事件
    improved_events = detector.detect_improved_airtime_events(sensor_data)
    
    # 过滤高置信度事件
    high_confidence_events = [event for event in improved_events if event.confidence >= 0.85]
    
    if not high_confidence_events:
        print("未检测到高置信度滞空事件")
        return
    
    # 输出表格头部
    print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'水平距离':<10} {'置信度':<6} {'转体角度(°)':<10} {'转体速度(圈/s)':<12} {'转动方向':<10}")
    print("-" * 120)
    
    # 输出每个高置信度事件
    for i, event in enumerate(high_confidence_events, 1):
        # 获取精确时间戳
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            start_data = sensor_data[event.start_data_index]
            end_data = sensor_data[event.end_data_index]
            
            start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
            end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
            
            # 计算转体信息
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            rotation_info = detector._calculate_detailed_rotation(airtime_segment, event.duration_seconds)
            
            rotation_angle = rotation_info['total_angle']
            rotation_speed = rotation_info['avg_speed']
            rotation_direction = rotation_info['direction']
            first_half_speed = rotation_info['first_half_speed']
            second_half_speed = rotation_info['second_half_speed']
            has_split_analysis = rotation_info['has_split_analysis']
            
            # 计算水平距离
            horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
        else:
            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
            rotation_angle = 0.0
            rotation_speed = 0.0
            rotation_direction = 'none'
            horizontal_distance = "null"
            has_split_analysis = False
            first_half_speed = 0.0
            second_half_speed = 0.0
        
        # 构建转体速度显示字符串
        if has_split_analysis:
            speed_display = f"{rotation_speed:.2f}(前:{first_half_speed:.2f}|后:{second_half_speed:.2f})"
        else:
            speed_display = f"{rotation_speed:.2f}"
        
        # 缩短时间戳显示（只显示时分秒.毫秒）
        start_time_short = start_timestamp.split(' ')[1]  # 只取时间部分
        end_time_short = end_timestamp.split(' ')[1]      # 只取时间部分
        
        print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
              f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
              f"{horizontal_distance:<10} {event.confidence:<6.3f} {rotation_angle:<10.1f} "
              f"{speed_display:<12} {rotation_direction:<10}")

if __name__ == "__main__":
    main()
