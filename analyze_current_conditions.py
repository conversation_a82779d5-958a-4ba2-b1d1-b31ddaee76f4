#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析当前判断条件
找出为什么所有动作都被判定为非转体
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
import math


def analyze_detection_conditions():
    """分析当前的检测条件"""
    print("🔍 分析当前转体检测条件")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    quality_events = [e for e in airtime_events if e.confidence >= 0.6]
    
    print(f"检测到 {len(quality_events)} 个高质量滞空事件")
    
    if not quality_events:
        print("❌ 没有高质量事件可分析")
        return
    
    # 分析每个事件为什么被判定为非转体
    for i, event in enumerate(quality_events[:5], 1):  # 分析前5个
        print(f"\n{'='*60}")
        print(f"🎿 事件 {i} 条件分析")
        print(f"{'='*60}")
        
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            
            # 重新计算角度积分
            total_time = event.duration_seconds
            dt = total_time / (len(airtime_segment) - 1) if len(airtime_segment) > 1 else 0
            
            x_angle = 0.0
            y_angle = 0.0
            z_angle = 0.0
            
            for j in range(1, len(airtime_segment)):
                gx_avg = (airtime_segment[j].gx + airtime_segment[j-1].gx) / 2
                gy_avg = (airtime_segment[j].gy + airtime_segment[j-1].gy) / 2
                gz_avg = (airtime_segment[j].gz + airtime_segment[j-1].gz) / 2
                
                x_angle += gx_avg * dt
                y_angle += gy_avg * dt
                z_angle += gz_avg * dt
            
            abs_x_angle = abs(x_angle)
            abs_y_angle = abs(y_angle)
            abs_z_angle = abs(z_angle)
            
            print(f"📊 角度数据:")
            print(f"   X轴角度: {abs_x_angle:.1f}° ({abs_x_angle/360:.2f}圈)")
            print(f"   Y轴角度: {abs_y_angle:.1f}° ({abs_y_angle/360:.2f}圈)")
            print(f"   Z轴角度: {abs_z_angle:.1f}° ({abs_z_angle/360:.2f}圈)")
            print(f"   持续时间: {total_time:.3f}秒")
            
            # 分析当前的4个判断条件
            min_spin_angle = 30  # 用户修改后的值
            max_other_axis_angle = max(abs_x_angle, abs_y_angle)
            
            # 条件1: Z轴角度 > 30°
            condition1 = abs_z_angle > min_spin_angle
            print(f"\n⚖️ 判断条件分析:")
            print(f"   条件1 - Z轴角度 > {min_spin_angle}°: {abs_z_angle:.1f}° > {min_spin_angle}° = {condition1}")
            
            # 条件2: Z轴角度是主要旋转轴
            condition2 = abs_z_angle > max_other_axis_angle * 1.2
            print(f"   条件2 - Z轴是主要旋转轴: {abs_z_angle:.1f}° > {max_other_axis_angle:.1f}° × 1.2 = {abs_z_angle:.1f}° > {max_other_axis_angle*1.2:.1f}° = {condition2}")
            
            # 条件3: Z轴角速度稳定性
            gz_values = [data.gz for data in airtime_segment]
            if len(gz_values) > 1:
                gz_mean = sum(gz_values) / len(gz_values)
                gz_variance = sum((gz - gz_mean)**2 for gz in gz_values) / len(gz_values)
                gz_std = (gz_variance ** 0.5)
            else:
                gz_std = 0
            
            condition3 = gz_std < 3000
            print(f"   条件3 - Z轴角速度稳定: 标准差 {gz_std:.1f}°/s < 3000°/s = {condition3}")
            
            # 条件4: 持续时间足够
            condition4 = total_time > 0.15
            print(f"   条件4 - 持续时间足够: {total_time:.3f}s > 0.15s = {condition4}")
            
            # 综合判断
            is_spin = condition1 and condition2 and condition3 and condition4
            print(f"\n🎯 综合判断: {condition1} AND {condition2} AND {condition3} AND {condition4} = {is_spin}")
            print(f"   结果: {'转体' if is_spin else '非转体'}")
            
            # 分析哪个条件导致失败
            if not is_spin:
                print(f"\n❌ 失败原因:")
                if not condition1:
                    print(f"   - Z轴角度不足 ({abs_z_angle:.1f}° ≤ {min_spin_angle}°)")
                if not condition2:
                    print(f"   - Z轴不是主要旋转轴 (其他轴更大: {max_other_axis_angle:.1f}°)")
                if not condition3:
                    print(f"   - Z轴角速度不稳定 (标准差 {gz_std:.1f}°/s ≥ 3000°/s)")
                if not condition4:
                    print(f"   - 持续时间不足 ({total_time:.3f}s ≤ 0.15s)")


def suggest_parameter_adjustment():
    """建议参数调整"""
    print(f"\n💡 参数调整建议")
    print("=" * 80)
    
    print(f"如果所有动作都被判定为非转体，可能的原因和解决方案:")
    print(f"\n1. 条件2太严格 (Z轴必须是主要旋转轴):")
    print(f"   当前: Z轴角度 > max(X轴, Y轴) × 1.2")
    print(f"   建议: 降低系数 1.2 → 0.8 或完全移除此条件")
    
    print(f"\n2. 条件3太严格 (角速度稳定性):")
    print(f"   当前: Z轴角速度标准差 < 3000°/s")
    print(f"   建议: 提高阈值 3000 → 5000 或移除此条件")
    
    print(f"\n3. 条件4可能太严格 (持续时间):")
    print(f"   当前: 持续时间 > 0.15s")
    print(f"   建议: 降低阈值 0.15 → 0.1s")
    
    print(f"\n4. 简化判断逻辑:")
    print(f"   建议: 只保留条件1 (Z轴角度 > 30°)")
    print(f"   或者: 条件1 + 放宽的条件2 (Z轴角度 > max(其他轴) × 0.5)")


def main():
    """主函数"""
    analyze_detection_conditions()
    suggest_parameter_adjustment()
    
    print(f"\n🔧 下一步:")
    print(f"   根据分析结果调整判断条件")
    print(f"   建议先简化为只检查Z轴角度")


if __name__ == "__main__":
    main()
