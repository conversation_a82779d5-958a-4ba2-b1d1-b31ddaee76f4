#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的高度计算
h字段 = 海拔高度(米) × 10
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator
from optimized_airtime_detector import OptimizedAirtimeDetector


def test_height_calculation():
    """测试修正后的高度计算"""
    print("🏔️ 测试修正后的滞空高度计算")
    print("h字段 = 海拔高度(米) × 10")
    print("=" * 60)
    
    # 使用优化检测器
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("无法加载数据")
        return
    
    # 分析h字段的数据特征
    h_values = [data.humidity for data in sensor_data]
    print(f"📊 h字段数据分析:")
    print(f"   数据范围: {min(h_values)} - {max(h_values)}")
    print(f"   总变化: {max(h_values) - min(h_values)}")
    print(f"   对应海拔范围: {min(h_values)/10:.1f}m - {max(h_values)/10:.1f}m")
    print(f"   海拔变化: {(max(h_values) - min(h_values))/10:.1f}m")
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    print(f"\n🎿 滞空事件高度分析 (前15个事件):")
    print(f"{'序号':<4} {'时间段':<17} {'持续(s)':<8} {'h字段差值':<10} {'滞空高度(m)':<12} {'置信度':<8}")
    print("-" * 75)
    
    for i, event in enumerate(airtime_events[:15], 1):
        # 找到对应的传感器数据段
        segment = []
        for data in sensor_data:
            if event.start_time <= data.timestamp <= event.end_time:
                segment.append(data)
        
        if segment:
            # 计算原始h字段差值
            h_values_segment = [data.humidity for data in segment]
            h_diff = max(h_values_segment) - min(h_values_segment)
            
            # 使用修正后的高度计算
            corrected_height = event.max_height_change
            
            print(f"{i:<4} {event.start_time.strftime('%H:%M:%S')}-{event.end_time.strftime('%H:%M:%S'):<17} "
                  f"{event.duration_seconds:<8.2f} {h_diff:<10.1f} "
                  f"{corrected_height:<12.3f} {event.confidence:<8.2f}")
    
    # 统计分析
    all_heights = [event.max_height_change for event in airtime_events]
    
    if all_heights:
        print(f"\n📈 滞空高度统计:")
        print(f"   事件数量: {len(airtime_events)}")
        print(f"   高度范围: {min(all_heights):.3f} - {max(all_heights):.3f} 米")
        print(f"   平均高度: {sum(all_heights)/len(all_heights):.3f} 米")
        print(f"   中位数高度: {sorted(all_heights)[len(all_heights)//2]:.3f} 米")
        
        # 按高度分类
        low_height = [h for h in all_heights if h < 0.5]
        medium_height = [h for h in all_heights if 0.5 <= h < 1.5]
        high_height = [h for h in all_heights if h >= 1.5]
        
        print(f"\n🏔️ 按高度分类:")
        print(f"   低高度 (<0.5m): {len(low_height)} 次")
        print(f"   中等高度 (0.5-1.5m): {len(medium_height)} 次")
        print(f"   高高度 (≥1.5m): {len(high_height)} 次")


def compare_old_vs_new():
    """对比修正前后的高度计算"""
    print(f"\n🔄 对比修正前后的高度计算")
    print("=" * 60)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        return
    
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    print(f"{'序号':<4} {'修正前(旧算法)':<15} {'修正后(÷10)':<12} {'差异':<8}")
    print("-" * 50)
    
    for i, event in enumerate(airtime_events[:10], 1):
        segment = []
        for data in sensor_data:
            if event.start_time <= data.timestamp <= event.end_time:
                segment.append(data)
        
        if segment:
            h_values = [data.humidity for data in segment]
            h_diff = max(h_values) - min(h_values)
            
            # 旧算法（智能单位转换）
            if h_diff > 1000:
                old_height = h_diff / 1000.0
            elif h_diff > 100:
                old_height = h_diff / 100.0
            else:
                old_height = h_diff / 100.0
            old_height = min(old_height, 3.0)
            
            # 新算法（÷10）
            new_height = h_diff / 10.0
            new_height = min(new_height, 5.0)
            
            difference = new_height - old_height
            
            print(f"{i:<4} {old_height:<15.3f} {new_height:<12.3f} {difference:<8.3f}")


def validate_height_reasonableness():
    """验证高度的合理性"""
    print(f"\n✅ 验证高度合理性")
    print("=" * 60)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        return
    
    airtime_events = detector.detect_airtime_events(sensor_data)
    all_heights = [event.max_height_change for event in airtime_events]
    
    # 检查合理性
    reasonable_count = 0
    unreasonable_count = 0
    
    for height in all_heights:
        if 0.1 <= height <= 3.0:  # 合理的滑雪跳跃高度范围
            reasonable_count += 1
        else:
            unreasonable_count += 1
    
    print(f"合理高度事件 (0.1-3.0m): {reasonable_count} 次")
    print(f"不合理高度事件: {unreasonable_count} 次")
    print(f"合理性比例: {reasonable_count/(reasonable_count+unreasonable_count)*100:.1f}%")
    
    # 显示不合理的事件
    if unreasonable_count > 0:
        print(f"\n⚠️  不合理高度事件:")
        for i, (event, height) in enumerate(zip(airtime_events, all_heights)):
            if not (0.1 <= height <= 3.0):
                print(f"  {event.start_time.strftime('%H:%M:%S')}: {height:.3f}m")
    
    print(f"\n💡 结论:")
    print(f"修正后的高度计算公式: 滞空高度(米) = h字段差值 ÷ 10")
    print(f"这个公式产生了{reasonable_count/(reasonable_count+unreasonable_count)*100:.1f}%的合理结果")


def main():
    """主函数"""
    test_height_calculation()
    compare_old_vs_new()
    validate_height_reasonableness()


if __name__ == "__main__":
    main()
