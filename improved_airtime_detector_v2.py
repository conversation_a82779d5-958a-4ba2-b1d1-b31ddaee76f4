#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的滞空检测器 V2
使用多特征融合和滑动窗口优化
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import SensorData, AirtimeEvent
from typing import List
import numpy as np
from dataclasses import dataclass


@dataclass
class AirtimeScore:
    """滞空评分"""
    index: int
    gravity_score: float
    angular_score: float
    height_score: float
    combined_score: float
    is_airborne: bool


class ImprovedAirtimeDetectorV2(OptimizedAirtimeDetector):
    """改进的滞空检测器V2"""
    
    def __init__(self):
        super().__init__()
        
        # 多特征阈值
        self.gravity_threshold_low = 300     # 低重力阈值
        self.gravity_threshold_high = 600    # 高重力阈值
        self.angular_threshold_low = 3000    # 低角速度阈值
        self.angular_threshold_high = 8000   # 高角速度阈值
        
        # 滑动窗口参数
        self.window_size = 5                 # 滑动窗口大小
        self.min_duration_points = 4         # 最小持续数据点数
        self.merge_gap_points = 3            # 合并间隔点数
        
        # 评分权重
        self.gravity_weight = 0.4
        self.angular_weight = 0.4
        self.height_weight = 0.2
        
        # 综合评分阈值
        self.airtime_score_threshold = 0.6
    
    def smooth_data(self, sensor_data: List[SensorData], window_size: int = 5) -> List[SensorData]:
        """使用滑动窗口平滑数据"""
        if len(sensor_data) < window_size:
            return sensor_data
        
        smoothed_data = []
        half_window = window_size // 2
        
        for i in range(len(sensor_data)):
            # 确定窗口范围
            start_idx = max(0, i - half_window)
            end_idx = min(len(sensor_data), i + half_window + 1)
            window_data = sensor_data[start_idx:end_idx]
            
            # 计算平均值
            avg_ax = sum(d.ax for d in window_data) / len(window_data)
            avg_ay = sum(d.ay for d in window_data) / len(window_data)
            avg_az = sum(d.az for d in window_data) / len(window_data)
            avg_gx = sum(d.gx for d in window_data) / len(window_data)
            avg_gy = sum(d.gy for d in window_data) / len(window_data)
            avg_gz = sum(d.gz for d in window_data) / len(window_data)
            
            # 创建平滑后的数据点（保持原始时间戳）
            smoothed_point = SensorData(
                timestamp=sensor_data[i].timestamp,
                milliseconds=sensor_data[i].milliseconds,
                x=sensor_data[i].x, y=sensor_data[i].y, z=sensor_data[i].z,
                P=sensor_data[i].P,
                ax=avg_ax, ay=avg_ay, az=avg_az,
                gx=avg_gx, gy=avg_gy, gz=avg_gz,
                mx=sensor_data[i].mx, my=sensor_data[i].my, mz=sensor_data[i].mz,
                temperature=sensor_data[i].temperature,
                humidity=sensor_data[i].humidity,
                speed=sensor_data[i].speed,
                fs=sensor_data[i].fs,
                longitude=sensor_data[i].longitude,
                latitude=sensor_data[i].latitude,
                gps_height=sensor_data[i].gps_height
            )
            smoothed_data.append(smoothed_point)
        
        return smoothed_data
    
    def calculate_airtime_scores(self, sensor_data: List[SensorData]) -> List[AirtimeScore]:
        """计算每个数据点的滞空评分"""
        scores = []
        
        for i, data in enumerate(sensor_data):
            # 1. 重力评分 (0-1)
            gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            if gravity_deviation < self.gravity_threshold_low:
                gravity_score = 0.0
            elif gravity_deviation > self.gravity_threshold_high:
                gravity_score = 1.0
            else:
                gravity_score = (gravity_deviation - self.gravity_threshold_low) / \
                               (self.gravity_threshold_high - self.gravity_threshold_low)
            
            # 2. 角速度评分 (0-1)
            angular_velocity = data.total_angular_velocity()
            if angular_velocity < self.angular_threshold_low:
                angular_score = 0.0
            elif angular_velocity > self.angular_threshold_high:
                angular_score = 1.0
            else:
                angular_score = (angular_velocity - self.angular_threshold_low) / \
                               (self.angular_threshold_high - self.angular_threshold_low)
            
            # 3. 高度变化评分 (0-1)
            height_score = 0.0
            if i > 2 and i < len(sensor_data) - 2:
                # 计算前后高度变化
                height_before = sensor_data[i-2].humidity
                height_after = sensor_data[i+2].humidity
                height_change = abs(height_after - height_before)
                height_score = min(1.0, height_change / 50.0)  # 5米变化为满分
            
            # 4. 综合评分
            combined_score = (
                gravity_score * self.gravity_weight +
                angular_score * self.angular_weight +
                height_score * self.height_weight
            )
            
            # 5. 判断是否滞空
            is_airborne = combined_score > self.airtime_score_threshold
            
            score = AirtimeScore(
                index=i,
                gravity_score=gravity_score,
                angular_score=angular_score,
                height_score=height_score,
                combined_score=combined_score,
                is_airborne=is_airborne
            )
            scores.append(score)
        
        return scores
    
    def find_airtime_candidates(self, scores: List[AirtimeScore]) -> List[tuple]:
        """找到滞空候选区间"""
        candidates = []
        in_airtime = False
        start_idx = 0
        
        for i, score in enumerate(scores):
            if score.is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                start_idx = i
            elif not score.is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                if i - start_idx >= self.min_duration_points:
                    candidates.append((start_idx, i-1))
        
        # 处理最后一个区间
        if in_airtime and len(scores) - start_idx >= self.min_duration_points:
            candidates.append((start_idx, len(scores)-1))
        
        return candidates
    
    def merge_close_candidates(self, candidates: List[tuple]) -> List[tuple]:
        """合并过近的候选区间"""
        if len(candidates) <= 1:
            return candidates
        
        merged = []
        current_start, current_end = candidates[0]
        
        for start, end in candidates[1:]:
            if start - current_end <= self.merge_gap_points:
                # 合并区间
                current_end = end
            else:
                # 保存当前区间，开始新区间
                merged.append((current_start, current_end))
                current_start, current_end = start, end
        
        # 添加最后一个区间
        merged.append((current_start, current_end))
        return merged
    
    def optimize_boundaries(self, candidates: List[tuple], sensor_data: List[SensorData]) -> List[tuple]:
        """优化边界检测"""
        optimized = []
        
        for start_idx, end_idx in candidates:
            # 向前扩展寻找真正的起跳点
            optimized_start = start_idx
            for i in range(max(0, start_idx - 5), start_idx):
                gravity_dev = abs(abs(sensor_data[i].az) - self.standard_gravity)
                if gravity_dev > self.gravity_threshold_low:
                    optimized_start = i
                    break
            
            # 向后扩展寻找真正的落地点
            optimized_end = end_idx
            for i in range(end_idx + 1, min(len(sensor_data), end_idx + 6)):
                gravity_dev = abs(abs(sensor_data[i].az) - self.standard_gravity)
                if gravity_dev < self.gravity_threshold_low:
                    optimized_end = i - 1
                    break
            
            optimized.append((optimized_start, optimized_end))
        
        return optimized
    
    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空检测"""
        if len(sensor_data) < 10:
            return []
        
        print("🚀 使用改进的多特征融合检测算法...")
        
        # 1. 数据平滑
        smoothed_data = self.smooth_data(sensor_data, self.window_size)
        
        # 2. 计算评分
        scores = self.calculate_airtime_scores(smoothed_data)
        
        # 3. 找到候选区间
        candidates = self.find_airtime_candidates(scores)
        print(f"   初始候选区间: {len(candidates)} 个")
        
        # 4. 合并过近的区间
        merged_candidates = self.merge_close_candidates(candidates)
        print(f"   合并后区间: {len(merged_candidates)} 个")
        
        # 5. 优化边界
        optimized_candidates = self.optimize_boundaries(merged_candidates, smoothed_data)
        print(f"   边界优化后: {len(optimized_candidates)} 个")
        
        # 6. 创建事件
        events = []
        for start_idx, end_idx in optimized_candidates:
            start_data = smoothed_data[start_idx]
            end_data = smoothed_data[end_idx]
            
            duration_ms = self._calculate_duration_ms(start_data, end_data)
            
            # 时间过滤
            if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                airtime_segment = smoothed_data[start_idx:end_idx+1]
                
                # 计算特征
                max_height_change = self._calculate_max_height_change(airtime_segment)
                max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                
                # 计算置信度（基于评分）
                segment_scores = scores[start_idx:end_idx+1]
                avg_score = sum(s.combined_score for s in segment_scores) / len(segment_scores)
                confidence = min(1.0, avg_score * 1.2)  # 稍微提升置信度
                
                # 计算旋转指标
                rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
                
                event = AirtimeEvent(
                    start_time=start_data.timestamp,
                    end_time=end_data.timestamp,
                    duration_ms=duration_ms,
                    max_height_change=max_height_change,
                    max_acceleration=max_acceleration,
                    confidence=confidence,
                    max_rotation_speed=rotation_metrics['max_rotation_speed'],
                    avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                    rotation_detected=rotation_metrics['rotation_detected']
                )
                
                # 添加索引信息
                event.start_data_index = start_idx
                event.end_data_index = end_idx
                
                events.append(event)
        
        print(f"🎿 改进检测完成，发现 {len(events)} 个滞空事件")
        return events


def main():
    """主函数"""
    print("🚀 改进的滞空检测器V2测试")
    print("=" * 80)
    
    detector = ImprovedAirtimeDetectorV2()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 使用改进的检测方法
    improved_events = detector.detect_improved_airtime_events(sensor_data)
    
    # 分析结果
    stats = detector.analyze_airtime_statistics(improved_events)
    
    print(f"\n📊 改进检测结果:")
    print(f"总检测事件: {stats['total_events']} 次")
    print(f"有效事件 (置信度>0.5): {stats['valid_events']} 次")
    print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
    
    # 对比原始检测
    original_events = detector.detect_airtime_events(sensor_data)
    print(f"\n🔄 对比原始检测:")
    print(f"原始检测事件: {len(original_events)} 次")
    print(f"改进检测事件: {len(improved_events)} 次")
    print(f"事件数量变化: {len(improved_events) - len(original_events):+d}")
    
    # 显示前10个事件
    if improved_events:
        quality_events = [e for e in improved_events if e.confidence >= 0.6]
        print(f"\n🏆 高质量滞空事件 (置信度≥0.6): {len(quality_events)} 次")
        
        if quality_events:
            print(f"\n详细列表 (前10个):")
            print(f"{'序号':<4} {'开始时间':<12} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'转体速度(圈/s)':<14}")
            print("-" * 80)
            
            for i, event in enumerate(quality_events[:10], 1):
                start_time = event.start_time.strftime('%H:%M:%S')
                print(f"{i:<4} {start_time:<12} {event.duration_seconds:<12.3f} "
                      f"{event.max_height_change:<12.3f} {event.confidence:<8.3f} "
                      f"{event.avg_rotation_speed:<14.2f}")


if __name__ == "__main__":
    main()
