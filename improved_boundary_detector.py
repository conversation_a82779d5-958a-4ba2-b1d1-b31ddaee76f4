#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的滞空边界检测器
解决滞空事件段划分偏小的问题
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import SensorData, AirtimeEvent
from typing import List, Tuple, Dict
import numpy as np


class ImprovedBoundaryDetector(OptimizedAirtimeDetector):
    """改进的边界检测器 - 针对滑雪平花动作和跳台滑雪优化"""

    def __init__(self):
        super().__init__()

        # 针对滑雪运动的优化参数
        self.skiing_gravity_threshold = 400      # 滑雪专用重力阈值（更敏感）
        self.skiing_angular_threshold = 600      # 滑雪专用角速度阈值
        self.flatland_angular_threshold = 300    # 平花动作角速度阈值（更低）
        self.jump_angular_threshold = 1000       # 跳台动作角速度阈值（更高）

        # 多级阈值设计
        self.gravity_threshold_strict = 600      # 严格阈值（核心滞空）
        self.gravity_threshold_loose = 200       # 宽松阈值（边界扩展，降低以捕获更多滞空）
        self.angular_threshold_strict = 800      # 严格角速度阈值
        self.angular_threshold_loose = 300       # 宽松角速度阈值（降低以适应平花动作）

        # 边界扩展参数
        self.pre_jump_window = 10                # 起跳前检测窗口（增加以捕获准备动作）
        self.post_landing_window = 10            # 落地后检测窗口（增加以捕获缓冲动作）
        self.boundary_extension_threshold = 0.25 # 边界扩展评分阈值（降低以扩大检测范围）

        # 渐进式检测参数
        self.gradient_threshold = 150            # 加速度梯度阈值（降低以提高敏感度）
        self.momentum_threshold = 0.5            # 动量变化阈值（降低）

        # 滑雪运动特定参数
        self.min_flatland_airtime_ms = 80        # 平花动作最小滞空时间（更短）
        self.min_jump_airtime_ms = 200           # 跳台动作最小滞空时间
        self.max_flatland_airtime_ms = 800       # 平花动作最大滞空时间
        self.max_jump_airtime_ms = 4000          # 跳台动作最大滞空时间

        # 运动类型检测参数
        self.speed_threshold_for_jump = 5.0      # 区分跳台和平花的速度阈值（m/s）
        self.height_threshold_for_jump = 0.5     # 区分跳台和平花的高度阈值（m）
    
    def detect_takeoff_preparation(self, sensor_data: List[SensorData], 
                                  core_start_idx: int) -> int:
        """检测起跳准备阶段，向前扩展起跳点"""
        
        # 向前搜索窗口
        search_start = max(0, core_start_idx - self.pre_jump_window)
        search_end = core_start_idx
        
        best_takeoff_idx = core_start_idx
        
        for i in range(search_end - 1, search_start - 1, -1):  # 从核心点向前搜索
            data = sensor_data[i]
            
            # 1. 重力变化检测（更宽松的阈值）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            gravity_score = min(1.0, z_gravity_deviation / self.gravity_threshold_loose)
            
            # 2. 角速度预备动作检测
            angular_velocity = data.total_angular_velocity()
            angular_score = min(1.0, angular_velocity / self.angular_threshold_loose)
            
            # 3. 加速度梯度检测（起跳准备的加速度变化）
            gradient_score = 0.0
            if i < len(sensor_data) - 1:
                az_change = abs(sensor_data[i+1].az - data.az)
                gradient_score = min(1.0, az_change / self.gradient_threshold)
            
            # 4. 运动趋势检测（向上加速趋势）
            momentum_score = 0.0
            if i < len(sensor_data) - 2:
                # 检测连续的向上加速度增加
                az_curr = data.az
                az_next1 = sensor_data[i+1].az
                az_next2 = sensor_data[i+2].az
                
                if az_next1 > az_curr and az_next2 > az_next1:
                    momentum_score = 0.8
                elif az_next1 > az_curr:
                    momentum_score = 0.4
            
            # 综合评分
            total_score = (gravity_score * 0.3 + 
                          angular_score * 0.3 + 
                          gradient_score * 0.2 + 
                          momentum_score * 0.2)
            
            # 如果评分超过阈值，更新起跳点
            if total_score >= self.boundary_extension_threshold:
                best_takeoff_idx = i
            else:
                # 如果评分太低，停止向前搜索
                break
        
        return best_takeoff_idx
    
    def detect_landing_completion(self, sensor_data: List[SensorData], 
                                 core_end_idx: int) -> int:
        """检测落地完成阶段，向后扩展落地点"""
        
        # 向后搜索窗口
        search_start = core_end_idx
        search_end = min(len(sensor_data), core_end_idx + self.post_landing_window)
        
        best_landing_idx = core_end_idx
        
        for i in range(search_start + 1, search_end):  # 从核心点向后搜索
            data = sensor_data[i]
            
            # 1. 重力恢复检测（渐进式）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            # 重力偏差越小，说明越接近正常状态，但仍可能在落地缓冲中
            gravity_score = max(0.0, 1.0 - z_gravity_deviation / self.gravity_threshold_loose)
            
            # 2. 角速度衰减检测
            angular_velocity = data.total_angular_velocity()
            angular_score = min(1.0, angular_velocity / self.angular_threshold_loose)
            
            # 3. 冲击检测（落地时的加速度突变）
            impact_score = 0.0
            if i > 0:
                az_change = abs(data.az - sensor_data[i-1].az)
                impact_score = min(1.0, az_change / self.gradient_threshold)
            
            # 4. 稳定性检测（落地后的稳定趋势）
            stability_score = 0.0
            if i > 1:
                # 检测加速度是否趋于稳定
                az_prev2 = sensor_data[i-2].az
                az_prev1 = sensor_data[i-1].az
                az_curr = data.az
                
                # 如果加速度变化在减小，说明在趋于稳定
                change1 = abs(az_prev1 - az_prev2)
                change2 = abs(az_curr - az_prev1)
                
                if change2 < change1:
                    stability_score = 0.3
                elif change2 > change1 * 1.5:  # 如果变化增大，可能还在落地过程中
                    stability_score = 0.7
            
            # 综合评分
            total_score = (gravity_score * 0.2 +      # 重力恢复权重较低
                          angular_score * 0.3 +       # 角速度仍然重要
                          impact_score * 0.3 +        # 冲击检测重要
                          stability_score * 0.2)      # 稳定性检测
            
            # 如果评分超过阈值，更新落地点
            if total_score >= self.boundary_extension_threshold:
                best_landing_idx = i
            else:
                # 如果连续几个点评分都很低，可能已经完全落地
                if i > core_end_idx + 3:  # 至少检查3个点后再停止
                    break
        
        return best_landing_idx

    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空事件检测"""
        if len(sensor_data) < 10:
            return []
        
        print("🚀 使用改进的边界检测算法...")
        
        # 第一步：使用原算法检测核心滞空区间
        core_events = super().detect_airtime_events(sensor_data)
        print(f"   核心滞空区间: {len(core_events)} 个")
        
        if not core_events:
            return []
        
        # 第二步：对每个核心区间进行边界扩展
        improved_events = []
        
        for i, event in enumerate(core_events):
            print(f"\n🔍 优化事件 {i+1}:")
            
            # 获取核心区间的索引
            core_start_idx = getattr(event, 'start_data_index', None)
            core_end_idx = getattr(event, 'end_data_index', None)
            
            if core_start_idx is None or core_end_idx is None:
                # 如果没有索引信息，尝试通过时间戳查找
                core_start_idx, core_end_idx = self._find_event_indices(sensor_data, event)
            
            if core_start_idx is None or core_end_idx is None:
                print(f"   ⚠️ 无法找到事件索引，跳过优化")
                improved_events.append(event)
                continue
            
            print(f"   核心区间: 索引 {core_start_idx} ~ {core_end_idx}")
            
            # 检测改进的起跳点
            improved_start_idx = self.detect_takeoff_preparation(sensor_data, core_start_idx)
            print(f"   起跳点扩展: {core_start_idx} → {improved_start_idx} (扩展 {core_start_idx - improved_start_idx} 点)")
            
            # 检测改进的落地点
            improved_end_idx = self.detect_landing_completion(sensor_data, core_end_idx)
            print(f"   落地点扩展: {core_end_idx} → {improved_end_idx} (扩展 {improved_end_idx - core_end_idx} 点)")
            
            # 创建改进的事件
            improved_event = self._create_improved_event(
                sensor_data, improved_start_idx, improved_end_idx, event)
            
            improved_events.append(improved_event)
            
            print(f"   改进后持续时间: {improved_event.duration_seconds:.3f}秒 "
                  f"(原: {event.duration_seconds:.3f}秒)")
        
        print(f"\n✅ 边界优化完成，共 {len(improved_events)} 个改进事件")

        # 第三步：合并重叠事件
        merged_events = self.merge_overlapping_events(improved_events, sensor_data)
        print(f"🔗 合并重叠事件后，剩余 {len(merged_events)} 个事件")

        return merged_events
    
    def _find_event_indices(self, sensor_data: List[SensorData], 
                           event: AirtimeEvent) -> Tuple[int, int]:
        """通过时间戳查找事件在数据中的索引"""
        start_idx = None
        end_idx = None
        
        for i, data in enumerate(sensor_data):
            if data.timestamp == event.start_time:
                start_idx = i
            if data.timestamp == event.end_time:
                end_idx = i
        
        return start_idx, end_idx
    
    def _create_improved_event(self, sensor_data: List[SensorData], 
                              start_idx: int, end_idx: int, 
                              original_event: AirtimeEvent) -> AirtimeEvent:
        """创建改进的滞空事件"""
        
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]
        
        # 计算新的持续时间
        duration_ms = self._calculate_duration_ms(start_data, end_data)
        
        # 重新计算滞空特征
        airtime_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(airtime_segment)
        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
        
        # 重新计算旋转指标
        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
        
        # 创建新事件
        improved_event = AirtimeEvent(
            start_time=start_data.timestamp,
            end_time=end_data.timestamp,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=original_event.confidence,  # 保持原置信度
            max_rotation_speed=rotation_metrics['max_rotation_speed'],
            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
            rotation_detected=rotation_metrics['rotation_detected']
        )
        
        # 保存索引信息
        improved_event.start_data_index = start_idx
        improved_event.end_data_index = end_idx
        
        return improved_event

    def merge_overlapping_events(self, events: List[AirtimeEvent],
                                sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """合并重叠的滞空事件"""
        if len(events) <= 1:
            return events

        # 按开始时间排序
        sorted_events = sorted(events, key=lambda e: e.start_time)
        merged_events = []

        i = 0
        while i < len(sorted_events):
            current_event = sorted_events[i]

            # 查找与当前事件重叠的所有事件
            overlapping_events = [current_event]
            j = i + 1

            while j < len(sorted_events):
                next_event = sorted_events[j]

                # 检查时间重叠
                if self._events_overlap(current_event, next_event):
                    overlapping_events.append(next_event)
                    j += 1
                else:
                    break

            # 如果有重叠事件，进行合并
            if len(overlapping_events) > 1:
                merged_event = self._merge_events(overlapping_events, sensor_data)
                merged_events.append(merged_event)
            else:
                merged_events.append(current_event)

            # 跳过已处理的重叠事件
            i = j if len(overlapping_events) > 1 else i + 1

        return merged_events

    def detect_skiing_optimized_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """针对滑雪运动优化的滞空检测算法"""
        if len(sensor_data) < 10:
            return []

        print("🎿 使用滑雪运动优化的滞空检测算法...")

        # 步骤1：分析运动类型（平花 vs 跳台）
        motion_type = self._analyze_skiing_motion_type(sensor_data)
        print(f"   检测到运动类型: {motion_type}")

        # 步骤2：根据运动类型调整检测参数
        self._adjust_parameters_for_motion_type(motion_type)

        # 步骤3：使用多阶段检测策略
        candidate_events = self._multi_stage_airtime_detection(sensor_data, motion_type)
        print(f"   候选滞空事件: {len(candidate_events)} 个")

        # 步骤4：运动特征验证和过滤
        validated_events = self._validate_skiing_airtime_events(candidate_events, sensor_data, motion_type)
        print(f"   验证后滞空事件: {len(validated_events)} 个")

        # 步骤5：边界优化
        optimized_events = []
        for event in validated_events:
            optimized_event = self._optimize_skiing_event_boundaries(event, sensor_data, motion_type)
            optimized_events.append(optimized_event)

        print(f"✅ 滑雪优化检测完成，共 {len(optimized_events)} 个滞空事件")
        return optimized_events

    def _analyze_skiing_motion_type(self, sensor_data: List[SensorData]) -> str:
        """分析滑雪运动类型：平花动作 vs 跳台滑雪"""
        if len(sensor_data) < 5:
            return "unknown"

        # 分析速度特征
        speeds = [data.speed for data in sensor_data if data.speed > 0]
        avg_speed = sum(speeds) / len(speeds) if speeds else 0
        max_speed = max(speeds) if speeds else 0

        # 分析高度变化特征
        heights = [data.humidity / 10.0 for data in sensor_data]
        height_variation = max(heights) - min(heights)

        # 分析角速度特征
        angular_velocities = [data.total_angular_velocity() for data in sensor_data]
        avg_angular = sum(angular_velocities) / len(angular_velocities)
        max_angular = max(angular_velocities)

        # 分析Z轴加速度变化特征
        z_accelerations = [abs(data.az) for data in sensor_data]
        z_variation = max(z_accelerations) - min(z_accelerations)

        # 决策逻辑
        jump_indicators = 0
        flatland_indicators = 0

        # 速度指标
        if avg_speed > self.speed_threshold_for_jump:
            jump_indicators += 2
        else:
            flatland_indicators += 1

        # 高度指标
        if height_variation > self.height_threshold_for_jump:
            jump_indicators += 2
        else:
            flatland_indicators += 1

        # 角速度指标
        if max_angular > 2000:
            jump_indicators += 1
        if avg_angular < 800:
            flatland_indicators += 1

        # Z轴变化指标
        if z_variation > 2000:
            jump_indicators += 1
        else:
            flatland_indicators += 1

        # 综合判断
        if jump_indicators > flatland_indicators:
            return "jump"
        elif flatland_indicators > jump_indicators:
            return "flatland"
        else:
            return "mixed"

    def _adjust_parameters_for_motion_type(self, motion_type: str):
        """根据运动类型调整检测参数"""
        if motion_type == "flatland":
            # 平花动作：更敏感的检测
            self.gravity_threshold = self.skiing_gravity_threshold
            self.angular_velocity_threshold = self.flatland_angular_threshold
            self.min_airtime_ms = self.min_flatland_airtime_ms
            self.max_airtime_ms = self.max_flatland_airtime_ms
            self.weightless_ratio = 0.25  # 更宽松的失重判断
            self.z_spike_ratio = 0.4      # 更敏感的突变检测

        elif motion_type == "jump":
            # 跳台滑雪：更严格的检测
            self.gravity_threshold = self.skiing_gravity_threshold
            self.angular_velocity_threshold = self.jump_angular_threshold
            self.min_airtime_ms = self.min_jump_airtime_ms
            self.max_airtime_ms = self.max_jump_airtime_ms
            self.weightless_ratio = 0.15  # 更严格的失重判断
            self.z_spike_ratio = 0.6      # 标准突变检测

        else:  # mixed or unknown
            # 混合模式：平衡的检测参数
            self.gravity_threshold = self.skiing_gravity_threshold
            self.angular_velocity_threshold = self.skiing_angular_threshold
            self.min_airtime_ms = 100
            self.max_airtime_ms = 3000
            self.weightless_ratio = 0.2
            self.z_spike_ratio = 0.5

    def _multi_stage_airtime_detection(self, sensor_data: List[SensorData], motion_type: str) -> List[AirtimeEvent]:
        """多阶段滞空检测"""
        # 阶段1：基础滞空检测
        basic_events = super().detect_airtime_events(sensor_data)

        # 阶段2：补充检测（捕获可能遗漏的短时滞空）
        supplementary_events = self._detect_supplementary_airtime(sensor_data, motion_type)

        # 阶段3：合并和去重
        all_events = basic_events + supplementary_events
        merged_events = self._merge_nearby_events(all_events, sensor_data)

        return merged_events

    def _detect_supplementary_airtime(self, sensor_data: List[SensorData], motion_type: str) -> List[AirtimeEvent]:
        """补充检测短时或微弱的滞空事件"""
        if len(sensor_data) < 5:
            return []

        supplementary_events = []

        # 使用更敏感的参数进行检测
        sensitive_gravity_threshold = self.gravity_threshold * 0.7
        sensitive_angular_threshold = self.angular_velocity_threshold * 0.6

        # 平滑数据
        smoothed_z = self._smooth_z_acceleration(sensor_data, window_size=3)

        # 检测模式
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]
            angular_velocity = data.total_angular_velocity()

            # 更敏感的滞空条件
            z_deviation = abs(abs(z_smooth) - self.standard_gravity)
            is_sensitive_gravity_anomaly = z_deviation > sensitive_gravity_threshold
            is_sensitive_angular = angular_velocity > sensitive_angular_threshold

            # 特殊的平花动作检测
            if motion_type == "flatland":
                # 平花动作可能有连续的小幅滞空
                is_micro_weightless = abs(z_smooth) < (self.standard_gravity * 0.3)
                is_rotation_active = angular_velocity > 200

                is_airborne = (is_sensitive_gravity_anomaly and is_sensitive_angular) or \
                             (is_micro_weightless and is_rotation_active)
            else:
                # 跳台动作检测
                is_airborne = is_sensitive_gravity_anomaly and is_sensitive_angular

            if is_airborne and not in_airtime:
                in_airtime = True
                airtime_start = data
                airtime_start_idx = i

            elif not is_airborne and in_airtime:
                in_airtime = False

                if airtime_start:
                    duration_ms = self._calculate_duration_ms(airtime_start, data)

                    # 更宽松的时间过滤
                    min_duration = 50 if motion_type == "flatland" else 100
                    max_duration = self.max_airtime_ms

                    if min_duration <= duration_ms <= max_duration:
                        # 创建补充事件
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        # 简化的置信度计算
                        confidence = self._calculate_simple_confidence(airtime_segment) * 0.8  # 降低置信度

                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=data.timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence
                        )

                        event.start_data_index = airtime_start_idx
                        event.end_data_index = i
                        event.is_supplementary = True  # 标记为补充检测

                        supplementary_events.append(event)

        return supplementary_events

    def _merge_nearby_events(self, events: List[AirtimeEvent], sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """合并相近的滞空事件"""
        if len(events) <= 1:
            return events

        # 按开始时间排序
        sorted_events = sorted(events, key=lambda e: e.start_time)
        merged_events = []

        i = 0
        while i < len(sorted_events):
            current_event = sorted_events[i]

            # 查找需要合并的相邻事件
            events_to_merge = [current_event]
            j = i + 1

            while j < len(sorted_events):
                next_event = sorted_events[j]

                # 计算事件间隔
                gap_ms = (next_event.start_time - current_event.end_time).total_seconds() * 1000

                # 如果间隔很小（小于200ms），考虑合并
                if gap_ms < 200:
                    events_to_merge.append(next_event)
                    current_event = next_event  # 更新当前事件为最新的事件
                    j += 1
                else:
                    break

            # 如果有多个事件需要合并
            if len(events_to_merge) > 1:
                merged_event = self._create_merged_event(events_to_merge, sensor_data)
                merged_events.append(merged_event)
            else:
                merged_events.append(current_event)

            i = j if len(events_to_merge) > 1 else i + 1

        return merged_events

    def _validate_skiing_airtime_events(self, events: List[AirtimeEvent],
                                       sensor_data: List[SensorData], motion_type: str) -> List[AirtimeEvent]:
        """验证滑雪滞空事件的有效性"""
        validated_events = []

        for event in events:
            if self._is_valid_skiing_airtime(event, sensor_data, motion_type):
                validated_events.append(event)
            else:
                print(f"过滤无效滞空事件: 持续时间{event.duration_seconds:.3f}s, 置信度{event.confidence:.3f}")

        return validated_events

    def _is_valid_skiing_airtime(self, event: AirtimeEvent, sensor_data: List[SensorData], motion_type: str) -> bool:
        """检查滞空事件是否符合滑雪运动特征"""
        # 基础时间检查
        if motion_type == "flatland":
            if not (self.min_flatland_airtime_ms <= event.duration_ms <= self.max_flatland_airtime_ms):
                return False
        elif motion_type == "jump":
            if not (self.min_jump_airtime_ms <= event.duration_ms <= self.max_jump_airtime_ms):
                return False

        # 置信度检查
        min_confidence = 0.3 if motion_type == "flatland" else 0.4
        if event.confidence < min_confidence:
            return False

        # 运动特征检查
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]

            # 检查是否有足够的运动特征
            if not self._has_sufficient_motion_characteristics(airtime_segment, motion_type):
                return False

        return True

    def _has_sufficient_motion_characteristics(self, airtime_segment: List[SensorData], motion_type: str) -> bool:
        """检查是否有足够的运动特征"""
        if len(airtime_segment) < 2:
            return False

        # 计算运动特征
        angular_velocities = [data.total_angular_velocity() for data in airtime_segment]
        z_accelerations = [abs(data.az) for data in airtime_segment]

        avg_angular = sum(angular_velocities) / len(angular_velocities)
        max_angular = max(angular_velocities)
        z_variation = max(z_accelerations) - min(z_accelerations)

        if motion_type == "flatland":
            # 平花动作：需要一定的旋转或Z轴变化
            return avg_angular > 150 or max_angular > 400 or z_variation > 300
        elif motion_type == "jump":
            # 跳台动作：需要明显的运动特征
            return avg_angular > 300 or max_angular > 800 or z_variation > 500
        else:
            # 混合模式：中等要求
            return avg_angular > 200 or max_angular > 600 or z_variation > 400

    def _optimize_skiing_event_boundaries(self, event: AirtimeEvent,
                                        sensor_data: List[SensorData], motion_type: str) -> AirtimeEvent:
        """优化滑雪滞空事件的边界"""
        if not (hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index')):
            return event

        original_start = event.start_data_index
        original_end = event.end_data_index

        # 根据运动类型调整边界优化策略
        if motion_type == "flatland":
            # 平花动作：更激进的边界扩展
            optimized_start = self._extend_flatland_start_boundary(sensor_data, original_start)
            optimized_end = self._extend_flatland_end_boundary(sensor_data, original_end)
        elif motion_type == "jump":
            # 跳台动作：保守的边界扩展
            optimized_start = self._extend_jump_start_boundary(sensor_data, original_start)
            optimized_end = self._extend_jump_end_boundary(sensor_data, original_end)
        else:
            # 混合模式：标准边界扩展
            optimized_start = self.detect_takeoff_preparation(sensor_data, original_start)
            optimized_end = self.detect_landing_completion(sensor_data, original_end)

        # 创建优化后的事件
        if optimized_start != original_start or optimized_end != original_end:
            return self._create_improved_event(sensor_data, optimized_start, optimized_end, event)

        return event

    def _extend_flatland_start_boundary(self, sensor_data: List[SensorData], start_idx: int) -> int:
        """扩展平花动作的起始边界"""
        search_start = max(0, start_idx - 12)  # 更大的搜索窗口
        best_start = start_idx

        for i in range(start_idx - 1, search_start - 1, -1):
            if i < 0:
                break

            data = sensor_data[i]

            # 平花动作的准备特征：轻微的角速度增加或Z轴变化
            angular_vel = data.total_angular_velocity()
            z_deviation = abs(abs(data.az) - self.standard_gravity)

            prep_score = 0.0
            if angular_vel > 100:  # 轻微旋转
                prep_score += 0.3
            if z_deviation > 150:  # 轻微重力偏差
                prep_score += 0.3

            if prep_score > 0.2:  # 更低的阈值
                best_start = i
            else:
                break

        return best_start

    def _extend_flatland_end_boundary(self, sensor_data: List[SensorData], end_idx: int) -> int:
        """扩展平花动作的结束边界"""
        search_end = min(len(sensor_data), end_idx + 12)
        best_end = end_idx

        for i in range(end_idx + 1, search_end):
            if i >= len(sensor_data):
                break

            data = sensor_data[i]

            # 平花动作的结束特征
            angular_vel = data.total_angular_velocity()
            z_deviation = abs(abs(data.az) - self.standard_gravity)

            end_score = 0.0
            if angular_vel > 100:
                end_score += 0.3
            if z_deviation > 150:
                end_score += 0.3

            if end_score > 0.2:
                best_end = i
            else:
                break

        return best_end

    def _extend_jump_start_boundary(self, sensor_data: List[SensorData], start_idx: int) -> int:
        """扩展跳台动作的起始边界"""
        search_start = max(0, start_idx - 8)
        best_start = start_idx

        for i in range(start_idx - 1, search_start - 1, -1):
            if i < 0:
                break

            data = sensor_data[i]

            # 跳台动作的准备特征：明显的加速度变化
            z_deviation = abs(abs(data.az) - self.standard_gravity)
            angular_vel = data.total_angular_velocity()

            prep_score = 0.0
            if z_deviation > self.gradient_threshold:
                prep_score += 0.4
            if angular_vel > self.angular_threshold_loose:
                prep_score += 0.3

            if prep_score > self.boundary_extension_threshold:
                best_start = i
            else:
                break

        return best_start

    def _extend_jump_end_boundary(self, sensor_data: List[SensorData], end_idx: int) -> int:
        """扩展跳台动作的结束边界"""
        search_end = min(len(sensor_data), end_idx + 8)
        best_end = end_idx

        for i in range(end_idx + 1, search_end):
            if i >= len(sensor_data):
                break

            data = sensor_data[i]

            # 跳台动作的结束特征
            z_deviation = abs(abs(data.az) - self.standard_gravity)
            angular_vel = data.total_angular_velocity()

            end_score = 0.0
            if z_deviation > self.gradient_threshold:
                end_score += 0.4
            if angular_vel > self.angular_threshold_loose:
                end_score += 0.3

            if end_score > self.boundary_extension_threshold:
                best_end = i
            else:
                break

        return best_end

    def _create_merged_event(self, events_to_merge: List[AirtimeEvent],
                           sensor_data: List[SensorData]) -> AirtimeEvent:
        """创建合并后的滞空事件"""
        # 找到最早开始和最晚结束
        earliest_start = min(event.start_time for event in events_to_merge)
        latest_end = max(event.end_time for event in events_to_merge)

        # 找到对应的数据索引
        start_idx = None
        end_idx = None

        for i, data in enumerate(sensor_data):
            if data.timestamp == earliest_start and start_idx is None:
                start_idx = i
            if data.timestamp == latest_end:
                end_idx = i

        if start_idx is None or end_idx is None:
            return events_to_merge[0]  # 返回第一个事件作为备选

        # 重新计算合并后的特征
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]
        duration_ms = self._calculate_duration_ms(start_data, end_data)

        airtime_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(airtime_segment)
        max_acceleration = max(d.total_acceleration() for d in airtime_segment)

        # 使用最高置信度
        max_confidence = max(event.confidence for event in events_to_merge)

        merged_event = AirtimeEvent(
            start_time=earliest_start,
            end_time=latest_end,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=max_confidence
        )

        merged_event.start_data_index = start_idx
        merged_event.end_data_index = end_idx
        merged_event.is_merged = True

        return merged_event

    def _calculate_simple_confidence(self, airtime_segment: List[SensorData]) -> float:
        """简化的置信度计算"""
        if len(airtime_segment) < 2:
            return 0.0

        # 计算基础特征
        angular_velocities = [data.total_angular_velocity() for data in airtime_segment]
        z_accelerations = [abs(data.az) for data in airtime_segment]

        avg_angular = sum(angular_velocities) / len(angular_velocities)
        max_angular = max(angular_velocities)
        z_variation = max(z_accelerations) - min(z_accelerations)

        # 计算置信度分数
        confidence = 0.0

        # 角速度贡献
        if avg_angular > 200:
            confidence += 0.3
        if max_angular > 500:
            confidence += 0.2

        # Z轴变化贡献
        if z_variation > 300:
            confidence += 0.3
        if z_variation > 600:
            confidence += 0.2

        # 持续时间贡献
        duration_points = len(airtime_segment)
        if duration_points >= 3:
            confidence += 0.2

        return min(confidence, 1.0)
    def _events_overlap(self, event1: AirtimeEvent, event2: AirtimeEvent) -> bool:
        """检查两个事件是否重叠"""
        # 事件1的结束时间是否晚于事件2的开始时间
        return event1.end_time >= event2.start_time

    def _merge_events(self, events: List[AirtimeEvent],
                     sensor_data: List[SensorData]) -> AirtimeEvent:
        """合并多个重叠事件为一个事件"""

        # 找到最早的开始时间和最晚的结束时间
        earliest_start = min(event.start_time for event in events)
        latest_end = max(event.end_time for event in events)

        # 找到对应的数据索引
        start_idx = None
        end_idx = None

        for i, data in enumerate(sensor_data):
            if data.timestamp == earliest_start:
                start_idx = i
            if data.timestamp == latest_end:
                end_idx = i

        # 如果找不到精确匹配，使用最接近的索引
        if start_idx is None or end_idx is None:
            for event in events:
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    if start_idx is None:
                        start_idx = event.start_data_index
                    else:
                        start_idx = min(start_idx, event.start_data_index)

                    if end_idx is None:
                        end_idx = event.end_data_index
                    else:
                        end_idx = max(end_idx, event.end_data_index)

        # 计算合并后的特征
        duration_ms = self._calculate_duration_ms(sensor_data[start_idx], sensor_data[end_idx])

        # 重新计算合并区间的特征
        merged_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(merged_segment)
        max_acceleration = max(d.total_acceleration() for d in merged_segment)

        # 使用最高的置信度
        max_confidence = max(event.confidence for event in events)

        # 重新计算旋转指标
        rotation_metrics = self._calculate_rotation_metrics(merged_segment)

        # 创建合并后的事件
        merged_event = AirtimeEvent(
            start_time=earliest_start,
            end_time=latest_end,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=max_confidence,
            max_rotation_speed=rotation_metrics['max_rotation_speed'],
            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
            rotation_detected=rotation_metrics['rotation_detected']
        )

        # 保存索引信息
        merged_event.start_data_index = start_idx
        merged_event.end_data_index = end_idx

        return merged_event

    def _calculate_detailed_rotation(self, airtime_segment: List[SensorData],
                                   duration_seconds: float) -> Dict:
        """计算详细的转体信息，包括分段转速"""
        yaw_angles = [data.z for data in airtime_segment]

        # 计算累积旋转角度
        cumulative_rotation = 0.0
        angle_changes = []

        for j in range(1, len(yaw_angles)):
            angle_change = yaw_angles[j] - yaw_angles[j-1]

            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360

            cumulative_rotation += angle_change
            angle_changes.append(angle_change)

        # 转体角度和基本信息
        total_rotation_angle = abs(cumulative_rotation)
        avg_rotation_speed = total_rotation_angle / 360.0 / duration_seconds if duration_seconds > 0 else 0

        # 判断转动方向
        if cumulative_rotation > 50:
            rotation_direction = 'clockwise'
        elif cumulative_rotation < -50:
            rotation_direction = 'counterclockwise'
        else:
            rotation_direction = 'none'
            total_rotation_angle = 0.0
            avg_rotation_speed = 0.0

        # 计算分段转速（当转体角度大于360度时）
        first_half_speed = 0.0
        second_half_speed = 0.0

        if total_rotation_angle > 360 and len(angle_changes) > 2:
            # 目标角度：总角度的一半
            target_half_angle = total_rotation_angle / 2.0

            # 寻找前半段结束点（累积角度达到一半时）
            cumulative_angle = 0.0
            split_point = 0

            for i, angle_change in enumerate(angle_changes):
                cumulative_angle += abs(angle_change)
                if cumulative_angle >= target_half_angle:
                    split_point = i + 1
                    break

            # 确保分割点有效
            if split_point == 0:
                split_point = len(angle_changes) // 2
            elif split_point >= len(angle_changes):
                split_point = len(angle_changes) - 1

            # 分割角度变化数组
            first_half_changes = angle_changes[:split_point]
            second_half_changes = angle_changes[split_point:]

            # 计算前半段和后半段的实际角度
            first_half_angle = sum(abs(change) for change in first_half_changes)
            second_half_angle = sum(abs(change) for change in second_half_changes)

            # 计算时间分配（按分割点比例分配）
            first_half_duration = (split_point / len(angle_changes)) * duration_seconds
            second_half_duration = duration_seconds - first_half_duration

            # 计算分段转速
            if first_half_duration > 0:
                first_half_speed = first_half_angle / 360.0 / first_half_duration
            if second_half_duration > 0:
                second_half_speed = second_half_angle / 360.0 / second_half_duration

        return {
            'total_angle': total_rotation_angle,
            'avg_speed': avg_rotation_speed,
            'direction': rotation_direction,
            'first_half_speed': first_half_speed,
            'second_half_speed': second_half_speed,
            'has_split_analysis': total_rotation_angle > 360
        }

    def _calculate_horizontal_distance(self, airtime_segment: List[SensorData],
                                     duration_seconds: float) -> str:
        """计算水平距离，使用速度speed与时间段相乘"""

        # 获取所有速度值
        speeds = [data.speed for data in airtime_segment]

        # 检查是否有非零速度
        non_zero_speeds = [s for s in speeds if s != 0]

        if not non_zero_speeds:
            return "null"

        # 计算平均速度（排除零值）
        avg_speed = sum(non_zero_speeds) / len(non_zero_speeds)/3.6/3.6

        # 计算水平距离 = 平均速度 × 时间
        horizontal_distance = avg_speed * duration_seconds

        return f"{horizontal_distance:.2f}m"


def main():
    """改进的滑雪滞空检测器"""
    # 创建检测器
    detector = ImprovedBoundaryDetector()

    # 加载数据
    sensor_data = detector.load_sensor_data(r'E:\cyl\成都跳台\data_02.txt')

    if sensor_data:
        # 使用滑雪运动优化的检测算法
        improved_events = detector.detect_skiing_optimized_airtime_events(sensor_data)

        # 过滤高置信度事件
        high_confidence_events = [event for event in improved_events if event.confidence >= 0.5]

        if high_confidence_events:
            print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'水平距离':<10} {'置信度':<6} {'转体角度(°)':<10} {'转体速度(圈/s)':<12} {'转动方向':<10}")
            print("-" * 120)

            for i, event in enumerate(high_confidence_events, 1):
                # 获取精确时间戳
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    start_data = sensor_data[event.start_data_index]
                    end_data = sensor_data[event.end_data_index]

                    start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
                    end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"

                    # 计算转体信息
                    airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
                    rotation_info = detector._calculate_detailed_rotation(airtime_segment, event.duration_seconds)

                    rotation_angle = rotation_info['total_angle']
                    rotation_speed = rotation_info['avg_speed']
                    rotation_direction = rotation_info['direction']
                    first_half_speed = rotation_info['first_half_speed']
                    second_half_speed = rotation_info['second_half_speed']
                    has_split_analysis = rotation_info['has_split_analysis']

                    # 计算水平距离
                    horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
                else:
                    start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
                    end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
                    rotation_angle = 0.0
                    rotation_speed = 0.0
                    rotation_direction = 'none'
                    horizontal_distance = "null"
                    has_split_analysis = False

                # 构建转体速度显示字符串
                if has_split_analysis:
                    speed_display = f"{rotation_speed:.2f}(前:{first_half_speed:.2f}|后:{second_half_speed:.2f})"
                else:
                    speed_display = f"{rotation_speed:.2f}"

                # 缩短时间戳显示（只显示时分秒.毫秒）
                start_time_short = start_timestamp.split(' ')[1]  # 只取时间部分
                end_time_short = end_timestamp.split(' ')[1]      # 只取时间部分

                print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
                      f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
                      f"{horizontal_distance:<10} {event.confidence:<6.3f} {rotation_angle:<10.1f} "
                      f"{speed_display:<12} {rotation_direction:<10}")

                # 如果有分段分析，添加详细信息
                if has_split_analysis:
                    print(f"     └─ 分段转速详情: 前半段 {first_half_speed:.2f}圈/s, 后半段 {second_half_speed:.2f}圈/s")
        else:
            print("未检测到高置信度的滞空事件")
    else:
        print("数据加载失败，请检查文件路径")


if __name__ == "__main__":
    main()
