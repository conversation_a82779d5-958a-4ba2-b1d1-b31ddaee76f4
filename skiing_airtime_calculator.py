#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滑雪滞空时间计算算法
基于传感器数据（加速度计、陀螺仪、磁力计等）计算滑雪过程中的滞空时间
"""

import re
import math
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import numpy as np


@dataclass
class SensorData:
    """传感器数据结构"""
    timestamp: datetime
    milliseconds: int
    # 位置坐标 (可能是相对位置)
    x: float
    y: float  
    z: float
    P: int  # 可能是压力或位置指示器
    
    # 加速度计数据 (mg 单位)
    ax: float  # X轴加速度
    ay: float  # Y轴加速度
    az: float  # Z轴加速度
    
    # 陀螺仪数据 (度/秒)
    gx: float  # X轴角速度
    gy: float  # Y轴角速度
    gz: float  # Z轴角速度
    
    # 磁力计数据
    mx: float  # X轴磁场
    my: float  # Y轴磁场
    mz: float  # Z轴磁场
    
    # 其他传感器数据
    temperature: float  # 温度
    humidity: float     # 湿度
    speed: float        # 速度
    fs: float          # 可能是采样频率
    longitude: float   # 经度
    latitude: float    # 纬度
    gps_height: float  # GPS高度
    
    def total_acceleration(self) -> float:
        """计算总加速度大小"""
        return math.sqrt(self.ax**2 + self.ay**2 + self.az**2)
    
    def total_angular_velocity(self) -> float:
        """计算总角速度大小"""
        return math.sqrt(self.gx**2 + self.gy**2 + self.gz**2)


@dataclass
class AirtimeEvent:
    """滞空事件"""
    start_time: datetime
    end_time: datetime
    duration_ms: int
    max_height_change: float
    max_acceleration: float
    confidence: float  # 置信度 (0-1)
    max_rotation_speed: float = 0.0      # 最大旋转速度 (°/s)
    avg_rotation_speed: float = 0.0      # 平均旋转速度 (°/s)
    rotation_detected: bool = False      # 是否检测到旋转

    @property
    def duration_seconds(self) -> float:
        return self.duration_ms / 1000.0


class SkiingAirtimeCalculator:
    """滑雪滞空时间计算器"""
    
    def __init__(self, 
                 gravity_threshold: float = 800,      # 重力阈值 (mg)
                 min_airtime_ms: int = 100,           # 最小滞空时间 (毫秒)
                 max_airtime_ms: int = 10000,         # 最大滞空时间 (毫秒)
                 angular_velocity_threshold: float = 1000,  # 角速度阈值
                 height_change_threshold: float = 0.5):     # 高度变化阈值
        
        self.gravity_threshold = gravity_threshold
        self.min_airtime_ms = min_airtime_ms
        self.max_airtime_ms = max_airtime_ms
        self.angular_velocity_threshold = angular_velocity_threshold
        self.height_change_threshold = height_change_threshold
        
        # 标准重力加速度 (mg)
        self.standard_gravity = 980
        
    def parse_sensor_line(self, line: str) -> Optional[SensorData]:
        """解析传感器数据行（支持新旧两种格式）"""
        try:
            # 处理null值的辅助函数
            def parse_value(value_str, default=0.0):
                if value_str == '(null)':
                    return default
                return float(value_str)

            # 尝试格式1：data_01.txt格式（包含ca、cs、bt字段，支持(null)值）
            pattern_data01 = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (\(null\)|-?\d+), y: (\(null\)|-?\d+), z: (\(null\)|-?\d+), P: (-?\d+), ax: (\(null\)|-?\d+), ay: (\(null\)|-?\d+), az: (\(null\)|-?\d+), gx: (\(null\)|-?\d+), gy: (\(null\)|-?\d+), gz: (\(null\)|-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (\(null\)|-?\d+), h: (\(null\)|-?\d+), s: (-?\d+\.?\d*), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (\(null\)|-?\d+), ca:\(null\), cs:\(null\), bt:(-?\d+)'

            match = re.match(pattern_data01, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=parse_value(groups[2]),
                    y=parse_value(groups[3]),
                    z=parse_value(groups[4]),
                    P=int(groups[5]),
                    ax=parse_value(groups[6]),
                    ay=parse_value(groups[7]),
                    az=parse_value(groups[8]),
                    gx=parse_value(groups[9]),
                    gy=parse_value(groups[10]),
                    gz=parse_value(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=parse_value(groups[15]),
                    humidity=parse_value(groups[16]),  # h字段
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    longitude=float(groups[20]),
                    latitude=float(groups[21]),
                    gps_height=parse_value(groups[22])
                )

            # 尝试格式2：720.txt格式（包含bt字段）
            pattern_720txt = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\), bt:(-?\d+)'

            match = re.match(pattern_720txt, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=float(groups[12]),
                    my=float(groups[13]),
                    mz=float(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),  # h字段
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    longitude=float(groups[20]),
                    latitude=float(groups[21]),
                    gps_height=float(groups[22])
                )

            # 尝试格式3：0.txt格式（不包含fs和as字段）
            pattern_0txt = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'

            match = re.match(pattern_0txt, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=float(groups[12]),
                    my=float(groups[13]),
                    mz=float(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),  # h字段
                    speed=float(groups[17]),
                    fs=0.0,  # 0.txt格式没有fs字段，设为默认值
                    longitude=float(groups[18]),
                    latitude=float(groups[19]),
                    gps_height=float(groups[20])
                )

            # 尝试格式4：包含fs和as字段的格式
            pattern_with_fs_as = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'

            match = re.match(pattern_with_fs_as, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    # groups[19] 是as字段，暂时跳过
                    longitude=float(groups[20]),
                    latitude=float(groups[21]),
                    gps_height=float(groups[22]),
                )

            # 尝试旧格式（不包含as字段）
            old_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'

            match = re.match(old_pattern, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    longitude=float(groups[19]),
                    latitude=float(groups[20]),
                    gps_height=float(groups[21])
                )

            # 所有格式都不匹配
            return None

        except (ValueError, IndexError) as e:
            print(f"解析数据行失败: {line[:50]}... 错误: {e}")
            return None
    
    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """从文件加载传感器数据"""
        sensor_data = []
        failed_lines = 0

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        data = self.parse_sensor_line(line)
                        if data:
                            sensor_data.append(data)
                        else:
                            failed_lines += 1
                            if line_num <= 10:  # 只对前10行显示警告
                                print(f"第{line_num}行解析失败: {line[:50]}...")

        except FileNotFoundError:
            print(f"文件未找到: {filename}")
            return []
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return []

        print(f"成功加载 {len(sensor_data)} 条传感器数据")
        if failed_lines > 0:
            print(f"解析失败的行数: {failed_lines}")
        return sensor_data

    def find_data_files(self, directory: str = '.') -> List[str]:
        """查找目录中的传感器数据文件"""
        import glob
        import os

        # 常见的数据文件模式
        patterns = [
            '*.txt',
            '*judy*.txt',
            '*新疆*.txt',
            '*sensor*.txt',
            '*data*.txt'
        ]

        data_files = []
        for pattern in patterns:
            files = glob.glob(os.path.join(directory, pattern))
            data_files.extend(files)

        # 去重并排序
        data_files = sorted(list(set(data_files)))
        return data_files

    def _get_real_timestamp_from_event(self, sensor_data: List[SensorData], target_time) -> str:
        """获取真实的毫秒时间戳"""
        # 找到最接近目标时间的传感器数据点
        closest_data = None
        min_diff = float('inf')

        for data in sensor_data:
            diff = abs((data.timestamp - target_time).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_data = data

        if closest_data:
            # 使用传感器数据中的真实毫秒值
            return f"{closest_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{closest_data.milliseconds:03d}"
        else:
            # 如果找不到，使用默认格式
            return target_time.strftime('%Y-%m-%d %H:%M:%S.000')
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """检测滞空事件"""
        if len(sensor_data) < 2:
            return []

        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, data in enumerate(sensor_data):
            # 使用Z轴加速度检测重力变化（更准确）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)

            # 检测失重状态（Z轴加速度接近0，表示自由落体）
            z_weightless = abs(data.az) < (self.standard_gravity * 0.2)  # 更严格的失重判断

            # 检测Z轴加速度突变（起跳或落地）
            if i > 0:
                prev_az = abs(sensor_data[i-1].az)
                z_acceleration_change = abs(abs(data.az) - prev_az)
                z_spike = z_acceleration_change > (self.standard_gravity * 0.8)
            else:
                z_spike = False

            # 计算角速度
            angular_velocity = data.total_angular_velocity()

            # 更精确的滞空判断条件（需要同时满足多个条件）
            is_airborne = (
                (z_gravity_deviation > self.gravity_threshold and
                 angular_velocity > self.angular_velocity_threshold * 0.5) or  # Z轴偏差+适度角速度
                (z_weightless and angular_velocity > self.angular_velocity_threshold * 0.3) or  # 失重+轻微旋转
                (z_spike and angular_velocity > self.angular_velocity_threshold * 0.7)  # Z轴突变+较高角速度
            )
            
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = data
                airtime_start_idx = i

            if not is_airborne and in_airtime:
                landing_spike =abs(data.az) >self.standard_gravity
                if landing_spike:
                    in_airtime =False

            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                
                if airtime_start:
                    # 计算滞空持续时间
                    duration_ms = self._calculate_duration_ms(airtime_start, data)
                    
                    # 过滤掉太短或太长的滞空事件
                    if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                        # 计算滞空期间的统计数据
                        # airtime_segment= sensor_data[airtime_start_idx:i+1]
                        airtime_segment = sensor_data[airtime_start_idx:i+1]
                        max_height_change = self._calculate_max_height_change(airtime_segment)
                        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                        confidence = self._calculate_confidence(airtime_segment, duration_ms)

                        # 计算旋转相关指标
                        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                        event = AirtimeEvent(
                            start_time=airtime_start.timestamp,
                            end_time=data.timestamp,
                            duration_ms=duration_ms,
                            max_height_change=max_height_change,
                            max_acceleration=max_acceleration,
                            confidence=confidence,
                            max_rotation_speed=rotation_metrics['max_rotation_speed'],
                            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                            rotation_detected=rotation_metrics['rotation_detected']
                        )
                        
                        airtime_events.append(event)
                
                airtime_start = None
                airtime_start_idx = 0
        
        return airtime_events

    # def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
    # #"""优化后的滞空事件检测：多条件联合判断 + 状态机模型"""
    #     if len(sensor_data) < 2:
    #         return []

    #     airtime_events = []
    #     state = "ground"  # 状态机：ground -> launching -> airtime -> landing
    #     airtime_start = None
    #     airtime_start_idx = 0

    #     for i, data in enumerate(sensor_data):
    #         az = data.az  # Z轴加速度（向上为正）
    #         total_accel = data.total_acceleration()
    #         angular_velocity = data.total_angular_velocity()

    #         # 1. 计算动态阈值（基于滑动窗口的噪声抵抗）
    #         window_size = 5
    #         if i >= window_size:
    #             window_az = [d.az for d in sensor_data[i-window_size:i]]
    #             az_std = np.std(window_az)  # Z轴加速度标准差
    #             dynamic_threshold = max(self.gravity_threshold, az_std * 2.0)
    #         else:
    #             dynamic_threshold = self.gravity_threshold

    #         # 2. 状态机逻辑
    #         if state == "ground":
    #             # 起跳条件：Z轴加速度突增（超过动态阈值） + 角速度变化
    #             if (az > self.standard_gravity + dynamic_threshold and 
    #                 angular_velocity > self.angular_velocity_threshold * 0.5):
    #                 state = "launching"
    #                 airtime_start = data
    #                 airtime_start_idx = i

    #         elif state == "launching":
    #             # 过渡到滞空条件：Z轴接近失重（自由落体）
    #             if abs(az) < self.standard_gravity * 0.3:
    #                 state = "airtime"

    #         elif state == "airtime":
    #             # 落地条件：Z轴加速度突增（冲击力） + 角速度降低
    #             if (abs(az) > self.standard_gravity * 1.5 and 
    #                 angular_velocity < self.angular_velocity_threshold * 0.3):
    #                 state = "landing"

    #         elif state == "landing":
    #             # 确认落地后返回地面状态
    #             if abs(az - self.standard_gravity) < dynamic_threshold * 0.5:
    #                 state = "ground"
                    
    #                 # 验证滞空事件的物理合理性
    #                 duration_ms = self._calculate_duration_ms(airtime_start, data)
    #                 airtime_segment = sensor_data[airtime_start_idx:i+1]
    #                 max_height_change = self._calculate_max_height_change(airtime_segment)
                    
    #                 # 过滤无效事件（如短时噪声或长时异常）
    #                 if (self.min_airtime_ms <= duration_ms <= self.max_airtime_ms and
    #                     max_height_change > self.min_height_change):
                        
    #                     # 计算旋转指标和置信度
    #                     rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
    #                     confidence = self._calculate_confidence(
    #                         duration_ms, 
    #                         max_height_change,
    #                         rotation_metrics['max_rotation_speed']
    #                     )

    #                     event = AirtimeEvent(
    #                         start_time=airtime_start.timestamp,
    #                         end_time=data.timestamp,
    #                         duration_ms=duration_ms,
    #                         max_height_change=max_height_change,
    #                         max_acceleration=max(d.total_acceleration() for d in airtime_segment),
    #                         confidence=confidence,
    #                         **rotation_metrics
    #                     )
    #                     airtime_events.append(event)

    #                 airtime_start = None
    #                 airtime_start_idx = 0

    #     return airtime_events

    
    def _calculate_duration_ms(self, start_data: SensorData, end_data: SensorData) -> int:
        """计算持续时间（毫秒）"""
        time_diff = (end_data.timestamp - start_data.timestamp).total_seconds() * 1000
        ms_diff = end_data.milliseconds - start_data.milliseconds
        return int(time_diff + ms_diff)
    
    def _calculate_max_height_change(self, airtime_segment: List[SensorData]) -> float:
        """
        使用传感器融合技术计算滞空高度
        结合IMU数据与外部参考数据（气压计、GPS）进行传感器融合，减少误差和漂移
        """
        if len(airtime_segment) < 3:
            return 0.0

        # 使用传感器融合方法计算高度
        fused_height = self._sensor_fusion_height_estimation(airtime_segment)

        # 限制在合理的滑雪跳跃高度范围内 (0-8米)
        return min(max(fused_height, 0.0), 8.0)

    def _sensor_fusion_height_estimation(self, airtime_segment: List[SensorData]) -> float:
        """
        传感器融合高度估算
        结合多种传感器数据：IMU加速度计、气压计、GPS高度、磁力计
        使用卡尔曼滤波器进行数据融合
        """
        if len(airtime_segment) < 3:
            return 0.0

        # 步骤1：数据预处理和平滑
        smoothed_segment = self._smooth_sensor_data(airtime_segment)

        # 步骤2：多传感器高度估算
        imu_height = self._imu_based_height_estimation(smoothed_segment)
        barometer_height = self._barometer_based_height_estimation(smoothed_segment)
        gps_height = self._gps_based_height_estimation(smoothed_segment)
        magnetometer_height = self._magnetometer_assisted_height_estimation(smoothed_segment)

        # 步骤3：传感器融合（加权平均 + 卡尔曼滤波）
        fused_height = self._kalman_filter_fusion(
            imu_height, barometer_height, gps_height, magnetometer_height, smoothed_segment)

        return fused_height

    def _imu_based_height_estimation(self, airtime_segment: List[SensorData]) -> float:
        """基于IMU加速度计的高度估算（双重积分法）"""
        if len(airtime_segment) < 3:
            return 0.0

        # 找到最高点
        highest_point_idx = self._find_highest_point(airtime_segment)
        if highest_point_idx >= len(airtime_segment) - 1:
            highest_point_idx = len(airtime_segment) // 2

        # 从最高点开始积分计算下降高度
        descent_segment = airtime_segment[highest_point_idx:]

        gravity_mg = 980  # 重力加速度，单位mg
        height = 0.0
        velocity = 0.0  # 在最高点垂直速度为0

        # 使用改进的积分方法，考虑传感器偏差校正
        for i in range(1, len(descent_segment)):
            # 计算时间间隔
            prev_time = descent_segment[i-1].timestamp.timestamp() + descent_segment[i-1].milliseconds / 1000.0
            curr_time = descent_segment[i].timestamp.timestamp() + descent_segment[i].milliseconds / 1000.0
            dt = curr_time - prev_time

            if dt <= 0 or dt > 0.1:
                continue

            # 获取Z轴加速度并进行重力补偿
            az_current = descent_segment[i].az
            az_prev = descent_segment[i-1].az

            # 计算净加速度（去除重力和传感器偏差）
            net_acc_current = (az_current - gravity_mg) / 100.0  # 转换为m/s²
            net_acc_prev = (az_prev - gravity_mg) / 100.0

            # 使用梯形积分法
            avg_acceleration = (net_acc_current + net_acc_prev) / 2.0

            # 积分得到速度和位移
            velocity += avg_acceleration * dt
            height += velocity * dt + 0.5 * avg_acceleration * dt * dt

        return abs(height)

    def _barometer_based_height_estimation(self, airtime_segment: List[SensorData]) -> float:
        """基于气压计的高度估算"""
        if len(airtime_segment) < 2:
            return 0.0

        # 使用湿度传感器数据作为气压高度的代理
        # 在实际应用中，这里应该是真正的气压计数据
        height_values = [data.humidity for data in airtime_segment]

        # 对气压高度数据进行平滑处理
        smoothed_heights = self._apply_gaussian_smoothing(height_values, sigma=1.2)

        # 计算高度变化（除以10转换为米）
        max_height = max(smoothed_heights)
        min_height = min(smoothed_heights)
        barometer_height = (max_height - min_height) / 10.0

        # 气压计数据通常比较稳定，但可能有温度漂移
        # 进行温度补偿
        if len(airtime_segment) > 5:
            temp_values = [data.temperature for data in airtime_segment]
            temp_variation = max(temp_values) - min(temp_values)

            # 温度补偿系数（经验值）
            temp_compensation = 1.0 - (temp_variation / 1000.0) * 0.1
            barometer_height *= max(temp_compensation, 0.9)

        return barometer_height

    def _gps_based_height_estimation(self, airtime_segment: List[SensorData]) -> float:
        """基于GPS高度的估算"""
        if len(airtime_segment) < 3:
            return 0.0

        # 提取GPS高度数据
        gps_heights = [data.gps_height for data in airtime_segment]

        # GPS高度数据通常噪声较大，需要强力平滑
        smoothed_gps_heights = self._apply_gaussian_smoothing(gps_heights, sigma=2.0)

        # 计算GPS高度变化
        max_gps_height = max(smoothed_gps_heights)
        min_gps_height = min(smoothed_gps_heights)
        gps_height_change = abs(max_gps_height - min_gps_height)

        # GPS高度精度有限，对于短时间的滞空可能不够精确
        # 根据滞空时间调整置信度
        start_time = airtime_segment[0].timestamp.timestamp() + airtime_segment[0].milliseconds / 1000.0
        end_time = airtime_segment[-1].timestamp.timestamp() + airtime_segment[-1].milliseconds / 1000.0
        flight_time = end_time - start_time

        # 短时间滞空时，GPS数据可靠性降低
        if flight_time < 1.0:
            gps_reliability = flight_time  # 0-1之间
        else:
            gps_reliability = 1.0

        return gps_height_change * gps_reliability

    def _magnetometer_assisted_height_estimation(self, airtime_segment: List[SensorData]) -> float:
        """基于磁力计辅助的高度估算"""
        if len(airtime_segment) < 3:
            return 0.0

        # 磁力计可以帮助检测姿态变化，间接估算高度
        # 提取磁力计数据
        mx_values = [data.mx for data in airtime_segment if data.mx != 0]
        my_values = [data.my for data in airtime_segment if data.my != 0]
        mz_values = [data.mz for data in airtime_segment if data.mz != 0]

        if not mx_values or not my_values or not mz_values:
            return 0.0  # 磁力计数据无效

        # 计算磁场强度变化
        mag_strengths = []
        for i in range(len(airtime_segment)):
            if (airtime_segment[i].mx != 0 and
                airtime_segment[i].my != 0 and
                airtime_segment[i].mz != 0):
                mag_strength = math.sqrt(
                    airtime_segment[i].mx**2 +
                    airtime_segment[i].my**2 +
                    airtime_segment[i].mz**2
                )
                mag_strengths.append(mag_strength)

        if len(mag_strengths) < 3:
            return 0.0

        # 平滑磁场强度数据
        smoothed_mag = self._apply_gaussian_smoothing(mag_strengths, sigma=1.0)

        # 磁场强度变化可以反映高度变化（地磁场随高度略有变化）
        mag_variation = max(smoothed_mag) - min(smoothed_mag)

        # 将磁场变化转换为高度估算（经验公式）
        # 这是一个粗略的估算，主要用于辅助验证
        magnetometer_height = mag_variation / 1000.0  # 经验转换系数

        return min(magnetometer_height, 2.0)  # 限制最大值

    def _kalman_filter_fusion(self, imu_height: float, barometer_height: float,
                             gps_height: float, magnetometer_height: float,
                             airtime_segment: List[SensorData]) -> float:
        """
        使用简化的卡尔曼滤波器进行传感器融合
        结合多个传感器的高度估算，减少误差和漂移
        """
        # 定义各传感器的测量噪声方差（基于经验值，用于后续扩展）
        # imu_variance = 0.5          # IMU积分误差较大
        # barometer_variance = 0.1    # 气压计相对稳定
        # gps_variance = 2.0          # GPS噪声较大
        # magnetometer_variance = 1.0 # 磁力计辅助，精度中等

        # 计算滞空时间，用于动态调整权重
        start_time = airtime_segment[0].timestamp.timestamp() + airtime_segment[0].milliseconds / 1000.0
        end_time = airtime_segment[-1].timestamp.timestamp() + airtime_segment[-1].milliseconds / 1000.0
        flight_time = end_time - start_time

        # 根据滞空时间动态调整传感器权重
        if flight_time < 0.5:
            # 短时间滞空：IMU和气压计权重高，GPS权重低
            imu_weight = 0.4
            barometer_weight = 0.4
            gps_weight = 0.1
            magnetometer_weight = 0.1
        elif flight_time < 1.5:
            # 中等时间滞空：平衡各传感器
            imu_weight = 0.35
            barometer_weight = 0.35
            gps_weight = 0.2
            magnetometer_weight = 0.1
        else:
            # 长时间滞空：GPS权重增加，IMU权重降低（避免积分漂移）
            imu_weight = 0.25
            barometer_weight = 0.35
            gps_weight = 0.3
            magnetometer_weight = 0.1

        # 数据有效性检查和权重调整
        valid_sensors = []
        weights = []
        heights = []

        # IMU数据检查
        if imu_height > 0 and imu_height < 10:
            valid_sensors.append('IMU')
            weights.append(imu_weight)
            heights.append(imu_height)

        # 气压计数据检查
        if barometer_height > 0 and barometer_height < 8:
            valid_sensors.append('Barometer')
            weights.append(barometer_weight)
            heights.append(barometer_height)

        # GPS数据检查
        if gps_height > 0 and gps_height < 15:
            valid_sensors.append('GPS')
            weights.append(gps_weight)
            heights.append(gps_height)

        # 磁力计数据检查
        if magnetometer_height > 0 and magnetometer_height < 5:
            valid_sensors.append('Magnetometer')
            weights.append(magnetometer_weight)
            heights.append(magnetometer_height)

        if not heights:
            return 0.0

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in weights]
        else:
            normalized_weights = [1.0 / len(heights)] * len(heights)

        # 加权融合
        fused_height = sum(h * w for h, w in zip(heights, normalized_weights))

        # 应用简化的卡尔曼滤波器进行进一步优化
        kalman_filtered_height = self._apply_simple_kalman_filter(
            fused_height, heights, normalized_weights, flight_time)

        return kalman_filtered_height

    def _apply_simple_kalman_filter(self, initial_estimate: float, measurements: List[float],
                                   weights: List[float], flight_time: float) -> float:
        """
        应用简化的卡尔曼滤波器
        """
        if not measurements:
            return initial_estimate

        # 初始状态估计
        state_estimate = initial_estimate

        # 过程噪声（基于飞行时间）
        process_noise = 0.1 + flight_time * 0.05

        # 估计误差协方差
        estimate_error = 0.5

        # 对每个测量值应用卡尔曼更新
        for measurement, weight in zip(measurements, weights):
            # 测量噪声（基于传感器权重，权重越高噪声越小）
            measurement_noise = 1.0 / (weight + 0.1)

            # 卡尔曼增益
            kalman_gain = estimate_error / (estimate_error + measurement_noise)

            # 更新状态估计
            state_estimate = state_estimate + kalman_gain * (measurement - state_estimate)

            # 更新估计误差协方差
            estimate_error = (1 - kalman_gain) * estimate_error + process_noise

        return state_estimate

    def _calculate_height_from_z_acceleration(self, airtime_segment: List[SensorData]) -> float:
        """通过Z轴加速度分析计算滞空高度（包含数据平滑处理）"""
        if len(airtime_segment) < 3:
            return 0.0

        # 步骤0：对传感器数据进行平滑处理以减少噪声
        smoothed_segment = self._smooth_sensor_data(airtime_segment)

        # 步骤1：找到滞空最高点（使用平滑后的数据）
        highest_point_idx = self._find_highest_point(smoothed_segment)

        if highest_point_idx == -1 or highest_point_idx >= len(smoothed_segment) - 1:
            # 如果找不到最高点或最高点在末尾，使用传统方法
            return self._fallback_height_calculation(smoothed_segment)

        # 步骤2：分析从最高点到落地点的Z轴加速度（使用平滑后的数据）
        descent_segment = smoothed_segment[highest_point_idx:]

        # 步骤3：通过Z轴加速度积分计算高度
        height = self._integrate_z_acceleration_to_height(descent_segment)

        return height

    def _smooth_sensor_data(self, airtime_segment: List[SensorData]) -> List[SensorData]:
        """对传感器数据进行平滑处理以减少噪声"""
        if len(airtime_segment) < 3:
            return airtime_segment

        # 创建平滑后的数据副本
        smoothed_segment = []

        # 使用移动平均滤波器进行平滑处理
        window_size = min(5, len(airtime_segment) // 3)  # 动态窗口大小，最大为5

        for i in range(len(airtime_segment)):
            # 计算窗口范围
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(airtime_segment), i + window_size // 2 + 1)

            # 对关键传感器数据进行平滑
            window_data = airtime_segment[start_idx:end_idx]

            # 平滑Z轴加速度
            smoothed_az = sum(data.az for data in window_data) / len(window_data)

            # 平滑高度数据
            smoothed_humidity = sum(data.humidity for data in window_data) / len(window_data)

            # 平滑角速度数据
            smoothed_gx = sum(data.gx for data in window_data) / len(window_data)
            smoothed_gy = sum(data.gy for data in window_data) / len(window_data)
            smoothed_gz = sum(data.gz for data in window_data) / len(window_data)

            # 创建平滑后的数据点
            original_data = airtime_segment[i]
            smoothed_data = SensorData(
                timestamp=original_data.timestamp,
                milliseconds=original_data.milliseconds,
                x=original_data.x,
                y=original_data.y,
                z=original_data.z,
                P=original_data.P,
                ax=original_data.ax,  # X、Y轴加速度保持原值
                ay=original_data.ay,
                az=smoothed_az,       # Z轴加速度使用平滑值
                gx=smoothed_gx,       # 角速度使用平滑值
                gy=smoothed_gy,
                gz=smoothed_gz,
                mx=original_data.mx,
                my=original_data.my,
                mz=original_data.mz,
                temperature=original_data.temperature,
                humidity=smoothed_humidity,  # 高度数据使用平滑值
                speed=original_data.speed,
                fs=original_data.fs,
                longitude=original_data.longitude,
                latitude=original_data.latitude,
                gps_height=original_data.gps_height
            )

            smoothed_segment.append(smoothed_data)

        return smoothed_segment

    def _apply_gaussian_smoothing(self, values: List[float], sigma: float = 1.0) -> List[float]:
        """应用高斯平滑滤波器"""
        if len(values) < 3:
            return values

        # 生成高斯核
        kernel_size = min(7, len(values))  # 核大小
        kernel = []
        center = kernel_size // 2

        for i in range(kernel_size):
            x = i - center
            weight = math.exp(-(x * x) / (2 * sigma * sigma))
            kernel.append(weight)

        # 归一化核
        kernel_sum = sum(kernel)
        kernel = [w / kernel_sum for w in kernel]

        # 应用卷积
        smoothed = []
        for i in range(len(values)):
            weighted_sum = 0.0
            weight_sum = 0.0

            for j in range(kernel_size):
                data_idx = i - center + j
                if 0 <= data_idx < len(values):
                    weighted_sum += values[data_idx] * kernel[j]
                    weight_sum += kernel[j]

            if weight_sum > 0:
                smoothed.append(weighted_sum / weight_sum)
            else:
                smoothed.append(values[i])

        return smoothed

    def _find_highest_point(self, airtime_segment: List[SensorData]) -> int:
        """找到滞空过程中的最高点索引（使用平滑后的数据）"""
        if len(airtime_segment) < 3:
            return 0

        # 方法1：通过平滑后的Z轴加速度变化找最高点
        z_values = [data.az for data in airtime_segment]
        gravity_mg = 980  # 重力加速度，单位mg

        # 进一步平滑Z轴加速度数据以减少噪声
        smoothed_z = self._apply_gaussian_smoothing(z_values, sigma=0.8)

        # 寻找Z轴加速度最接近重力加速度的点（失重状态）
        min_deviation_idx = 0
        min_deviation = float('inf')

        for i, az in enumerate(smoothed_z):
            # 计算与重力加速度的偏差
            deviation = abs(abs(az) - gravity_mg)
            if deviation < min_deviation:
                min_deviation = deviation
                min_deviation_idx = i

        # 方法2：通过Z轴加速度的导数找到转折点
        # 在最高点，Z轴加速度的变化率应该最小
        if len(smoothed_z) > 5:
            z_derivatives = []
            for i in range(1, len(smoothed_z) - 1):
                derivative = abs(smoothed_z[i+1] - smoothed_z[i-1]) / 2.0
                z_derivatives.append(derivative)

            if z_derivatives:
                min_derivative_idx = z_derivatives.index(min(z_derivatives)) + 1

                # 如果两种方法结果相近，取平均值
                if abs(min_deviation_idx - min_derivative_idx) <= len(airtime_segment) * 0.3:
                    min_deviation_idx = (min_deviation_idx + min_derivative_idx) // 2

        # 方法3：结合高度传感器数据验证
        if len(airtime_segment) > 5:
            height_values = [data.humidity for data in airtime_segment]
            # 对高度数据也进行平滑处理
            smoothed_heights = self._apply_gaussian_smoothing(height_values, sigma=1.0)
            max_height_idx = smoothed_heights.index(max(smoothed_heights))

            # 如果加速度方法和高度方法的结果相近，取加权平均
            if abs(min_deviation_idx - max_height_idx) <= len(airtime_segment) * 0.25:
                # 加速度方法权重60%，高度方法权重40%
                weighted_idx = int(min_deviation_idx * 0.6 + max_height_idx * 0.4)
                return weighted_idx

        return min_deviation_idx

    def _integrate_z_acceleration_to_height(self, descent_segment: List[SensorData]) -> float:
        """通过平滑后的Z轴加速度积分计算从最高点到落地点的高度"""
        if len(descent_segment) < 2:
            return 0.0

        gravity_mg = 980  # 重力加速度，单位mg

        # 提取Z轴加速度数据并进行额外的平滑处理
        z_accelerations = [data.az for data in descent_segment]
        smoothed_accelerations = self._apply_gaussian_smoothing(z_accelerations, sigma=0.5)

        height = 0.0
        velocity = 0.0  # 初始垂直速度（在最高点为0）

        # 使用梯形积分法进行更精确的积分计算
        for i in range(1, len(descent_segment)):
            # 计算时间间隔
            prev_time = descent_segment[i-1].timestamp.timestamp() + descent_segment[i-1].milliseconds / 1000.0
            curr_time = descent_segment[i].timestamp.timestamp() + descent_segment[i].milliseconds / 1000.0
            dt = curr_time - prev_time

            if dt <= 0 or dt > 0.1:  # 忽略异常时间间隔
                continue

            # 获取当前和前一个时刻的平滑Z轴加速度
            az_prev = smoothed_accelerations[i-1]
            az_current = smoothed_accelerations[i]

            # 计算净加速度（去除重力分量）
            net_acceleration_prev = az_prev - gravity_mg
            net_acceleration_current = az_current - gravity_mg

            # 转换为m/s²（从mg转换）
            net_acc_prev_ms2 = net_acceleration_prev / 100.0
            net_acc_current_ms2 = net_acceleration_current / 100.0

            # 使用梯形法则计算平均加速度
            avg_acceleration = (net_acc_prev_ms2 + net_acc_current_ms2) / 2.0

            # 积分得到速度变化（使用平均加速度）
            velocity += avg_acceleration * dt

            # 积分得到位移变化（使用梯形法则）
            height += velocity * dt + 0.5 * avg_acceleration * dt * dt

        # 返回绝对高度值
        return abs(height)

    def _fallback_height_calculation(self, airtime_segment: List[SensorData]) -> float:
        """备用高度计算方法（当无法找到明确最高点时）- 使用平滑数据"""
        # 使用平滑后的传感器高度数据
        height_values = [data.humidity for data in airtime_segment]
        smoothed_heights = self._apply_gaussian_smoothing(height_values, sigma=1.0)
        sensor_height_change = (max(smoothed_heights) - min(smoothed_heights)) / 10.0

        # 使用平滑后的Z轴加速度进行物理估算
        z_values = [data.az for data in airtime_segment]
        smoothed_z = self._apply_gaussian_smoothing(z_values, sigma=0.8)

        # 计算平均失重程度作为高度估算的参考
        gravity_mg = 980
        weightless_score = 0.0
        for az in smoothed_z:
            deviation = abs(abs(az) - gravity_mg)
            if deviation > 200:  # 显著偏离重力
                weightless_score += min(deviation / gravity_mg, 1.0)

        if len(smoothed_z) > 0:
            avg_weightless_score = weightless_score / len(smoothed_z)
            # 基于失重程度估算高度（经验公式）
            weightless_based_height = avg_weightless_score * 2.0  # 最大2米
        else:
            weightless_based_height = 0.0

        # 使用简单的物理估算作为验证
        start_time = airtime_segment[0].timestamp.timestamp() + airtime_segment[0].milliseconds / 1000.0
        end_time = airtime_segment[-1].timestamp.timestamp() + airtime_segment[-1].milliseconds / 1000.0
        flight_time = end_time - start_time

        if flight_time > 0:
            # 基于飞行时间的简单估算：h = g*t²/8
            time_based_height = 9.81 * flight_time * flight_time / 8.0

            # 综合三种方法：传感器高度40%，失重程度30%，飞行时间30%
            combined_height = (sensor_height_change * 0.4 +
                             weightless_based_height * 0.3 +
                             min(time_based_height, 5.0) * 0.3)
            return combined_height

        # 如果时间计算失败，使用传感器和失重程度的平均值
        return (sensor_height_change + weightless_based_height) / 2.0

    def _calculate_physics_based_height(self, airtime_segment: List[SensorData]) -> float:
        """基于抛物运动物理公式计算高度"""
        if len(airtime_segment) < 3:
            return 0.0

        # 计算滞空时间（秒）
        start_time = airtime_segment[0].timestamp.timestamp() + airtime_segment[0].milliseconds / 1000.0
        end_time = airtime_segment[-1].timestamp.timestamp() + airtime_segment[-1].milliseconds / 1000.0
        flight_time = end_time - start_time

        if flight_time <= 0:
            return 0.0

        # 重力加速度 (m/s²)
        g = 9.81

        # 方法1：基于飞行时间估算初始垂直速度
        # 对于对称抛物运动：t = 2*vy0/g，所以 vy0 = g*t/2
        # 最大高度：hmax = vy0²/(2*g) = (g*t/2)²/(2*g) = g*t²/8
        time_based_height = g * flight_time * flight_time / 8.0

        # 方法2：基于水平距离和角度估算（如果有GPS数据）
        start_data = airtime_segment[0]
        end_data = airtime_segment[-1]

        # 计算水平位移
        lat_diff = end_data.latitude - start_data.latitude
        lon_diff = end_data.longitude - start_data.longitude

        # 简化的水平距离计算（米）
        horizontal_distance = math.sqrt(lat_diff**2 + lon_diff**2) * 111000  # 大约转换为米

        if horizontal_distance > 0.1:  # 至少10cm的水平移动
            # 估算跳跃角度（假设为30-45度的典型滑雪跳跃）
            estimated_angle = math.radians(35)  # 35度

            # 基于水平距离和角度的高度估算
            # h ≈ X * tan(θ) / 2  (简化公式，考虑抛物线轨迹)
            angle_based_height = horizontal_distance * math.tan(estimated_angle) / 2.0

            # 取两种方法的平均值
            return (time_based_height + min(angle_based_height, 5.0)) / 2.0

        return time_based_height

    def _calculate_acceleration_based_height(self, airtime_segment: List[SensorData]) -> float:
        """基于加速度积分计算高度变化"""
        if len(airtime_segment) < 3:
            return 0.0

        # 计算垂直加速度变化（去除重力）
        g_mg = 980  # 重力加速度，单位mg
        vertical_velocities = []

        # 初始垂直速度设为0
        v_vertical = 0.0

        for i in range(1, len(airtime_segment)):
            # 计算时间间隔
            prev_time = airtime_segment[i-1].timestamp.timestamp() + airtime_segment[i-1].milliseconds / 1000.0
            curr_time = airtime_segment[i].timestamp.timestamp() + airtime_segment[i].milliseconds / 1000.0
            dt = curr_time - prev_time

            if dt > 0:
                # Z轴加速度（去除重力）
                az_corrected = airtime_segment[i].az - g_mg  # 去除重力分量

                # 转换为m/s²
                az_ms2 = az_corrected / 1000.0 * 9.81

                # 积分得到速度变化
                v_vertical += az_ms2 * dt
                vertical_velocities.append(v_vertical)

        if not vertical_velocities:
            return 0.0

        # 计算高度：对速度再次积分
        height = 0.0
        max_height = 0.0

        for i in range(1, len(vertical_velocities)):
            dt = 0.01  # 假设采样间隔约10ms
            height += vertical_velocities[i] * dt
            max_height = max(max_height, height)

        return min(max_height, 5.0)  # 限制最大值
    
    def _calculate_rotation_metrics(self, airtime_segment: List[SensorData]) -> dict:
        """计算旋转相关指标"""
        if len(airtime_segment) < 2:
            return {
                'max_rotation_speed': 0.0,
                'avg_rotation_speed': 0.0,
                'rotation_detected': False,
                'rotation_type': 'none'
            }

        # 提取角速度数据
        gx_values = [data.gx for data in airtime_segment]
        gy_values = [data.gy for data in airtime_segment]
        gz_values = [data.gz for data in airtime_segment]

        # 计算各轴的最大角速度
        max_gx = max(abs(gx) for gx in gx_values)
        max_gy = max(abs(gy) for gy in gy_values)
        max_gz = max(abs(gz) for gz in gz_values)

        # 计算总角速度
        total_angular_velocities = [data.total_angular_velocity() for data in airtime_segment]
        max_total_rotation = max(total_angular_velocities)
        avg_total_rotation = sum(total_angular_velocities) / len(total_angular_velocities)

        # 判断旋转类型和是否有明显旋转
        rotation_threshold = 500  # °/s，超过此值认为有明显旋转
        rotation_detected = max_total_rotation > rotation_threshold

        # 判断主要旋转轴
        rotation_type = 'none'
        if rotation_detected:
            if max_gz > max(max_gx, max_gy):
                rotation_type = 'spin'      # Z轴旋转（转体）
            elif max_gx > max_gy:
                rotation_type = 'flip_x'    # X轴翻转（前后翻）
            else:
                rotation_type = 'flip_y'    # Y轴翻转（侧翻）

        return {
            'max_rotation_speed': max_total_rotation,
            'avg_rotation_speed': avg_total_rotation,
            'rotation_detected': rotation_detected,
            'rotation_type': rotation_type,
            'max_gx': max_gx,
            'max_gy': max_gy,
            'max_gz': max_gz
        }

    def _calculate_confidence(self, airtime_segment: List[SensorData], duration_ms: int) -> float:
        """计算滞空事件的置信度"""
        if len(airtime_segment) < 2:
            return 0.0
            
        # 基于多个因素计算置信度
        factors = []
        
        # 1. 持续时间因素 (合理的滞空时间有更高置信度)
        if 200 <= duration_ms <= 3000:
            factors.append(1.0)
        elif duration_ms < 200:
            factors.append(duration_ms / 200.0)
        else:
            factors.append(max(0.1, 3000.0 / duration_ms))
        
        # 2. 加速度变化因素
        accelerations = [data.total_acceleration() for data in airtime_segment]
        acc_variance = np.var(accelerations) if len(accelerations) > 1 else 0
        acc_factor = min(1.0, acc_variance / 100000)  # 归一化
        factors.append(acc_factor)
        
        # 3. 角速度变化因素
        angular_velocities = [data.total_angular_velocity() for data in airtime_segment]
        ang_variance = np.var(angular_velocities) if len(angular_velocities) > 1 else 0
        ang_factor = min(1.0, ang_variance / 1000000)  # 归一化
        factors.append(ang_factor)
        
        # 计算综合置信度
        return sum(factors) / len(factors)
    
    def analyze_airtime_statistics(self, airtime_events: List[AirtimeEvent]) -> Dict:
        """分析滞空统计数据"""
        if not airtime_events:
            return {
                'total_events': 0,
                'valid_events': 0,
                'total_airtime_seconds': 0,
                'valid_airtime_seconds': 0,
                'average_airtime_seconds': 0,
                'valid_average_airtime_seconds': 0,
                'max_airtime_seconds': 0,
                'min_airtime_seconds': 0,
                'high_confidence_events': 0,
                'valid_confidence_events': 0
            }

        # 所有事件
        durations = [event.duration_seconds for event in airtime_events]

        # 有效滞空事件（置信度 > 0.5）
        valid_events = [e for e in airtime_events if e.confidence > 0.5]
        valid_durations = [e.duration_seconds for e in valid_events]

        # 高置信度事件（置信度 > 0.7，保持原有逻辑）
        high_confidence_events = [e for e in airtime_events if e.confidence > 0.7]

        return {
            'total_events': len(airtime_events),
            'valid_events': len(valid_events),
            'total_airtime_seconds': sum(durations),
            'valid_airtime_seconds': sum(valid_durations) if valid_durations else 0,
            'average_airtime_seconds': sum(durations) / len(durations),
            'valid_average_airtime_seconds': sum(valid_durations) / len(valid_durations) if valid_durations else 0,
            'max_airtime_seconds': max(durations),
            'min_airtime_seconds': min(durations),
            'high_confidence_events': len(high_confidence_events),
            'valid_confidence_events': len(valid_events),
            'confidence_scores': [e.confidence for e in airtime_events],
            'valid_events_list': valid_events
        }


def main():
    """主函数 - 演示如何使用滑雪滞空计算器"""
    
    # 创建计算器实例
    calculator = SkiingAirtimeCalculator(
        gravity_threshold=800,      # 重力偏差阈值
        min_airtime_ms=100,         # 最小滞空时间100ms
        max_airtime_ms=5000,        # 最大滞空时间5秒
        angular_velocity_threshold=1000,  # 角速度阈值
        height_change_threshold=1.0       # 高度变化阈值
    )
    
    # 加载传感器数据
    print("正在加载传感器数据...")
    sensor_data = calculator.load_sensor_data(r'E:\cyl\成都跳台\data_02.txt')
    
    if not sensor_data:
        print("没有加载到有效的传感器数据")
        return
    
    print(f"数据时间范围: {sensor_data[0].timestamp} 到 {sensor_data[-1].timestamp}")
    
    # 检测滞空事件
    print("正在检测滞空事件...")
    airtime_events = calculator.detect_airtime_events(sensor_data)
    
    # 分析统计数据
    stats = calculator.analyze_airtime_statistics(airtime_events)
    
    # 输出结果
    print("\n=== 滑雪滞空分析结果 ===")
    print(f"检测到滞空事件: {stats['total_events']} 次")
    print(f"有效滞空事件 (置信度>0.5): {stats['valid_events']} 次")
    print(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒")
    print(f"有效滞空时间: {stats['valid_airtime_seconds']:.2f} 秒")
    print(f"平均滞空时间: {stats['average_airtime_seconds']:.2f} 秒")
    print(f"有效平均滞空时间: {stats['valid_average_airtime_seconds']:.2f} 秒")
    print(f"最长滞空时间: {stats['max_airtime_seconds']:.2f} 秒")
    print(f"最短滞空时间: {stats['min_airtime_seconds']:.2f} 秒")
    print(f"高置信度事件 (>0.7): {stats['high_confidence_events']} 次")
    
    # 显示有效滞空事件（置信度>0.5）
    valid_events = stats.get('valid_events_list', [])
    if valid_events:
        print(f"\n=== 有效滞空事件详细列表 (置信度>0.5) ===")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'旋转':<6} {'最大转速(°/s)':<12}")
        print("-" * 115)
        for i, event in enumerate(valid_events, 1):
            # 获取精确的开始和结束时间戳（直接查找匹配的数据点）
            start_timestamp = None
            end_timestamp = None

            # 查找开始时间对应的传感器数据
            for data in sensor_data:
                if data.timestamp == event.start_time:
                    start_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                    break

            # 查找结束时间对应的传感器数据
            for data in sensor_data:
                if data.timestamp == event.end_time:
                    end_timestamp = f"{data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{data.milliseconds:03d}"
                    break

            # 如果找不到精确匹配，使用事件时间本身
            if not start_timestamp:
                start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
            if not end_timestamp:
                end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')

            rotation_indicator = "🌀" if event.rotation_detected else "—"
            print(f"{i:<4} {start_timestamp:<23} {end_timestamp:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {rotation_indicator:<6} {event.max_rotation_speed:<12.0f}")

    # 显示所有事件的简要信息
    if airtime_events:
        print(f"\n=== 所有检测事件概览 ===")
        print(f"{'序号':<4} {'时间':<17} {'持续(s)':<8} {'置信度':<8} {'状态':<8}")
        print("-" * 50)
        for i, event in enumerate(airtime_events, 1):
            status = "✓有效" if event.confidence > 0.5 else "低信度"
            print(f"{i:<4} {event.start_time.strftime('%H:%M:%S'):<17} "
                  f"{event.duration_seconds:<8.2f} {event.confidence:<8.2f} {status:<8}")


if __name__ == "__main__":
    main()
