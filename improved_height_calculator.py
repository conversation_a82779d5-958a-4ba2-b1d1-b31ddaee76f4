#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的滞空高度计算方法
基于物理原理和传感器数据的更准确高度估算
"""

import numpy as np
from typing import List
from skiing_airtime_calculator import SensorData
import math


class ImprovedHeightCalculator:
    """改进的高度计算器"""
    
    def __init__(self):
        self.gravity = 9.8  # m/s²
        self.sampling_rate = 33.33  # Hz (假设30ms采样间隔)
    
    def calculate_airtime_height_v1(self, airtime_segment: List[SensorData]) -> float:
        """方法1: 基于Z轴位置变化（原方法的改进版）"""
        if len(airtime_segment) < 2:
            return 0.0
        
        # 使用GPS高度数据（如果可用且可靠）
        gps_heights = [data.gps_height for data in airtime_segment if data.gps_height > 0]
        if len(gps_heights) >= 2:
            return max(gps_heights) - min(gps_heights)
        
        # 使用Z轴位置，但进行平滑处理
        z_values = [data.z for data in airtime_segment]
        
        # 平滑处理减少噪声
        if len(z_values) >= 3:
            smoothed_z = self._smooth_data(z_values, window_size=3)
        else:
            smoothed_z = z_values
        
        # 计算高度变化，但限制在合理范围内
        height_change = max(smoothed_z) - min(smoothed_z)
        
        # 限制在合理的滑雪跳跃高度范围内 (0-10米)
        return min(height_change, 10.0)
    
    def calculate_airtime_height_v2(self, airtime_segment: List[SensorData]) -> float:
        """方法2: 基于垂直加速度积分估算高度"""
        if len(airtime_segment) < 3:
            return 0.0
        
        # 提取Z轴加速度数据 (mg转换为m/s²)
        az_values = [data.az / 1000.0 * self.gravity for data in airtime_segment]
        
        # 去除重力分量，得到净垂直加速度
        net_accelerations = [az - self.gravity for az in az_values]
        
        # 平滑处理
        smoothed_acc = self._smooth_data(net_accelerations, window_size=3)
        
        # 通过二次积分计算高度变化
        dt = 1.0 / self.sampling_rate  # 时间间隔
        
        # 第一次积分得到速度
        velocities = [0.0]
        for i in range(1, len(smoothed_acc)):
            v = velocities[-1] + smoothed_acc[i] * dt
            velocities.append(v)
        
        # 第二次积分得到位移
        heights = [0.0]
        for i in range(1, len(velocities)):
            h = heights[-1] + velocities[i] * dt
            heights.append(h)
        
        # 计算最大高度变化
        max_height = max(heights) - min(heights)
        
        # 限制在合理范围内
        return min(abs(max_height), 5.0)
    
    def calculate_airtime_height_v3(self, airtime_segment: List[SensorData]) -> float:
        """方法3: 基于自由落体公式估算最大高度"""
        if len(airtime_segment) < 2:
            return 0.0
        
        # 计算滞空时间（秒）
        duration_seconds = len(airtime_segment) / self.sampling_rate
        
        # 使用自由落体公式估算最大高度
        # h = (1/2) * g * (t/2)²  (假设对称的抛物线运动)
        estimated_height = 0.5 * self.gravity * (duration_seconds / 2) ** 2
        
        # 限制在合理范围内 (滑雪跳跃通常不超过3米)
        return min(estimated_height, 3.0)
    
    def calculate_airtime_height_v4(self, airtime_segment: List[SensorData]) -> float:
        """方法4: 综合多种方法的加权平均"""
        if len(airtime_segment) < 2:
            return 0.0
        
        # 计算各种方法的结果
        height_v1 = self.calculate_airtime_height_v1(airtime_segment)
        height_v2 = self.calculate_airtime_height_v2(airtime_segment)
        height_v3 = self.calculate_airtime_height_v3(airtime_segment)
        
        # 过滤异常值
        heights = [h for h in [height_v1, height_v2, height_v3] if 0 <= h <= 5.0]
        
        if not heights:
            return 0.0
        
        # 如果有GPS数据，给更高权重
        gps_heights = [data.gps_height for data in airtime_segment if data.gps_height > 0]
        if len(gps_heights) >= 2:
            gps_change = max(gps_heights) - min(gps_heights)
            if 0 <= gps_change <= 10:
                heights.append(gps_change)
                heights.append(gps_change)  # 给GPS数据双倍权重
        
        # 返回加权平均值
        return sum(heights) / len(heights)
    
    def _smooth_data(self, data: List[float], window_size: int = 3) -> List[float]:
        """数据平滑处理"""
        if len(data) < window_size:
            return data
        
        smoothed = []
        for i in range(len(data)):
            start = max(0, i - window_size // 2)
            end = min(len(data), i + window_size // 2 + 1)
            smoothed.append(sum(data[start:end]) / (end - start))
        
        return smoothed
    
    def analyze_height_calculation_methods(self, airtime_segment: List[SensorData]) -> dict:
        """分析不同高度计算方法的结果"""
        results = {
            'original_method': max([data.z for data in airtime_segment]) - min([data.z for data in airtime_segment]),
            'improved_z_method': self.calculate_airtime_height_v1(airtime_segment),
            'acceleration_integration': self.calculate_airtime_height_v2(airtime_segment),
            'free_fall_estimation': self.calculate_airtime_height_v3(airtime_segment),
            'weighted_average': self.calculate_airtime_height_v4(airtime_segment),
            'duration_seconds': len(airtime_segment) / self.sampling_rate
        }
        
        return results


def test_height_calculation():
    """测试不同高度计算方法"""
    from optimized_airtime_detector import OptimizedAirtimeDetector
    
    print("🏔️ 滞空高度计算方法测试")
    print("=" * 60)
    
    # 加载数据
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("无法加载数据")
        return
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    # 创建高度计算器
    height_calculator = ImprovedHeightCalculator()
    
    print(f"分析前10个滞空事件的高度计算方法对比:")
    print(f"{'序号':<4} {'原方法':<8} {'改进Z轴':<8} {'加速度积分':<10} {'自由落体':<8} {'加权平均':<8} {'持续时间(s)':<10}")
    print("-" * 70)
    
    for i, event in enumerate(airtime_events[:10], 1):
        # 找到对应的传感器数据段
        start_time = event.start_time
        end_time = event.end_time
        
        segment = []
        for data in sensor_data:
            if start_time <= data.timestamp <= end_time:
                segment.append(data)
        
        if segment:
            results = height_calculator.analyze_height_calculation_methods(segment)
            
            print(f"{i:<4} {results['original_method']:<8.1f} {results['improved_z_method']:<8.1f} "
                  f"{results['acceleration_integration']:<10.1f} {results['free_fall_estimation']:<8.1f} "
                  f"{results['weighted_average']:<8.1f} {results['duration_seconds']:<10.2f}")
    
    print(f"\n💡 分析结论:")
    print(f"1. 原方法（Z坐标差值）数值过大，不符合实际")
    print(f"2. 改进Z轴方法限制了合理范围")
    print(f"3. 加速度积分方法基于物理原理")
    print(f"4. 自由落体估算基于滞空时间")
    print(f"5. 加权平均综合多种方法，更稳定")
    
    print(f"\n🎯 建议:")
    print(f"- 使用加权平均方法作为最终的高度估算")
    print(f"- 滑雪跳跃高度通常在0.1-3米范围内")
    print(f"- 可以根据实际测试调整权重和限制范围")


if __name__ == "__main__":
    test_height_calculation()
