#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的滑雪滞空检测器 - 只输出高置信度事件详细信息
"""

import re
import os
from datetime import datetime
from typing import List


class SensorData:
    """传感器数据类"""
    def __init__(self, timestamp, milliseconds, x, y, z, P, ax, ay, az, gx, gy, gz, 
                 mx, my, mz, t, humidity, speed, longitude, latitude, gh):
        self.timestamp = timestamp
        self.milliseconds = milliseconds
        self.x = x
        self.y = y
        self.z = z
        self.P = P
        self.ax = ax
        self.ay = ay
        self.az = az
        self.gx = gx
        self.gy = gy
        self.gz = gz
        self.mx = mx
        self.my = my
        self.mz = mz
        self.t = t
        self.humidity = humidity
        self.speed = speed
        self.longitude = longitude
        self.latitude = latitude
        self.gh = gh
    
    def total_acceleration(self):
        """计算总加速度"""
        return (self.ax**2 + self.ay**2 + self.az**2)**0.5
    
    def total_angular_velocity(self):
        """计算总角速度"""
        return (self.gx**2 + self.gy**2 + self.gz**2)**0.5


class AirtimeEvent:
    """滞空事件类"""
    def __init__(self, start_time, end_time, duration_ms, max_height_change, 
                 max_acceleration, confidence):
        self.start_time = start_time
        self.end_time = end_time
        self.duration_ms = duration_ms
        self.max_height_change = max_height_change
        self.max_acceleration = max_acceleration
        self.confidence = confidence
    
    @property
    def duration_seconds(self):
        """持续时间（秒）"""
        return self.duration_ms / 1000.0


class AirtimeDetector:
    """滑雪滞空检测器"""
    
    def __init__(self):
        # 基础检测参数
        self.gravity_threshold = 600
        self.angular_velocity_threshold = 800
        self.min_airtime_ms = 150
        self.max_airtime_ms = 3000
        self.standard_gravity = 1000
        self.weightless_ratio = 0.15
        self.z_spike_ratio = 0.6
    
    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """加载传感器数据"""
        if not os.path.exists(filename):
            print(f"错误: 文件 '{filename}' 不存在")
            return []
        
        sensor_data = []
        # 适配720.txt格式的正则表达式
        pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+).*?lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                parsed_count = 0
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    match = re.match(pattern, line)
                    if match:
                        try:
                            groups = match.groups()
                            timestamp = datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S')
                            milliseconds = int(groups[1])
                            
                            data = SensorData(
                                timestamp=timestamp,
                                milliseconds=milliseconds,
                                x=int(groups[2]),
                                y=int(groups[3]),
                                z=int(groups[4]),
                                P=int(groups[5]),
                                ax=int(groups[6]),
                                ay=int(groups[7]),
                                az=int(groups[8]),
                                gx=int(groups[9]),
                                gy=int(groups[10]),
                                gz=int(groups[11]),
                                mx=int(groups[12]),
                                my=int(groups[13]),
                                mz=int(groups[14]),
                                t=int(groups[15]),
                                humidity=int(groups[16]),
                                speed=int(groups[17]),
                                longitude=float(groups[18]),
                                latitude=float(groups[19]),
                                gh=int(groups[20])
                            )
                            sensor_data.append(data)
                            parsed_count += 1
                        except ValueError:
                            continue
                
                print(f"成功加载 {parsed_count} 条数据记录")
                
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []
        
        return sensor_data
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """检测滞空事件"""
        if len(sensor_data) < 10:
            return []
        
        # 平滑Z轴加速度
        smoothed_z = []
        for i in range(len(sensor_data)):
            start_idx = max(0, i - 1)
            end_idx = min(len(sensor_data), i + 2)
            window_values = [sensor_data[j].az for j in range(start_idx, end_idx)]
            smoothed_z.append(sum(window_values) / len(window_values))
        
        # 检测滞空条件
        airtime_conditions = []
        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]
            angular_velocity = data.total_angular_velocity()
            
            # 三种滞空条件
            z_gravity_deviation = abs(abs(z_smooth) - self.standard_gravity)
            gravity_anomaly = z_gravity_deviation > self.gravity_threshold
            high_angular = angular_velocity > self.angular_velocity_threshold
            
            is_weightless = abs(z_smooth) < (self.weightless_ratio * self.standard_gravity)
            medium_angular = angular_velocity > (0.4 * self.angular_velocity_threshold)
            
            z_spike = False
            if i > 0:
                z_change = abs(z_smooth - smoothed_z[i-1])
                z_spike = z_change > (self.z_spike_ratio * self.standard_gravity)
            high_angular_60 = angular_velocity > (0.6 * self.angular_velocity_threshold)
            
            # 满足任一条件即为滞空
            is_airborne = ((gravity_anomaly and high_angular) or 
                          (is_weightless and medium_angular) or 
                          (z_spike and high_angular_60))
            
            airtime_conditions.append(is_airborne)
        
        # 一致性过滤
        filtered_conditions = []
        for i in range(len(airtime_conditions)):
            start_idx = max(0, i - 1)
            end_idx = min(len(airtime_conditions), i + 2)
            window_conditions = airtime_conditions[start_idx:end_idx]
            true_ratio = sum(window_conditions) / len(window_conditions)
            filtered_conditions.append(true_ratio >= 0.6)
        
        # 提取滞空事件
        events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = None
        
        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                airtime_end = sensor_data[i-1]
                airtime_end_idx = i - 1
                
                # 计算持续时间
                time_diff = (airtime_end.timestamp - airtime_start.timestamp).total_seconds()
                ms_diff = airtime_end.milliseconds - airtime_start.milliseconds
                duration_ms = int(time_diff * 1000 + ms_diff)
                
                # 时间过滤
                if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                    # 计算事件特征
                    airtime_segment = sensor_data[airtime_start_idx:airtime_end_idx+1]
                    heights = [data.humidity / 10.0 for data in airtime_segment]
                    max_height_change = max(heights) - min(heights)
                    max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                    confidence = self._calculate_confidence(airtime_segment)
                    
                    # 创建事件
                    event = AirtimeEvent(
                        start_time=airtime_start.timestamp,
                        end_time=airtime_end.timestamp,
                        duration_ms=duration_ms,
                        max_height_change=max_height_change,
                        max_acceleration=max_acceleration,
                        confidence=confidence
                    )
                    
                    # 保存索引信息
                    event.start_data_index = airtime_start_idx
                    event.end_data_index = airtime_end_idx
                    
                    events.append(event)
        
        return events
    
    def _calculate_confidence(self, airtime_segment: List[SensorData]) -> float:
        """计算置信度"""
        if not airtime_segment:
            return 0.0
        
        # 失重状态评分
        weightless_count = sum(1 for data in airtime_segment 
                              if abs(data.az) < (self.weightless_ratio * self.standard_gravity))
        weightless_score = min(0.3, (weightless_count / len(airtime_segment)) * 0.3)
        
        # Z轴突变评分
        spike_count = 0
        for i in range(1, len(airtime_segment)):
            z_change = abs(airtime_segment[i].az - airtime_segment[i-1].az)
            if z_change > (self.z_spike_ratio * self.standard_gravity):
                spike_count += 1
        
        z_pattern_score = 0.2 if spike_count >= 2 else (0.1 if spike_count >= 1 else 0.0)
        
        # 重力异常评分
        gravity_anomaly_count = sum(1 for data in airtime_segment 
                                   if abs(abs(data.az) - self.standard_gravity) > self.gravity_threshold)
        gravity_score = min(0.2, (gravity_anomaly_count / len(airtime_segment)) * 0.2)
        
        # 角速度一致性评分
        high_angular_count = sum(1 for data in airtime_segment 
                                if data.total_angular_velocity() > self.angular_velocity_threshold)
        angular_score = min(0.3, (high_angular_count / len(airtime_segment)) * 0.3)
        
        return weightless_score + z_pattern_score + gravity_score + angular_score


def main():
    """滑雪滞空检测器"""
    detector = AirtimeDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    
    if not sensor_data:
        print("无法加载数据文件")
        return
    
    # 检测滞空事件
    events = detector.detect_airtime_events(sensor_data)
    
    # 过滤高置信度事件
    high_confidence_events = [event for event in events if event.confidence >= 0.85]
    
    if not high_confidence_events:
        print("未检测到高置信度滞空事件")
        return
    
    # 输出表格
    print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'置信度':<6}")
    print("-" * 80)
    
    for i, event in enumerate(high_confidence_events, 1):
        start_time_short = event.start_time.strftime('%H:%M:%S.%f')[:-3]
        end_time_short = event.end_time.strftime('%H:%M:%S.%f')[:-3]
        
        print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
              f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
              f"{event.confidence:<6.3f}")


if __name__ == "__main__":
    main()
