#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试时间戳不匹配问题
分析为什么显示的时间戳与持续时间不对应
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from datetime import datetime


def debug_timestamp_mismatch():
    """调试时间戳不匹配问题"""
    print("🔍 调试时间戳不匹配问题")
    print("=" * 80)
    
    # 使用原始的优化检测器
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    print(f"📊 数据概览:")
    print(f"   数据点数量: {len(sensor_data)}")
    print(f"   时间范围: {sensor_data[0].timestamp} - {sensor_data[-1].timestamp}")
    
    # 检测滞空事件
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    if not airtime_events:
        print("❌ 未检测到滞空事件")
        return
    
    print(f"\n🎿 检测到 {len(airtime_events)} 个滞空事件")
    
    # 分析每个事件的时间戳问题
    for i, event in enumerate(airtime_events[:5], 1):  # 只分析前5个
        print(f"\n{'='*60}")
        print(f"🎿 事件 {i} 时间戳分析")
        print(f"{'='*60}")
        
        print(f"📋 事件基本信息:")
        print(f"   事件开始时间: {event.start_time}")
        print(f"   事件结束时间: {event.end_time}")
        print(f"   存储持续时间: {event.duration_ms}ms ({event.duration_seconds:.3f}s)")
        
        # 查找事件开始和结束时间对应的所有数据点
        start_matches = []
        end_matches = []
        
        for j, data in enumerate(sensor_data):
            if data.timestamp == event.start_time:
                start_matches.append((j, data))
            if data.timestamp == event.end_time:
                end_matches.append((j, data))
        
        print(f"\n🔍 匹配的数据点:")
        print(f"   开始时间匹配点数: {len(start_matches)}")
        print(f"   结束时间匹配点数: {len(end_matches)}")
        
        if start_matches:
            print(f"   开始时间匹配点:")
            for j, (idx, data) in enumerate(start_matches):
                print(f"     {j+1}. 索引{idx}: {data.timestamp}, 毫秒{data.milliseconds}")
        
        if end_matches:
            print(f"   结束时间匹配点:")
            for j, (idx, data) in enumerate(end_matches):
                print(f"     {j+1}. 索引{idx}: {data.timestamp}, 毫秒{data.milliseconds}")
        
        # 使用当前的时间戳获取方法
        print(f"\n📅 当前时间戳获取方法结果:")
        
        # 模拟当前的时间戳获取逻辑
        start_timestamp = None
        end_timestamp = None
        start_data_found = None
        end_data_found = None
        
        # 查找开始时间对应的传感器数据（精确匹配）
        for data in sensor_data:
            if data.timestamp == event.start_time:
                if start_data_found is None:  # 取第一个匹配的
                    start_data_found = data
                if data.timestamp == event.end_time and end_data_found is None:
                    end_data_found = data
        
        # 如果开始和结束时间相同，需要找到不同的数据点
        if start_data_found and end_data_found and start_data_found == end_data_found:
            # 重新查找：找到开始时间的第一个匹配点和结束时间的最后一个匹配点
            start_candidates = [data for data in sensor_data if data.timestamp == event.start_time]
            end_candidates = [data for data in sensor_data if data.timestamp == event.end_time]
            
            if len(start_candidates) > 1 or len(end_candidates) > 1:
                start_data_found = start_candidates[0]  # 第一个
                end_data_found = end_candidates[-1]    # 最后一个
        
        if start_data_found:
            start_timestamp = f"{start_data_found.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data_found.milliseconds:03d}"
        if end_data_found:
            end_timestamp = f"{end_data_found.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data_found.milliseconds:03d}"
        
        # 如果找不到精确匹配，使用事件时间本身
        if not start_timestamp:
            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
        if not end_timestamp:
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
        
        print(f"   显示开始时间戳: {start_timestamp}")
        print(f"   显示结束时间戳: {end_timestamp}")
        
        # 从显示的时间戳计算持续时间
        try:
            start_dt = datetime.strptime(start_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            end_dt = datetime.strptime(end_timestamp, '%Y-%m-%d %H:%M:%S.%f')
            display_duration = (end_dt - start_dt).total_seconds()
            
            print(f"\n🧮 时间戳计算验证:")
            print(f"   显示时间戳计算持续时间: {display_duration:.3f}s")
            print(f"   事件存储持续时间: {event.duration_seconds:.3f}s")
            print(f"   差异: {abs(display_duration - event.duration_seconds):.3f}s")
            
            if abs(display_duration - event.duration_seconds) > 0.01:
                print(f"   ❌ 时间戳与持续时间不匹配！")
                
                # 分析可能的原因
                print(f"\n🔍 问题分析:")
                
                # 检查是否使用了错误的数据点
                if start_data_found and end_data_found:
                    actual_duration = detector._calculate_duration_ms(start_data_found, end_data_found)
                    print(f"   用显示数据点计算的持续时间: {actual_duration}ms")
                    print(f"   与事件存储持续时间差异: {abs(actual_duration - event.duration_ms)}ms")
                    
                    if abs(actual_duration - event.duration_ms) > 10:
                        print(f"   ❌ 显示的数据点不是用于计算持续时间的数据点！")
                    else:
                        print(f"   ✅ 显示的数据点是正确的")
                
            else:
                print(f"   ✅ 时间戳与持续时间匹配")
                
        except Exception as e:
            print(f"   ❌ 时间戳解析错误: {e}")


def find_actual_data_points():
    """找到实际用于计算的数据点"""
    print(f"\n🔍 查找实际用于计算的数据点")
    print("=" * 80)
    
    detector = OptimizedAirtimeDetector()
    sensor_data = detector.load_sensor_data('0.txt')
    airtime_events = detector.detect_airtime_events(sensor_data)
    
    if not airtime_events:
        return
    
    # 重新运行检测过程，记录实际使用的数据点
    print("重新运行检测过程，记录实际数据点...")
    
    # 这里需要修改检测器来记录实际使用的数据点索引
    # 由于无法直接修改，我们通过分析来推断
    
    for i, event in enumerate(airtime_events[:3], 1):
        print(f"\n事件 {i}:")
        print(f"   存储持续时间: {event.duration_ms}ms")
        
        # 尝试找到能产生这个持续时间的数据点组合
        found_combination = False
        
        for start_idx in range(len(sensor_data)):
            if sensor_data[start_idx].timestamp != event.start_time:
                continue
                
            for end_idx in range(start_idx + 1, len(sensor_data)):
                if sensor_data[end_idx].timestamp != event.end_time:
                    continue
                
                calculated_duration = detector._calculate_duration_ms(
                    sensor_data[start_idx], sensor_data[end_idx])
                
                if abs(calculated_duration - event.duration_ms) < 5:  # 允许5ms误差
                    print(f"   实际数据点: 索引{start_idx} → 索引{end_idx}")
                    print(f"   实际开始: {sensor_data[start_idx].timestamp}, 毫秒{sensor_data[start_idx].milliseconds}")
                    print(f"   实际结束: {sensor_data[end_idx].timestamp}, 毫秒{sensor_data[end_idx].milliseconds}")
                    print(f"   实际计算持续时间: {calculated_duration}ms")
                    
                    # 生成正确的时间戳
                    correct_start = f"{sensor_data[start_idx].timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{sensor_data[start_idx].milliseconds:03d}"
                    correct_end = f"{sensor_data[end_idx].timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{sensor_data[end_idx].milliseconds:03d}"
                    
                    print(f"   正确开始时间戳: {correct_start}")
                    print(f"   正确结束时间戳: {correct_end}")
                    
                    found_combination = True
                    break
            
            if found_combination:
                break
        
        if not found_combination:
            print(f"   ❌ 未找到匹配的数据点组合")


def main():
    """主函数"""
    debug_timestamp_mismatch()
    find_actual_data_points()
    
    print(f"\n💡 总结:")
    print(f"   问题根源: 显示的时间戳与实际用于计算的数据点不匹配")
    print(f"   解决方案: 需要在事件创建时记录实际使用的数据点索引")
    print(f"   建议: 使用 FixedTimestampDetector 获得准确的时间戳")


if __name__ == "__main__":
    main()
