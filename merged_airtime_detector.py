#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并的滑雪滞空检测器 - 完整版本
包含：基础计算器 + 优化检测器 + 改进边界检测器
保持原始算法逻辑不变
"""

import re
import math
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import numpy as np


@dataclass
class SensorData:
    """传感器数据结构"""
    timestamp: datetime
    milliseconds: int
    # 位置坐标 (可能是相对位置)
    x: float
    y: float  
    z: float
    P: int  # 可能是压力或位置指示器
    
    # 加速度计数据 (mg 单位)
    ax: float  # X轴加速度
    ay: float  # Y轴加速度
    az: float  # Z轴加速度
    
    # 陀螺仪数据 (度/秒)
    gx: float  # X轴角速度
    gy: float  # Y轴角速度
    gz: float  # Z轴角速度
    
    # 磁力计数据
    mx: float  # X轴磁场
    my: float  # Y轴磁场
    mz: float  # Z轴磁场
    
    # 其他传感器数据
    temperature: float  # 温度
    humidity: float     # 湿度
    speed: float        # 速度
    fs: float          # 可能是采样频率
    longitude: float   # 经度
    latitude: float    # 纬度
    gps_height: float  # GPS高度
    
    def total_acceleration(self) -> float:
        """计算总加速度大小"""
        return math.sqrt(self.ax**2 + self.ay**2 + self.az**2)
    
    def total_angular_velocity(self) -> float:
        """计算总角速度大小"""
        return math.sqrt(self.gx**2 + self.gy**2 + self.gz**2)
    
    def acceleration_magnitude_without_gravity(self) -> float:
        """计算去除重力后的加速度大小（假设重力主要在Z轴）"""
        # 假设静止时Z轴加速度约为1000mg（1g）
        gravity_compensated_az = self.az - 1000
        return math.sqrt(self.ax**2 + self.ay**2 + gravity_compensated_az**2)


@dataclass
class AirtimeEvent:
    """滞空事件数据结构"""
    start_time: datetime
    end_time: datetime
    duration_ms: int
    max_height_change: float
    max_acceleration: float
    confidence: float
    max_rotation_speed: float = 0
    avg_rotation_speed: float = 0
    rotation_detected: bool = False
    
    @property
    def duration_seconds(self) -> float:
        """获取持续时间（秒）"""
        return self.duration_ms / 1000.0


class SkiingAirtimeCalculator:
    """滑雪滞空时间计算器基类"""
    
    def __init__(self, 
                 gravity_threshold: float = 800,
                 min_airtime_ms: int = 200,
                 max_airtime_ms: int = 5000,
                 angular_velocity_threshold: float = 1000,
                 confidence_threshold: float = 0.5):
        """
        初始化滞空时间计算器
        
        Args:
            gravity_threshold: 重力异常检测阈值 (mg)
            min_airtime_ms: 最小滞空时间 (毫秒)
            max_airtime_ms: 最大滞空时间 (毫秒)
            angular_velocity_threshold: 角速度阈值 (度/秒)
            confidence_threshold: 置信度阈值
        """
        self.gravity_threshold = gravity_threshold
        self.min_airtime_ms = min_airtime_ms
        self.max_airtime_ms = max_airtime_ms
        self.angular_velocity_threshold = angular_velocity_threshold
        self.confidence_threshold = confidence_threshold
        
        # 物理常数
        self.standard_gravity = 1000  # 标准重力加速度 (mg)
        self.earth_rotation_rate = 15  # 地球自转角速度 (度/小时)
        
        # 滑雪特定参数
        self.typical_jump_height = 2.0  # 典型跳跃高度 (米)
        self.typical_airtime = 1.0      # 典型滞空时间 (秒)
    
    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """
        从文件加载传感器数据
        
        Args:
            filename: 数据文件路径
            
        Returns:
            传感器数据列表
        """
        sensor_data = []
        
        # 正则表达式匹配数据行
        pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: \(null\), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    match = re.match(pattern, line)
                    if match:
                        groups = match.groups()
                        
                        # 解析时间戳
                        timestamp = datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S')
                        milliseconds = int(groups[1])
                        
                        # 创建传感器数据对象
                        data = SensorData(
                            timestamp=timestamp,
                            milliseconds=milliseconds,
                            x=float(groups[2]),
                            y=float(groups[3]),
                            z=float(groups[4]),
                            P=int(groups[5]),
                            ax=float(groups[6]),
                            ay=float(groups[7]),
                            az=float(groups[8]),
                            gx=float(groups[9]),
                            gy=float(groups[10]),
                            gz=float(groups[11]),
                            mx=float(groups[12]),
                            my=float(groups[13]),
                            mz=float(groups[14]),
                            temperature=float(groups[15]),
                            humidity=float(groups[16]),
                            speed=float(groups[17]),
                            fs=0.0,  # null值设为0
                            longitude=float(groups[19]),
                            latitude=float(groups[20]),
                            gps_height=float(groups[21])
                        )
                        
                        sensor_data.append(data)
                        
        except FileNotFoundError:
            print(f"错误：找不到文件 {filename}")
        except Exception as e:
            print(f"读取文件时发生错误：{e}")
        
        return sensor_data
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """
        检测滞空事件
        
        Args:
            sensor_data: 传感器数据列表
            
        Returns:
            滞空事件列表
        """
        if len(sensor_data) < 10:
            return []
        
        airtime_events = []
        
        # 检测重力异常和高角速度的组合
        anomaly_points = []
        for i, data in enumerate(sensor_data):
            # 检测重力异常（Z轴加速度偏离标准重力）
            gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            is_gravity_anomaly = gravity_deviation > self.gravity_threshold
            
            # 检测高角速度
            is_high_angular = data.total_angular_velocity() > self.angular_velocity_threshold
            
            # 组合条件：重力异常且高角速度
            if is_gravity_anomaly and is_high_angular:
                anomaly_points.append(i)
        
        if not anomaly_points:
            return []
        
        # 将连续的异常点组合成事件
        events = self._group_anomaly_points(sensor_data, anomaly_points)
        
        # 过滤和验证事件
        validated_events = []
        for event in events:
            if self._validate_airtime_event(event):
                validated_events.append(event)
        
        return validated_events
    
    def _group_anomaly_points(self, sensor_data: List[SensorData], anomaly_points: List[int]) -> List[AirtimeEvent]:
        """将连续的异常点组合成滞空事件"""
        if not anomaly_points:
            return []
        
        events = []
        current_group = [anomaly_points[0]]
        
        for i in range(1, len(anomaly_points)):
            # 如果当前点与前一个点的间隔小于等于3，认为是连续的
            if anomaly_points[i] - anomaly_points[i-1] <= 3:
                current_group.append(anomaly_points[i])
            else:
                # 处理当前组
                if len(current_group) >= 3:  # 至少需要3个连续点
                    event = self._create_airtime_event(sensor_data, current_group)
                    if event:
                        events.append(event)
                
                # 开始新组
                current_group = [anomaly_points[i]]
        
        # 处理最后一组
        if len(current_group) >= 3:
            event = self._create_airtime_event(sensor_data, current_group)
            if event:
                events.append(event)
        
        return events
    
    def _create_airtime_event(self, sensor_data: List[SensorData], point_indices: List[int]) -> Optional[AirtimeEvent]:
        """从异常点创建滞空事件"""
        if not point_indices:
            return None
        
        start_idx = point_indices[0]
        end_idx = point_indices[-1]
        
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]
        
        # 计算持续时间
        duration_ms = self._calculate_duration_ms(start_data, end_data)
        
        # 计算事件段的数据
        event_data = sensor_data[start_idx:end_idx+1]
        
        # 计算最大高度变化（使用湿度传感器作为高度代理）
        heights = [data.humidity for data in event_data]
        max_height_change = max(heights) - min(heights)
        
        # 计算最大加速度
        max_acceleration = max(data.total_acceleration() for data in event_data)
        
        # 计算置信度
        confidence = self._calculate_confidence(event_data)
        
        # 计算旋转指标
        rotation_metrics = self._calculate_rotation_metrics(event_data)
        
        return AirtimeEvent(
            start_time=start_data.timestamp,
            end_time=end_data.timestamp,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=confidence,
            max_rotation_speed=rotation_metrics['max_rotation_speed'],
            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
            rotation_detected=rotation_metrics['rotation_detected']
        )
    
    def _calculate_duration_ms(self, start_data: SensorData, end_data: SensorData) -> int:
        """计算两个数据点之间的持续时间（毫秒）"""
        time_diff = (end_data.timestamp - start_data.timestamp).total_seconds()
        ms_diff = end_data.milliseconds - start_data.milliseconds
        return int(time_diff * 1000 + ms_diff)
    
    def _calculate_confidence(self, event_data: List[SensorData]) -> float:
        """计算滞空事件的置信度"""
        if not event_data:
            return 0.0
        
        confidence_factors = []
        
        # 因子1：重力异常的一致性
        gravity_anomalies = 0
        for data in event_data:
            deviation = abs(abs(data.az) - self.standard_gravity)
            if deviation > self.gravity_threshold:
                gravity_anomalies += 1
        
        gravity_consistency = gravity_anomalies / len(event_data)
        confidence_factors.append(gravity_consistency * 0.4)
        
        # 因子2：角速度的强度
        avg_angular = sum(data.total_angular_velocity() for data in event_data) / len(event_data)
        angular_factor = min(1.0, avg_angular / (self.angular_velocity_threshold * 2))
        confidence_factors.append(angular_factor * 0.3)
        
        # 因子3：持续时间的合理性
        duration_ms = self._calculate_duration_ms(event_data[0], event_data[-1])
        if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
            duration_factor = 1.0
        else:
            duration_factor = 0.5
        confidence_factors.append(duration_factor * 0.2)
        
        # 因子4：高度变化
        heights = [data.humidity for data in event_data]
        height_change = max(heights) - min(heights)
        height_factor = min(1.0, height_change / 100)  # 假设100为合理的高度变化
        confidence_factors.append(height_factor * 0.1)
        
        return sum(confidence_factors)
    
    def _calculate_rotation_metrics(self, event_data: List[SensorData]) -> Dict[str, float]:
        """计算旋转相关指标"""
        if not event_data:
            return {'max_rotation_speed': 0, 'avg_rotation_speed': 0, 'rotation_detected': False}
        
        # 计算Z轴角速度（主要旋转轴）
        z_angular_velocities = [abs(data.gz) for data in event_data]
        
        max_rotation_speed = max(z_angular_velocities)
        avg_rotation_speed = sum(z_angular_velocities) / len(z_angular_velocities)
        
        # 判断是否检测到明显旋转
        rotation_detected = avg_rotation_speed > self.angular_velocity_threshold * 0.5
        
        return {
            'max_rotation_speed': max_rotation_speed,
            'avg_rotation_speed': avg_rotation_speed,
            'rotation_detected': rotation_detected
        }
    
    def _validate_airtime_event(self, event: AirtimeEvent) -> bool:
        """验证滞空事件的有效性"""
        # 检查持续时间
        if not (self.min_airtime_ms <= event.duration_ms <= self.max_airtime_ms):
            return False
        
        # 检查置信度
        if event.confidence < self.confidence_threshold:
            return False
        
        return True


class OptimizedAirtimeDetector(SkiingAirtimeCalculator):
    """优化的滑雪滞空检测器"""

    def __init__(self,
                 gravity_threshold: float = 600,        # Z轴重力偏差阈值
                 angular_velocity_threshold: float = 800,  # 角速度阈值
                 min_airtime_ms: int = 150,             # 最小滞空时间
                 max_airtime_ms: int = 3000,            # 最大滞空时间
                 weightless_ratio: float = 0.15,       # 失重判断比例（15%重力）
                 z_spike_ratio: float = 0.6,           # Z轴突变判断比例
                 consistency_window: int = 3):          # 一致性检查窗口

        super().__init__(gravity_threshold, min_airtime_ms, max_airtime_ms,
                        angular_velocity_threshold, 0.5)

        self.weightless_ratio = weightless_ratio
        self.z_spike_ratio = z_spike_ratio
        self.consistency_window = consistency_window

    def _smooth_z_acceleration(self, sensor_data: List[SensorData], window_size: int = 3) -> List[float]:
        """平滑Z轴加速度数据"""
        z_values = [data.az for data in sensor_data]

        if len(z_values) < window_size:
            return z_values

        smoothed = []
        for i in range(len(z_values)):
            start = max(0, i - window_size // 2)
            end = min(len(z_values), i + window_size // 2 + 1)
            smoothed.append(sum(z_values[start:end]) / (end - start))

        return smoothed

    def _detect_z_axis_patterns(self, sensor_data: List[SensorData]) -> List[dict]:
        """检测Z轴加速度模式"""
        smoothed_z = self._smooth_z_acceleration(sensor_data)
        patterns = []

        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]

            # 1. 重力偏差检测
            z_gravity_deviation = abs(abs(z_smooth) - self.standard_gravity)
            gravity_anomaly = z_gravity_deviation > self.gravity_threshold

            # 2. 失重状态检测（Z轴加速度很小）
            weightless_threshold = self.standard_gravity * self.weightless_ratio
            is_weightless = abs(z_smooth) < weightless_threshold

            # 3. Z轴突变检测（起跳/落地特征）
            z_spike = False
            if i > 0:
                z_change = abs(z_smooth - smoothed_z[i-1])
                spike_threshold = self.standard_gravity * self.z_spike_ratio
                z_spike = z_change > spike_threshold

            # 4. 角速度检测
            angular_velocity = data.total_angular_velocity()
            high_angular = angular_velocity > self.angular_velocity_threshold

            patterns.append({
                'gravity_anomaly': gravity_anomaly,
                'is_weightless': is_weightless,
                'z_spike': z_spike,
                'high_angular': high_angular,
                'z_deviation': z_gravity_deviation,
                'angular_velocity': angular_velocity,
                'z_value': z_smooth
            })

        return patterns

    def _apply_consistency_filter(self, conditions: List[bool]) -> List[bool]:
        """应用一致性过滤，减少单点噪声"""
        if len(conditions) < self.consistency_window:
            return conditions

        filtered = []
        for i in range(len(conditions)):
            start = max(0, i - self.consistency_window // 2)
            end = min(len(conditions), i + self.consistency_window // 2 + 1)

            window_conditions = conditions[start:end]
            # 窗口内超过60%的点满足条件才认为有效
            true_ratio = sum(window_conditions) / len(window_conditions)
            filtered.append(true_ratio >= 0.6)

        return filtered

    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """优化的滞空事件检测"""
        if len(sensor_data) < 5:
            return []

        print("使用优化的Z轴重力检测算法...")

        # 检测Z轴模式
        patterns = self._detect_z_axis_patterns(sensor_data)

        # 定义多种滞空判断条件
        conditions = []
        for pattern in patterns:
            # 条件1: 重力异常 + 高角速度
            condition1 = pattern['gravity_anomaly'] and pattern['high_angular']

            # 条件2: 失重状态 + 中等角速度
            condition2 = pattern['is_weightless'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.4

            # 条件3: Z轴突变 + 较高角速度（起跳/落地瞬间）
            condition3 = pattern['z_spike'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.6

            # 综合判断
            is_airborne = condition1 or condition2 or condition3
            conditions.append(is_airborne)

        # 应用一致性过滤
        filtered_conditions = self._apply_consistency_filter(conditions)

        # 提取滞空事件
        airtime_events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = 0

        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                airtime_end = sensor_data[i-1]
                airtime_end_idx = i - 1

                # 计算持续时间
                duration_ms = self._calculate_duration_ms(airtime_start, airtime_end)

                # 时间过滤
                if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                    # 计算事件特征
                    airtime_segment = sensor_data[airtime_start_idx:airtime_end_idx+1]
                    max_height_change = self._calculate_max_height_change(airtime_segment)
                    max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                    confidence = self._calculate_optimized_confidence(airtime_segment)

                    # 计算旋转指标
                    rotation_metrics = self._calculate_rotation_metrics(airtime_segment)

                    # 创建事件
                    event = AirtimeEvent(
                        start_time=airtime_start.timestamp,
                        end_time=airtime_end.timestamp,
                        duration_ms=duration_ms,
                        max_height_change=max_height_change,
                        max_acceleration=max_acceleration,
                        confidence=confidence,
                        max_rotation_speed=rotation_metrics['max_rotation_speed'],
                        avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
                        rotation_detected=rotation_metrics['rotation_detected']
                    )

                    # 保存索引信息用于后续处理
                    event.start_data_index = airtime_start_idx
                    event.end_data_index = airtime_end_idx

                    airtime_events.append(event)

        print(f"优化算法检测到 {len(airtime_events)} 个滞空事件")
        return airtime_events

    def _calculate_max_height_change(self, airtime_segment: List[SensorData]) -> float:
        """计算滞空期间的最大高度变化"""
        # 使用湿度传感器数据作为高度代理，除以10获取真实高度值
        heights = [data.humidity / 10.0 for data in airtime_segment]
        return max(heights) - min(heights)

    def _calculate_optimized_confidence(self, airtime_segment: List[SensorData]) -> float:
        """计算优化的置信度"""
        if not airtime_segment:
            return 0.0

        # 失重状态评分（30%权重）
        weightless_count = 0
        for data in airtime_segment:
            if abs(data.az) < (self.weightless_ratio * self.standard_gravity):
                weightless_count += 1

        weightless_score = min(0.3, (weightless_count / len(airtime_segment)) * 0.3)

        # Z轴突变模式评分（20%权重）
        spike_count = 0
        smoothed_z = self._smooth_z_acceleration(airtime_segment)
        for i in range(1, len(smoothed_z)):
            z_change = abs(smoothed_z[i] - smoothed_z[i-1])
            if z_change > (self.z_spike_ratio * self.standard_gravity):
                spike_count += 1

        if spike_count >= 2:  # 至少2次突变（起跳+落地）
            z_pattern_score = 0.2
        elif spike_count >= 1:
            z_pattern_score = 0.1
        else:
            z_pattern_score = 0.0

        # 重力异常一致性评分（20%权重）
        gravity_anomaly_count = 0
        for data in airtime_segment:
            z_deviation = abs(abs(data.az) - self.standard_gravity)
            if z_deviation > self.gravity_threshold:
                gravity_anomaly_count += 1

        gravity_score = min(0.2, (gravity_anomaly_count / len(airtime_segment)) * 0.2)

        # 角速度一致性评分（30%权重）
        high_angular_count = 0
        for data in airtime_segment:
            if data.total_angular_velocity() > self.angular_velocity_threshold:
                high_angular_count += 1

        angular_score = min(0.3, (high_angular_count / len(airtime_segment)) * 0.3)

        return weightless_score + z_pattern_score + gravity_score + angular_score


class ImprovedBoundaryDetector(OptimizedAirtimeDetector):
    """改进的边界检测器"""

    def __init__(self):
        super().__init__()

        # 多级阈值设计
        self.gravity_threshold_strict = 600      # 严格阈值（核心滞空）
        self.gravity_threshold_loose = 300       # 宽松阈值（边界扩展）
        self.angular_threshold_strict = 800      # 严格角速度阈值
        self.angular_threshold_loose = 400       # 宽松角速度阈值

        # 边界扩展参数
        self.pre_jump_window = 8                 # 起跳前检测窗口（数据点）
        self.post_landing_window = 8             # 落地后检测窗口（数据点）
        self.boundary_extension_threshold = 0.3  # 边界扩展评分阈值

        # 渐进式检测参数
        self.gradient_threshold = 200            # 加速度梯度阈值
        self.momentum_threshold = 0.6            # 动量变化阈值

    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空事件检测"""
        if len(sensor_data) < 10:
            return []

        # 第一步：使用基础算法检测核心滞空区间
        core_events = self.detect_airtime_events(sensor_data)

        if not core_events:
            return []

        # 第二步：对每个核心区间进行边界扩展
        improved_events = []

        for i, event in enumerate(core_events):
            # 获取核心区间的索引
            core_start_idx = getattr(event, 'start_data_index', None)
            core_end_idx = getattr(event, 'end_data_index', None)

            if core_start_idx is None or core_end_idx is None:
                # 如果没有索引信息，尝试通过时间戳查找
                core_start_idx, core_end_idx = self._find_event_indices(sensor_data, event)

            if core_start_idx is None or core_end_idx is None:
                improved_events.append(event)
                continue

            # 检测改进的起跳点和落地点
            improved_start_idx = self.detect_takeoff_preparation(sensor_data, core_start_idx)
            improved_end_idx = self.detect_landing_completion(sensor_data, core_end_idx)

            # 创建改进的事件
            improved_event = self._create_improved_event(
                sensor_data, improved_start_idx, improved_end_idx, event)

            improved_events.append(improved_event)

        # 第三步：合并重叠事件
        merged_events = self.merge_overlapping_events(improved_events, sensor_data)

        return merged_events

    def _find_event_indices(self, sensor_data: List[SensorData], event: AirtimeEvent) -> Tuple[Optional[int], Optional[int]]:
        """通过时间戳查找事件在数据中的索引"""
        start_idx = None
        end_idx = None

        for i, data in enumerate(sensor_data):
            # 匹配开始时间
            if (start_idx is None and
                data.timestamp == event.start_time and
                data.milliseconds == event.start_time.microsecond // 1000):
                start_idx = i

            # 匹配结束时间
            if (data.timestamp == event.end_time and
                data.milliseconds == event.end_time.microsecond // 1000):
                end_idx = i
                break

        return start_idx, end_idx

    def detect_takeoff_preparation(self, sensor_data: List[SensorData], core_start_idx: int) -> int:
        """检测起跳准备阶段，向前扩展边界"""
        best_start_idx = core_start_idx

        # 向前搜索起跳准备信号
        search_start = max(0, core_start_idx - self.pre_jump_window)

        for i in range(core_start_idx - 1, search_start - 1, -1):
            if i < 0:
                break

            data = sensor_data[i]

            # 计算起跳准备评分
            prep_score = self._calculate_takeoff_prep_score(sensor_data, i, core_start_idx)

            if prep_score > self.boundary_extension_threshold:
                best_start_idx = i
            else:
                # 如果评分下降，停止搜索
                if i < core_start_idx - 3:  # 至少检查3个点后再停止
                    break

        return best_start_idx

    def detect_landing_completion(self, sensor_data: List[SensorData], core_end_idx: int) -> int:
        """检测落地完成阶段，向后扩展边界"""
        best_landing_idx = core_end_idx

        # 向后搜索落地完成信号
        search_end = min(len(sensor_data), core_end_idx + self.post_landing_window)

        for i in range(core_end_idx + 1, search_end):
            if i >= len(sensor_data):
                break

            data = sensor_data[i]

            # 计算落地完成评分
            landing_score = self._calculate_landing_completion_score(sensor_data, i, core_end_idx)

            if landing_score > self.boundary_extension_threshold:
                best_landing_idx = i
            else:
                # 如果评分下降，停止搜索
                if i > core_end_idx + 3:  # 至少检查3个点后再停止
                    break

        return best_landing_idx

    def _calculate_takeoff_prep_score(self, sensor_data: List[SensorData],
                                    current_idx: int, core_start_idx: int) -> float:
        """计算起跳准备阶段的评分"""
        if current_idx < 0 or current_idx >= len(sensor_data):
            return 0.0

        data = sensor_data[current_idx]
        score = 0.0

        # 1. 加速度梯度检测（准备起跳时加速度开始变化）
        if current_idx > 0:
            prev_data = sensor_data[current_idx - 1]
            az_gradient = abs(data.az - prev_data.az)
            if az_gradient > self.gradient_threshold:
                score += 0.3

        # 2. 角速度增加趋势
        angular_vel = data.total_angular_velocity()
        if angular_vel > self.angular_threshold_loose:
            score += 0.2

        # 3. Z轴重力偏差（开始偏离正常重力）
        z_deviation = abs(abs(data.az) - self.standard_gravity)
        if z_deviation > self.gravity_threshold_loose:
            score += 0.3

        # 4. 距离核心区间的合理性（越近评分越高）
        distance_factor = 1.0 - (core_start_idx - current_idx) / self.pre_jump_window
        score *= distance_factor

        return score

    def _calculate_landing_completion_score(self, sensor_data: List[SensorData],
                                          current_idx: int, core_end_idx: int) -> float:
        """计算落地完成阶段的评分"""
        if current_idx >= len(sensor_data):
            return 0.0

        data = sensor_data[current_idx]
        score = 0.0

        # 1. 加速度稳定性检测（落地后加速度趋于稳定）
        if current_idx < len(sensor_data) - 1:
            next_data = sensor_data[current_idx + 1]
            az_stability = abs(data.az - next_data.az)
            if az_stability < self.gradient_threshold:
                score += 0.3

        # 2. 角速度衰减
        angular_vel = data.total_angular_velocity()
        if angular_vel > self.angular_threshold_loose:
            score += 0.2

        # 3. Z轴重力恢复（逐渐回到正常重力）
        z_deviation = abs(abs(data.az) - self.standard_gravity)
        if z_deviation > self.gravity_threshold_loose:
            score += 0.3

        # 4. 距离核心区间的合理性
        distance_factor = 1.0 - (current_idx - core_end_idx) / self.post_landing_window
        score *= distance_factor

        return score

    def _create_improved_event(self, sensor_data: List[SensorData],
                             start_idx: int, end_idx: int,
                             original_event: AirtimeEvent) -> AirtimeEvent:
        """创建改进的滞空事件"""
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]

        # 计算新的持续时间
        duration_ms = self._calculate_duration_ms(start_data, end_data)

        # 重新计算事件特征
        airtime_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(airtime_segment)
        max_acceleration = max(d.total_acceleration() for d in airtime_segment)

        # 创建新事件
        improved_event = AirtimeEvent(
            start_time=start_data.timestamp,
            end_time=end_data.timestamp,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=original_event.confidence  # 保持原置信度
        )

        # 保存索引信息
        improved_event.start_data_index = start_idx
        improved_event.end_data_index = end_idx

        return improved_event

    def merge_overlapping_events(self, events: List[AirtimeEvent],
                               sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """合并重叠的滞空事件"""
        if len(events) <= 1:
            return events

        # 按开始时间排序
        sorted_events = sorted(events, key=lambda e: e.start_time)
        merged_events = []

        i = 0
        while i < len(sorted_events):
            current_event = sorted_events[i]

            # 查找与当前事件重叠的所有事件
            overlapping_events = [current_event]
            j = i + 1

            while j < len(sorted_events):
                next_event = sorted_events[j]

                # 检查时间重叠
                if self._events_overlap(current_event, next_event):
                    overlapping_events.append(next_event)
                    j += 1
                else:
                    break

            # 如果有重叠事件，进行合并
            if len(overlapping_events) > 1:
                merged_event = self._merge_events(overlapping_events, sensor_data)
                merged_events.append(merged_event)
            else:
                merged_events.append(current_event)

            # 跳过已处理的重叠事件
            i = j if len(overlapping_events) > 1 else i + 1

        return merged_events

    def _events_overlap(self, event1: AirtimeEvent, event2: AirtimeEvent) -> bool:
        """检查两个事件是否重叠"""
        # 计算时间重叠
        start1_ms = event1.start_time.timestamp() * 1000 + event1.start_time.microsecond // 1000
        end1_ms = event1.end_time.timestamp() * 1000 + event1.end_time.microsecond // 1000
        start2_ms = event2.start_time.timestamp() * 1000 + event2.start_time.microsecond // 1000
        end2_ms = event2.end_time.timestamp() * 1000 + event2.end_time.microsecond // 1000

        # 检查是否有时间重叠
        return not (end1_ms < start2_ms or end2_ms < start1_ms)

    def _merge_events(self, overlapping_events: List[AirtimeEvent],
                     sensor_data: List[SensorData]) -> AirtimeEvent:
        """合并多个重叠事件为一个事件"""
        # 找到最早开始时间和最晚结束时间
        earliest_start = min(event.start_time for event in overlapping_events)
        latest_end = max(event.end_time for event in overlapping_events)

        # 找到对应的数据索引
        start_idx = None
        end_idx = None

        for i, data in enumerate(sensor_data):
            if data.timestamp == earliest_start and start_idx is None:
                start_idx = i
            if data.timestamp == latest_end:
                end_idx = i

        # 如果找不到精确匹配，使用最接近的索引
        if start_idx is None:
            start_idx = 0
        if end_idx is None:
            end_idx = len(sensor_data) - 1

        # 重新计算合并后的事件特征
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]
        duration_ms = self._calculate_duration_ms(start_data, end_data)

        airtime_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(airtime_segment)
        max_acceleration = max(d.total_acceleration() for d in airtime_segment)

        # 使用最高置信度
        max_confidence = max(event.confidence for event in overlapping_events)

        # 创建合并后的事件
        merged_event = AirtimeEvent(
            start_time=earliest_start,
            end_time=latest_end,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=max_confidence
        )

        # 保存索引信息
        merged_event.start_data_index = start_idx
        merged_event.end_data_index = end_idx

        return merged_event

    def _calculate_detailed_rotation(self, airtime_segment: List[SensorData],
                                   duration_seconds: float) -> Dict:
        """计算详细的转体信息，包括分段转速"""
        yaw_angles = [data.z for data in airtime_segment]

        # 计算累积旋转角度
        cumulative_rotation = 0.0
        angle_changes = []

        for j in range(1, len(yaw_angles)):
            angle_change = yaw_angles[j] - yaw_angles[j-1]

            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360

            cumulative_rotation += angle_change
            angle_changes.append(angle_change)

        # 转体角度和基本信息
        total_rotation_angle = abs(cumulative_rotation)
        avg_rotation_speed = total_rotation_angle / 360.0 / duration_seconds if duration_seconds > 0 else 0

        # 判断转动方向
        if cumulative_rotation > 50:
            rotation_direction = 'clockwise'
        elif cumulative_rotation < -50:
            rotation_direction = 'counterclockwise'
        else:
            rotation_direction = 'none'
            total_rotation_angle = 0.0
            avg_rotation_speed = 0.0

        # 计算分段转速（当转体角度大于360度时）
        first_half_speed = 0.0
        second_half_speed = 0.0

        if total_rotation_angle > 360 and len(angle_changes) > 2:
            # 目标角度：总角度的一半
            target_half_angle = total_rotation_angle / 2.0

            # 寻找前半段结束点（累积角度达到一半时）
            cumulative_angle = 0.0
            split_point = 0

            for i, angle_change in enumerate(angle_changes):
                cumulative_angle += abs(angle_change)
                if cumulative_angle >= target_half_angle:
                    split_point = i + 1
                    break

            # 确保分割点有效
            if split_point == 0:
                split_point = len(angle_changes) // 2
            elif split_point >= len(angle_changes):
                split_point = len(angle_changes) - 1

            # 分割角度变化数组
            first_half_changes = angle_changes[:split_point]
            second_half_changes = angle_changes[split_point:]

            # 计算前半段和后半段的实际角度
            first_half_angle = sum(abs(change) for change in first_half_changes)
            second_half_angle = sum(abs(change) for change in second_half_changes)

            # 计算时间分配（按分割点比例分配）
            first_half_duration = (split_point / len(angle_changes)) * duration_seconds
            second_half_duration = duration_seconds - first_half_duration

            # 计算分段转速
            if first_half_duration > 0:
                first_half_speed = first_half_angle / 360.0 / first_half_duration
            if second_half_duration > 0:
                second_half_speed = second_half_angle / 360.0 / second_half_duration

        return {
            'total_angle': total_rotation_angle,
            'avg_speed': avg_rotation_speed,
            'direction': rotation_direction,
            'first_half_speed': first_half_speed,
            'second_half_speed': second_half_speed,
            'has_split_analysis': total_rotation_angle > 360
        }

    def _calculate_horizontal_distance(self, airtime_segment: List[SensorData],
                                     duration_seconds: float) -> str:
        """计算水平距离，使用速度speed与时间段相乘"""

        # 获取所有速度值
        speeds = [data.speed for data in airtime_segment]

        # 检查是否有非零速度
        non_zero_speeds = [s for s in speeds if s != 0]

        if not non_zero_speeds:
            return "null"

        # 计算平均速度（排除零值）
        avg_speed = sum(non_zero_speeds) / len(non_zero_speeds)

        # 计算水平距离 = 平均速度 × 时间
        horizontal_distance = avg_speed * duration_seconds

        return f"{horizontal_distance:.2f}m"

    def parse_sensor_line(self, line: str) -> Optional[SensorData]:
        """解析单行传感器数据 - 使用原始的多格式解析逻辑"""
        try:
            # 处理null值的辅助函数
            def parse_value(value_str, default=0.0):
                if value_str == '(null)':
                    return default
                return float(value_str)

            # 尝试格式1：0.txt格式（不包含fs和as字段）
            pattern_0txt = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+), ca:\(null\), cs:\(null\)'

            match = re.match(pattern_0txt, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=float(groups[12]),
                    my=float(groups[13]),
                    mz=float(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),  # h字段
                    speed=float(groups[17]),
                    fs=0.0,  # 0.txt格式没有fs字段，设为默认值
                    longitude=float(groups[18]),
                    latitude=float(groups[19]),
                    gps_height=float(groups[20])
                )

            # 尝试格式2：包含fs和as字段的格式
            pattern_with_fs_as = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), as:(-?\d+\.?\d*), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'

            match = re.match(pattern_with_fs_as, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    # groups[19] 是as字段，暂时跳过
                    longitude=float(groups[20]),
                    latitude=float(groups[21]),
                    gps_height=float(groups[22])
                )

            # 尝试旧格式（不包含as字段）
            old_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (\(null\)|-?\d+), my: (\(null\)|-?\d+), mz: (\(null\)|-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+), fs: (\(null\)|-?\d+), lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'

            match = re.match(old_pattern, line.strip())
            if match:
                groups = match.groups()
                return SensorData(
                    timestamp=datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S'),
                    milliseconds=int(groups[1]),
                    x=float(groups[2]),
                    y=float(groups[3]),
                    z=float(groups[4]),
                    P=int(groups[5]),
                    ax=float(groups[6]),
                    ay=float(groups[7]),
                    az=float(groups[8]),
                    gx=float(groups[9]),
                    gy=float(groups[10]),
                    gz=float(groups[11]),
                    mx=parse_value(groups[12]),
                    my=parse_value(groups[13]),
                    mz=parse_value(groups[14]),
                    temperature=float(groups[15]),
                    humidity=float(groups[16]),
                    speed=float(groups[17]),
                    fs=parse_value(groups[18]),
                    longitude=float(groups[19]),
                    latitude=float(groups[20]),
                    gps_height=float(groups[21])
                )

            # 两种格式都不匹配
            return None

        except (ValueError, IndexError) as e:
            print(f"解析数据行失败: {line[:50]}... 错误: {e}")
            return None

    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """从文件加载传感器数据 - 使用原始的解析逻辑"""
        sensor_data = []
        failed_lines = 0

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        data = self.parse_sensor_line(line)
                        if data:
                            sensor_data.append(data)
                        else:
                            failed_lines += 1
                            if line_num <= 10:  # 只对前10行显示警告
                                print(f"第{line_num}行解析失败: {line[:50]}...")

        except FileNotFoundError:
            print(f"文件未找到: {filename}")
            return []
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return []

        print(f"成功加载 {len(sensor_data)} 条传感器数据")
        if failed_lines > 0:
            print(f"解析失败的行数: {failed_lines}")
        return sensor_data


def main():
    """滑雪滞空检测器主函数"""
    # 创建检测器
    detector = ImprovedBoundaryDetector()

    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')

    if not sensor_data:
        print("无法加载数据文件")
        return

    # 检测滞空事件
    events = detector.detect_improved_airtime_events(sensor_data)

    # 过滤高置信度事件
    high_confidence_events = [event for event in events if event.confidence >= 0.85]

    if not high_confidence_events:
        print("未检测到高置信度滞空事件")
        print(f"检测到 {len(events)} 个滞空事件，但置信度都低于0.85")
        if events:
            print("显示所有检测到的事件:")
            # 显示所有事件，不管置信度
            high_confidence_events = events

    # 输出表格
    print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'水平距离':<10} {'置信度':<6} {'转体角度(°)':<10} {'转体速度(圈/s)':<12} {'转动方向':<10}")
    print("-" * 120)

    for i, event in enumerate(high_confidence_events, 1):
        # 获取精确时间戳
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            start_data = sensor_data[event.start_data_index]
            end_data = sensor_data[event.end_data_index]

            start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
            end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"

            # 计算转体信息
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            rotation_info = detector._calculate_detailed_rotation(airtime_segment, event.duration_seconds)

            rotation_angle = rotation_info['total_angle']
            rotation_speed = rotation_info['avg_speed']
            rotation_direction = rotation_info['direction']
            first_half_speed = rotation_info['first_half_speed']
            second_half_speed = rotation_info['second_half_speed']
            has_split_analysis = rotation_info['has_split_analysis']

            # 计算水平距离
            horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
        else:
            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
            rotation_angle = 0.0
            rotation_speed = 0.0
            rotation_direction = 'none'
            horizontal_distance = "null"
            has_split_analysis = False
            first_half_speed = 0.0
            second_half_speed = 0.0

        # 构建转体速度显示字符串
        if has_split_analysis:
            speed_display = f"{rotation_speed:.2f}(前:{first_half_speed:.2f}|后:{second_half_speed:.2f})"
        else:
            speed_display = f"{rotation_speed:.2f}"

        # 缩短时间戳显示（只显示时分秒.毫秒）
        start_time_short = start_timestamp.split(' ')[1]  # 只取时间部分
        end_time_short = end_timestamp.split(' ')[1]      # 只取时间部分

        print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
              f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
              f"{horizontal_distance:<10} {event.confidence:<6.3f} {rotation_angle:<10.1f} "
              f"{speed_display:<12} {rotation_direction:<10}")


if __name__ == "__main__":
    main()
