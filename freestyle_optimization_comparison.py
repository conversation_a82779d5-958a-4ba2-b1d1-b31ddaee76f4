#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自由式滑雪优化效果对比分析
对比原算法和自由式专用算法的检测效果
"""

from skiing_airtime_calculator import SkiingAirtimeCalculator
from optimized_airtime_detector import OptimizedAirtimeDetector
from freestyle_airtime_detector import FreestyleAirtimeDetector


def compare_detection_algorithms():
    """对比不同检测算法的效果"""
    print("🎪 自由式滑雪检测算法优化效果对比")
    print("=" * 80)
    
    filename = '0.txt'
    
    # 1. 原始算法
    print("\n🔸 原始算法 (通用滑雪检测):")
    original_detector = SkiingAirtimeCalculator()
    sensor_data = original_detector.load_sensor_data(filename)
    
    if sensor_data:
        original_events = original_detector.detect_airtime_events(sensor_data)
        original_stats = original_detector.analyze_airtime_statistics(original_events)
        
        print(f"   检测事件: {original_stats['total_events']} 次")
        print(f"   有效事件: {original_stats['valid_events']} 次")
        print(f"   总滞空时间: {original_stats['total_airtime_seconds']:.2f} 秒")
        print(f"   平均置信度: {sum(e.confidence for e in original_events)/len(original_events):.3f}" if original_events else "   平均置信度: N/A")
    
    # 2. 优化算法
    print("\n🔹 优化算法 (多重条件检测):")
    optimized_detector = OptimizedAirtimeDetector()
    
    if sensor_data:
        optimized_events = optimized_detector.detect_airtime_events(sensor_data)
        optimized_stats = optimized_detector.analyze_airtime_statistics(optimized_events)
        
        print(f"   检测事件: {optimized_stats['total_events']} 次")
        print(f"   有效事件: {optimized_stats['valid_events']} 次")
        print(f"   总滞空时间: {optimized_stats['total_airtime_seconds']:.2f} 秒")
        print(f"   平均置信度: {sum(e.confidence for e in optimized_events)/len(optimized_events):.3f}" if optimized_events else "   平均置信度: N/A")
    
    # 3. 自由式专用算法
    print("\n🎪 自由式专用算法 (针对自由式滑雪优化):")
    freestyle_detector = FreestyleAirtimeDetector()
    
    if sensor_data:
        freestyle_events = freestyle_detector.detect_freestyle_airtime_events(sensor_data)
        freestyle_stats = freestyle_detector.analyze_airtime_statistics(freestyle_events)
        
        print(f"   检测事件: {freestyle_stats['total_events']} 次")
        print(f"   有效事件: {freestyle_stats['valid_events']} 次")
        print(f"   总滞空时间: {freestyle_stats['total_airtime_seconds']:.2f} 秒")
        print(f"   平均置信度: {sum(e.confidence for e in freestyle_events)/len(freestyle_events):.3f}" if freestyle_events else "   平均置信度: N/A")
    
    # 对比分析
    print(f"\n📊 算法对比分析:")
    print(f"{'算法类型':<20} {'检测事件':<10} {'有效事件':<10} {'总时长(s)':<12} {'平均置信度':<12}")
    print("-" * 70)
    
    original_count = original_stats['total_events'] if 'original_stats' in locals() else 0
    original_valid = original_stats['valid_events'] if 'original_stats' in locals() else 0
    original_time = original_stats['total_airtime_seconds'] if 'original_stats' in locals() else 0
    original_conf = sum(e.confidence for e in original_events)/len(original_events) if 'original_events' in locals() and original_events else 0
    
    optimized_count = optimized_stats['total_events'] if 'optimized_stats' in locals() else 0
    optimized_valid = optimized_stats['valid_events'] if 'optimized_stats' in locals() else 0
    optimized_time = optimized_stats['total_airtime_seconds'] if 'optimized_stats' in locals() else 0
    optimized_conf = sum(e.confidence for e in optimized_events)/len(optimized_events) if 'optimized_events' in locals() and optimized_events else 0
    
    freestyle_count = freestyle_stats['total_events'] if 'freestyle_stats' in locals() else 0
    freestyle_valid = freestyle_stats['valid_events'] if 'freestyle_stats' in locals() else 0
    freestyle_time = freestyle_stats['total_airtime_seconds'] if 'freestyle_stats' in locals() else 0
    freestyle_conf = sum(e.confidence for e in freestyle_events)/len(freestyle_events) if 'freestyle_events' in locals() and freestyle_events else 0
    
    print(f"{'原始算法':<20} {original_count:<10} {original_valid:<10} {original_time:<12.2f} {original_conf:<12.3f}")
    print(f"{'优化算法':<20} {optimized_count:<10} {optimized_valid:<10} {optimized_time:<12.2f} {optimized_conf:<12.3f}")
    print(f"{'自由式专用':<20} {freestyle_count:<10} {freestyle_valid:<10} {freestyle_time:<12.2f} {freestyle_conf:<12.3f}")


def analyze_freestyle_optimization_features():
    """分析自由式优化特性"""
    print(f"\n🎯 自由式滑雪优化特性详解")
    print("=" * 80)
    
    print(f"\n📋 参数优化对比:")
    print(f"{'参数':<25} {'通用算法':<15} {'自由式优化':<15} {'优化说明':<30}")
    print("-" * 90)
    print(f"{'gravity_threshold':<25} {'800mg':<15} {'300mg':<15} {'更敏感的重力检测':<30}")
    print(f"{'angular_velocity_threshold':<25} {'1000°/s':<15} {'400°/s':<15} {'更低的角速度阈值':<30}")
    print(f"{'min_airtime_ms':<25} {'150ms':<15} {'80ms':<15} {'检测更短的滞空':<30}")
    print(f"{'max_airtime_ms':<25} {'3000ms':<15} {'10000ms':<15} {'支持更长的大跳台':<30}")
    
    print(f"\n🎪 自由式专用功能:")
    print(f"   ✅ 起跳前动作模式检测 - 分析蹲下-起跳的压缩-释放模式")
    print(f"   ✅ 落地冲击模式分析 - 检测落地时的冲击-缓冲特征")
    print(f"   ✅ 空中技巧动作识别 - 分析旋转、翻转等技巧动作")
    print(f"   ✅ 连续性检查机制 - 确保滞空状态的连续性，减少噪声")
    print(f"   ✅ 自由式专用置信度 - 综合多种自由式特征的置信度算法")
    
    print(f"\n🔬 检测逻辑优化:")
    print(f"   1. 多重判断条件:")
    print(f"      • Z轴重力偏差 > 300mg")
    print(f"      • 失重状态 + 轻微旋转 (>100°/s)")
    print(f"      • Z轴突变 + 中等角速度 (>200°/s)")
    print(f"      • 角速度超过阈值 (>400°/s)")
    print(f"      • 中等重力偏差 + 角速度组合")
    
    print(f"\n   2. 连续性验证:")
    print(f"      • 检查连续2个数据点满足条件")
    print(f"      • 避免单点噪声造成的误判")
    print(f"      • 确保滞空状态的稳定性")
    
    print(f"\n   3. 置信度评估:")
    print(f"      • 起跳模式评分 (20%)")
    print(f"      • 落地模式评分 (20%)")
    print(f"      • 空中技巧评分 (30%)")
    print(f"      • 持续时间适应性 (15%)")
    print(f"      • 高度变化评分 (15%)")


def provide_usage_recommendations():
    """提供使用建议"""
    print(f"\n💡 自由式滑雪检测使用建议")
    print("=" * 80)
    
    print(f"\n🎯 适用场景:")
    print(f"   ✅ 自由式滑雪 (Freestyle Skiing)")
    print(f"   ✅ 单板自由式 (Freestyle Snowboarding)")
    print(f"   ✅ 跳台滑雪 (Ski Jumping)")
    print(f"   ✅ 空中技巧 (Aerial Skiing)")
    print(f"   ✅ U型池滑雪 (Halfpipe)")
    
    print(f"\n⚙️ 参数调优建议:")
    print(f"   🔧 如果检测过于敏感 (误判多):")
    print(f"      • 提高 gravity_threshold (300 → 400mg)")
    print(f"      • 提高 angular_velocity_threshold (400 → 600°/s)")
    print(f"      • 增加 min_continuous_points (2 → 3)")
    
    print(f"\n   🔧 如果检测不够敏感 (漏检多):")
    print(f"      • 降低 gravity_threshold (300 → 200mg)")
    print(f"      • 降低 angular_velocity_threshold (400 → 300°/s)")
    print(f"      • 减少 min_continuous_points (2 → 1)")
    
    print(f"\n📊 结果验证:")
    print(f"   1. 对比视频验证检测准确性")
    print(f"   2. 关注置信度≥0.6的高质量事件")
    print(f"   3. 分析滞空高度的合理性 (0.5-5米)")
    print(f"   4. 检查持续时间的合理性 (0.1-4秒)")
    
    print(f"\n🚀 性能优化:")
    print(f"   • 自由式算法比通用算法更准确")
    print(f"   • 专门针对自由式滑雪的动作特征")
    print(f"   • 更高的置信度和更少的误判")
    print(f"   • 支持复杂的空中技巧检测")


def main():
    """主函数"""
    compare_detection_algorithms()
    analyze_freestyle_optimization_features()
    provide_usage_recommendations()
    
    print(f"\n✅ 自由式滑雪优化分析完成！")
    print(f"\n🎪 推荐使用 FreestyleAirtimeDetector 进行自由式滑雪滞空检测")


if __name__ == "__main__":
    main()
