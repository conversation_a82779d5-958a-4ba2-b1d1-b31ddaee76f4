#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的滞空边界检测器
解决滞空事件段划分偏小的问题
"""

from optimized_airtime_detector import OptimizedAirtimeDetector
from skiing_airtime_calculator import SensorData, AirtimeEvent
from typing import List, Tuple, Dict
import numpy as np


class ImprovedBoundaryDetector(OptimizedAirtimeDetector):
    """改进的边界检测器"""
    
    def __init__(self):
        super().__init__()
        
        # 多级阈值设计
        self.gravity_threshold_strict = 600      # 严格阈值（核心滞空）
        self.gravity_threshold_loose = 300       # 宽松阈值（边界扩展）
        self.angular_threshold_strict = 800      # 严格角速度阈值
        self.angular_threshold_loose = 400       # 宽松角速度阈值
        
        # 边界扩展参数
        self.pre_jump_window = 8                 # 起跳前检测窗口（数据点）
        self.post_landing_window = 8             # 落地后检测窗口（数据点）
        self.boundary_extension_threshold = 0.3  # 边界扩展评分阈值
        
        # 渐进式检测参数
        self.gradient_threshold = 200            # 加速度梯度阈值
        self.momentum_threshold = 0.6            # 动量变化阈值
    
    def detect_takeoff_preparation(self, sensor_data: List[SensorData], 
                                  core_start_idx: int) -> int:
        """检测起跳准备阶段，向前扩展起跳点"""
        
        # 向前搜索窗口
        search_start = max(0, core_start_idx - self.pre_jump_window)
        search_end = core_start_idx
        
        best_takeoff_idx = core_start_idx
        
        for i in range(search_end - 1, search_start - 1, -1):  # 从核心点向前搜索
            data = sensor_data[i]
            
            # 1. 重力变化检测（更宽松的阈值）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            gravity_score = min(1.0, z_gravity_deviation / self.gravity_threshold_loose)
            
            # 2. 角速度预备动作检测
            angular_velocity = data.total_angular_velocity()
            angular_score = min(1.0, angular_velocity / self.angular_threshold_loose)
            
            # 3. 加速度梯度检测（起跳准备的加速度变化）
            gradient_score = 0.0
            if i < len(sensor_data) - 1:
                az_change = abs(sensor_data[i+1].az - data.az)
                gradient_score = min(1.0, az_change / self.gradient_threshold)
            
            # 4. 运动趋势检测（向上加速趋势）
            momentum_score = 0.0
            if i < len(sensor_data) - 2:
                # 检测连续的向上加速度增加
                az_curr = data.az
                az_next1 = sensor_data[i+1].az
                az_next2 = sensor_data[i+2].az
                
                if az_next1 > az_curr and az_next2 > az_next1:
                    momentum_score = 0.8
                elif az_next1 > az_curr:
                    momentum_score = 0.4
            
            # 综合评分
            total_score = (gravity_score * 0.3 + 
                          angular_score * 0.3 + 
                          gradient_score * 0.2 + 
                          momentum_score * 0.2)
            
            # 如果评分超过阈值，更新起跳点
            if total_score >= self.boundary_extension_threshold:
                best_takeoff_idx = i
            else:
                # 如果评分太低，停止向前搜索
                break
        
        return best_takeoff_idx
    
    def detect_landing_completion(self, sensor_data: List[SensorData], 
                                 core_end_idx: int) -> int:
        """检测落地完成阶段，向后扩展落地点"""
        
        # 向后搜索窗口
        search_start = core_end_idx
        search_end = min(len(sensor_data), core_end_idx + self.post_landing_window)
        
        best_landing_idx = core_end_idx
        
        for i in range(search_start + 1, search_end):  # 从核心点向后搜索
            data = sensor_data[i]
            
            # 1. 重力恢复检测（渐进式）
            z_gravity_deviation = abs(abs(data.az) - self.standard_gravity)
            # 重力偏差越小，说明越接近正常状态，但仍可能在落地缓冲中
            gravity_score = max(0.0, 1.0 - z_gravity_deviation / self.gravity_threshold_loose)
            
            # 2. 角速度衰减检测
            angular_velocity = data.total_angular_velocity()
            angular_score = min(1.0, angular_velocity / self.angular_threshold_loose)
            
            # 3. 冲击检测（落地时的加速度突变）
            impact_score = 0.0
            if i > 0:
                az_change = abs(data.az - sensor_data[i-1].az)
                impact_score = min(1.0, az_change / self.gradient_threshold)
            
            # 4. 稳定性检测（落地后的稳定趋势）
            stability_score = 0.0
            if i > 1:
                # 检测加速度是否趋于稳定
                az_prev2 = sensor_data[i-2].az
                az_prev1 = sensor_data[i-1].az
                az_curr = data.az
                
                # 如果加速度变化在减小，说明在趋于稳定
                change1 = abs(az_prev1 - az_prev2)
                change2 = abs(az_curr - az_prev1)
                
                if change2 < change1:
                    stability_score = 0.3
                elif change2 > change1 * 1.5:  # 如果变化增大，可能还在落地过程中
                    stability_score = 0.7
            
            # 综合评分
            total_score = (gravity_score * 0.2 +      # 重力恢复权重较低
                          angular_score * 0.3 +       # 角速度仍然重要
                          impact_score * 0.3 +        # 冲击检测重要
                          stability_score * 0.2)      # 稳定性检测
            
            # 如果评分超过阈值，更新落地点
            if total_score >= self.boundary_extension_threshold:
                best_landing_idx = i
            else:
                # 如果连续几个点评分都很低，可能已经完全落地
                if i > core_end_idx + 3:  # 至少检查3个点后再停止
                    break
        
        return best_landing_idx

    def detect_improved_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """改进的滞空事件检测"""
        if len(sensor_data) < 10:
            return []
        
        print("🚀 使用改进的边界检测算法...")
        
        # 第一步：使用原算法检测核心滞空区间
        core_events = super().detect_airtime_events(sensor_data)
        print(f"   核心滞空区间: {len(core_events)} 个")
        
        if not core_events:
            return []
        
        # 第二步：对每个核心区间进行边界扩展
        improved_events = []
        
        for i, event in enumerate(core_events):
            print(f"\n🔍 优化事件 {i+1}:")
            
            # 获取核心区间的索引
            core_start_idx = getattr(event, 'start_data_index', None)
            core_end_idx = getattr(event, 'end_data_index', None)
            
            if core_start_idx is None or core_end_idx is None:
                # 如果没有索引信息，尝试通过时间戳查找
                core_start_idx, core_end_idx = self._find_event_indices(sensor_data, event)
            
            if core_start_idx is None or core_end_idx is None:
                print(f"   ⚠️ 无法找到事件索引，跳过优化")
                improved_events.append(event)
                continue
            
            print(f"   核心区间: 索引 {core_start_idx} ~ {core_end_idx}")
            
            # 检测改进的起跳点
            improved_start_idx = self.detect_takeoff_preparation(sensor_data, core_start_idx)
            print(f"   起跳点扩展: {core_start_idx} → {improved_start_idx} (扩展 {core_start_idx - improved_start_idx} 点)")
            
            # 检测改进的落地点
            improved_end_idx = self.detect_landing_completion(sensor_data, core_end_idx)
            print(f"   落地点扩展: {core_end_idx} → {improved_end_idx} (扩展 {improved_end_idx - core_end_idx} 点)")
            
            # 创建改进的事件
            improved_event = self._create_improved_event(
                sensor_data, improved_start_idx, improved_end_idx, event)
            
            improved_events.append(improved_event)
            
            print(f"   改进后持续时间: {improved_event.duration_seconds:.3f}秒 "
                  f"(原: {event.duration_seconds:.3f}秒)")
        
        print(f"\n✅ 边界优化完成，共 {len(improved_events)} 个改进事件")

        # 第三步：合并重叠事件
        merged_events = self.merge_overlapping_events(improved_events, sensor_data)
        print(f"🔗 合并重叠事件后，剩余 {len(merged_events)} 个事件")

        return merged_events
    
    def _find_event_indices(self, sensor_data: List[SensorData], 
                           event: AirtimeEvent) -> Tuple[int, int]:
        """通过时间戳查找事件在数据中的索引"""
        start_idx = None
        end_idx = None
        
        for i, data in enumerate(sensor_data):
            if data.timestamp == event.start_time:
                start_idx = i
            if data.timestamp == event.end_time:
                end_idx = i
        
        return start_idx, end_idx
    
    def _create_improved_event(self, sensor_data: List[SensorData], 
                              start_idx: int, end_idx: int, 
                              original_event: AirtimeEvent) -> AirtimeEvent:
        """创建改进的滞空事件"""
        
        start_data = sensor_data[start_idx]
        end_data = sensor_data[end_idx]
        
        # 计算新的持续时间
        duration_ms = self._calculate_duration_ms(start_data, end_data)
        
        # 重新计算滞空特征
        airtime_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(airtime_segment)
        max_acceleration = max(d.total_acceleration() for d in airtime_segment)
        
        # 重新计算旋转指标
        rotation_metrics = self._calculate_rotation_metrics(airtime_segment)
        
        # 创建新事件
        improved_event = AirtimeEvent(
            start_time=start_data.timestamp,
            end_time=end_data.timestamp,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=original_event.confidence,  # 保持原置信度
            max_rotation_speed=rotation_metrics['max_rotation_speed'],
            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
            rotation_detected=rotation_metrics['rotation_detected']
        )
        
        # 保存索引信息
        improved_event.start_data_index = start_idx
        improved_event.end_data_index = end_idx
        
        return improved_event

    def merge_overlapping_events(self, events: List[AirtimeEvent],
                                sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """合并重叠的滞空事件"""
        if len(events) <= 1:
            return events

        # 按开始时间排序
        sorted_events = sorted(events, key=lambda e: e.start_time)
        merged_events = []

        i = 0
        while i < len(sorted_events):
            current_event = sorted_events[i]

            # 查找与当前事件重叠的所有事件
            overlapping_events = [current_event]
            j = i + 1

            while j < len(sorted_events):
                next_event = sorted_events[j]

                # 检查时间重叠
                if self._events_overlap(current_event, next_event):
                    overlapping_events.append(next_event)
                    j += 1
                else:
                    break

            # 如果有重叠事件，进行合并
            if len(overlapping_events) > 1:
                merged_event = self._merge_events(overlapping_events, sensor_data)
                merged_events.append(merged_event)
            else:
                merged_events.append(current_event)

            # 跳过已处理的重叠事件
            i = j if len(overlapping_events) > 1 else i + 1

        return merged_events
        return merged_even
    def _events_overlap(self, event1: AirtimeEvent, event2: AirtimeEvent) -> bool:
        """检查两个事件是否重叠"""
        # 事件1的结束时间是否晚于事件2的开始时间
        return event1.end_time >= event2.start_time

    def _merge_events(self, events: List[AirtimeEvent],
                     sensor_data: List[SensorData]) -> AirtimeEvent:
        """合并多个重叠事件为一个事件"""

        # 找到最早的开始时间和最晚的结束时间
        earliest_start = min(event.start_time for event in events)
        latest_end = max(event.end_time for event in events)

        # 找到对应的数据索引
        start_idx = None
        end_idx = None

        for i, data in enumerate(sensor_data):
            if data.timestamp == earliest_start:
                start_idx = i
            if data.timestamp == latest_end:
                end_idx = i

        # 如果找不到精确匹配，使用最接近的索引
        if start_idx is None or end_idx is None:
            for event in events:
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    if start_idx is None:
                        start_idx = event.start_data_index
                    else:
                        start_idx = min(start_idx, event.start_data_index)

                    if end_idx is None:
                        end_idx = event.end_data_index
                    else:
                        end_idx = max(end_idx, event.end_data_index)

        # 计算合并后的特征
        duration_ms = self._calculate_duration_ms(sensor_data[start_idx], sensor_data[end_idx])

        # 重新计算合并区间的特征
        merged_segment = sensor_data[start_idx:end_idx+1]
        max_height_change = self._calculate_max_height_change(merged_segment)
        max_acceleration = max(d.total_acceleration() for d in merged_segment)

        # 使用最高的置信度
        max_confidence = max(event.confidence for event in events)

        # 重新计算旋转指标
        rotation_metrics = self._calculate_rotation_metrics(merged_segment)

        # 创建合并后的事件
        merged_event = AirtimeEvent(
            start_time=earliest_start,
            end_time=latest_end,
            duration_ms=duration_ms,
            max_height_change=max_height_change,
            max_acceleration=max_acceleration,
            confidence=max_confidence,
            max_rotation_speed=rotation_metrics['max_rotation_speed'],
            avg_rotation_speed=rotation_metrics['avg_rotation_speed'],
            rotation_detected=rotation_metrics['rotation_detected']
        )

        # 保存索引信息
        merged_event.start_data_index = start_idx
        merged_event.end_data_index = end_idx

        return merged_event

    def _calculate_detailed_rotation(self, airtime_segment: List[SensorData],
                                   duration_seconds: float) -> Dict:
        """计算详细的转体信息，包括分段转速"""
        yaw_angles = [data.z for data in airtime_segment]

        # 计算累积旋转角度
        cumulative_rotation = 0.0
        angle_changes = []

        for j in range(1, len(yaw_angles)):
            angle_change = yaw_angles[j] - yaw_angles[j-1]

            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360

            cumulative_rotation += angle_change
            angle_changes.append(angle_change)

        # 转体角度和基本信息
        total_rotation_angle = abs(cumulative_rotation)
        avg_rotation_speed = total_rotation_angle / 360.0 / duration_seconds if duration_seconds > 0 else 0

        # 判断转动方向
        if cumulative_rotation > 50:
            rotation_direction = 'clockwise'
        elif cumulative_rotation < -50:
            rotation_direction = 'counterclockwise'
        else:
            rotation_direction = 'none'
            total_rotation_angle = 0.0
            avg_rotation_speed = 0.0

        # 计算分段转速（当转体角度大于360度时）
        first_half_speed = 0.0
        second_half_speed = 0.0

        if total_rotation_angle > 360 and len(angle_changes) > 2:
            # 目标角度：总角度的一半
            target_half_angle = total_rotation_angle / 2.0

            # 寻找前半段结束点（累积角度达到一半时）
            cumulative_angle = 0.0
            split_point = 0

            for i, angle_change in enumerate(angle_changes):
                cumulative_angle += abs(angle_change)
                if cumulative_angle >= target_half_angle:
                    split_point = i + 1
                    break

            # 确保分割点有效
            if split_point == 0:
                split_point = len(angle_changes) // 2
            elif split_point >= len(angle_changes):
                split_point = len(angle_changes) - 1

            # 分割角度变化数组
            first_half_changes = angle_changes[:split_point]
            second_half_changes = angle_changes[split_point:]

            # 计算前半段和后半段的实际角度
            first_half_angle = sum(abs(change) for change in first_half_changes)
            second_half_angle = sum(abs(change) for change in second_half_changes)

            # 计算时间分配（按分割点比例分配）
            first_half_duration = (split_point / len(angle_changes)) * duration_seconds
            second_half_duration = duration_seconds - first_half_duration

            # 计算分段转速
            if first_half_duration > 0:
                first_half_speed = first_half_angle / 360.0 / first_half_duration
            if second_half_duration > 0:
                second_half_speed = second_half_angle / 360.0 / second_half_duration

        return {
            'total_angle': total_rotation_angle,
            'avg_speed': avg_rotation_speed,
            'direction': rotation_direction,
            'first_half_speed': first_half_speed,
            'second_half_speed': second_half_speed,
            'has_split_analysis': total_rotation_angle > 360
        }

    def _calculate_horizontal_distance(self, airtime_segment: List[SensorData],
                                     duration_seconds: float) -> str:
        """计算水平距离，使用速度speed与时间段相乘"""

        # 获取所有速度值
        speeds = [data.speed for data in airtime_segment]

        # 检查是否有非零速度
        non_zero_speeds = [s for s in speeds if s != 0]

        if not non_zero_speeds:
            return "null"

        # 计算平均速度（排除零值）
        avg_speed = sum(non_zero_speeds) / len(non_zero_speeds)/3.6/3.6

        # 计算水平距离 = 平均速度 × 时间
        horizontal_distance = avg_speed * duration_seconds

        return f"{horizontal_distance:.2f}m"


def main():
    """改进的滑雪滞空检测器"""
    # 创建检测器
    detector = ImprovedBoundaryDetector()

    # 加载数据
    sensor_data = detector.load_sensor_data(r'E:\cyl\成都跳台\data_02.txt')

    if sensor_data:
        # 检测滞空事件
        improved_events = detector.detect_improved_airtime_events(sensor_data)

        # 过滤高置信度事件
        high_confidence_events = [event for event in improved_events if event.confidence >= 0.5]

        if high_confidence_events:
            print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'水平距离':<10} {'置信度':<6} {'转体角度(°)':<10} {'转体速度(圈/s)':<12} {'转动方向':<10}")
            print("-" * 120)

            for i, event in enumerate(high_confidence_events, 1):
                # 获取精确时间戳
                if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                    start_data = sensor_data[event.start_data_index]
                    end_data = sensor_data[event.end_data_index]

                    start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
                    end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"

                    # 计算转体信息
                    airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
                    rotation_info = detector._calculate_detailed_rotation(airtime_segment, event.duration_seconds)

                    rotation_angle = rotation_info['total_angle']
                    rotation_speed = rotation_info['avg_speed']
                    rotation_direction = rotation_info['direction']
                    first_half_speed = rotation_info['first_half_speed']
                    second_half_speed = rotation_info['second_half_speed']
                    has_split_analysis = rotation_info['has_split_analysis']

                    # 计算水平距离
                    horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
                else:
                    start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
                    end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
                    rotation_angle = 0.0
                    rotation_speed = 0.0
                    rotation_direction = 'none'
                    horizontal_distance = "null"
                    has_split_analysis = False

                # 构建转体速度显示字符串
                if has_split_analysis:
                    speed_display = f"{rotation_speed:.2f}(前:{first_half_speed:.2f}|后:{second_half_speed:.2f})"
                else:
                    speed_display = f"{rotation_speed:.2f}"

                # 缩短时间戳显示（只显示时分秒.毫秒）
                start_time_short = start_timestamp.split(' ')[1]  # 只取时间部分
                end_time_short = end_timestamp.split(' ')[1]      # 只取时间部分

                print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
                      f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
                      f"{horizontal_distance:<10} {event.confidence:<6.3f} {rotation_angle:<10.1f} "
                      f"{speed_display:<12} {rotation_direction:<10}")

                # 如果有分段分析，添加详细信息
                if has_split_analysis:
                    print(f"     └─ 分段转速详情: 前半段 {first_half_speed:.2f}圈/s, 后半段 {second_half_speed:.2f}圈/s")
        else:
            print("未检测到高置信度的滞空事件")
    else:
        print("数据加载失败，请检查文件路径")


if __name__ == "__main__":
    main()
