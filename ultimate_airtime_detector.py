#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极滞空检测器
集成所有优化技术的最终版本
"""

from improved_airtime_detector_v2 import ImprovedAirtimeDetectorV2
from skiing_airtime_calculator import SensorData, AirtimeEvent
from typing import List
import numpy as np


class UltimateAirtimeDetector(ImprovedAirtimeDetectorV2):
    """终极滞空检测器"""
    
    def __init__(self):
        super().__init__()
        
        # 进一步优化的参数
        self.gradient_threshold = 200        # 梯度变化阈值
        self.min_peak_height = 0.1          # 最小峰值高度
        self.stability_window = 3           # 稳定性检查窗口
        
    def detect_gradient_changes(self, sensor_data: List[SensorData]) -> List[int]:
        """检测加速度梯度变化点（起跳和落地）"""
        gradient_points = []
        
        for i in range(2, len(sensor_data) - 2):
            # 计算Z轴加速度的二阶导数（梯度变化）
            az_prev2 = abs(sensor_data[i-2].az)
            az_prev1 = abs(sensor_data[i-1].az)
            az_curr = abs(sensor_data[i].az)
            az_next1 = abs(sensor_data[i+1].az)
            az_next2 = abs(sensor_data[i+2].az)
            
            # 计算梯度
            grad1 = az_curr - az_prev1
            grad2 = az_next1 - az_curr
            
            # 检测梯度突变
            gradient_change = abs(grad2 - grad1)
            
            if gradient_change > self.gradient_threshold:
                gradient_points.append(i)
        
        return gradient_points
    
    def validate_event_physics(self, event: AirtimeEvent, sensor_data: List[SensorData]) -> bool:
        """基于物理规律验证事件的合理性"""
        
        # 1. 持续时间合理性（0.1-3.0秒）
        if not (0.1 <= event.duration_seconds <= 3.0):
            return False
        
        # 2. 高度变化合理性（0.05-5.0米）
        if not (0.05 <= event.max_height_change <= 5.0):
            return False
        
        # 3. 转体速度合理性（<200圈/秒）
        if event.avg_rotation_speed > 200:
            return False
        
        # 4. 加速度变化合理性
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            segment = sensor_data[event.start_data_index:event.end_data_index+1]
            accelerations = [data.total_acceleration() for data in segment]
            
            # 检查是否有明显的加速度变化
            max_acc = max(accelerations)
            min_acc = min(accelerations)
            acc_range = max_acc - min_acc
            
            if acc_range < 200:  # 加速度变化太小，可能不是真正的滞空
                return False
        
        return True
    
    def post_process_events(self, events: List[AirtimeEvent], sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """后处理优化事件"""
        processed_events = []
        
        for event in events:
            # 1. 物理验证
            if not self.validate_event_physics(event, sensor_data):
                continue
            
            # 2. 重新计算更精确的置信度
            if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
                segment = sensor_data[event.start_data_index:event.end_data_index+1]
                
                # 基于多个因素重新计算置信度
                duration_factor = min(1.0, event.duration_seconds / 0.5)  # 0.5秒为理想持续时间
                height_factor = min(1.0, event.max_height_change / 1.0)   # 1米为理想高度
                
                # 加速度变化因素
                accelerations = [data.total_acceleration() for data in segment]
                acc_std = np.std(accelerations)
                acc_factor = min(1.0, acc_std / 1000)  # 标准差越大，置信度越高
                
                # 综合置信度
                new_confidence = (duration_factor * 0.3 + height_factor * 0.3 + 
                                acc_factor * 0.2 + event.confidence * 0.2)
                event.confidence = min(1.0, new_confidence)
            
            processed_events.append(event)
        
        # 3. 按时间排序
        processed_events.sort(key=lambda e: e.start_time)
        
        return processed_events
    
    def detect_ultimate_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """终极滞空检测"""
        if len(sensor_data) < 10:
            return []
        
        print("🌟 使用终极滞空检测算法...")
        
        # 1. 使用改进的多特征检测
        events = self.detect_improved_airtime_events(sensor_data)
        print(f"   多特征检测: {len(events)} 个事件")
        
        # 2. 梯度变化检测辅助验证
        gradient_points = self.detect_gradient_changes(sensor_data)
        print(f"   梯度变化点: {len(gradient_points)} 个")
        
        # 3. 后处理优化
        final_events = self.post_process_events(events, sensor_data)
        print(f"   后处理优化: {len(final_events)} 个事件")
        
        return final_events
    
    def get_precise_timestamps_and_rotation(self, sensor_data: List[SensorData], event: AirtimeEvent) -> dict:
        """获取精确的时间戳和转动信息"""
        if not (hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index')):
            return {
                'start_timestamp': event.start_time.strftime('%Y-%m-%d %H:%M:%S.000'),
                'end_timestamp': event.end_time.strftime('%Y-%m-%d %H:%M:%S.000'),
                'rotation_angle': 0.0,
                'rotation_speed_deg_per_sec': 0.0,
                'rotation_speed_rev_per_sec': 0.0,
                'rotation_direction': 'none'
            }

        # 获取精确时间戳
        start_data = sensor_data[event.start_data_index]
        end_data = sensor_data[event.end_data_index]

        start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
        end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"

        # 计算精确的转动角度和转速（基于偏航角z）
        airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
        yaw_angles = [data.z for data in airtime_segment]

        # 计算累积旋转角度（处理跨越360°的情况）
        cumulative_rotation = 0.0
        for i in range(1, len(yaw_angles)):
            angle_change = yaw_angles[i] - yaw_angles[i-1]

            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360

            cumulative_rotation += angle_change

        # 转动角度（绝对值）
        rotation_angle = abs(cumulative_rotation)

        # 计算转动速度
        total_time = event.duration_seconds
        rotation_speed_deg_per_sec = rotation_angle / total_time if total_time > 0 else 0
        rotation_speed_rev_per_sec = rotation_speed_deg_per_sec / 360.0

        # 判断转动方向
        if cumulative_rotation > 50:
            direction = 'clockwise'
        elif cumulative_rotation < -50:
            direction = 'counterclockwise'
        else:
            direction = 'none'

        return {
            'start_timestamp': start_timestamp,
            'end_timestamp': end_timestamp,
            'rotation_angle': rotation_angle,
            'rotation_speed_deg_per_sec': rotation_speed_deg_per_sec,
            'rotation_speed_rev_per_sec': rotation_speed_rev_per_sec,
            'rotation_direction': direction
        }

    def generate_detailed_report(self, events: List[AirtimeEvent]) -> dict:
        """生成详细的检测报告"""
        if not events:
            return {"error": "没有检测到滞空事件"}
        
        # 基础统计
        total_events = len(events)
        total_airtime = sum(e.duration_seconds for e in events)
        avg_duration = total_airtime / total_events
        
        # 高度统计
        heights = [e.max_height_change for e in events]
        avg_height = sum(heights) / len(heights)
        max_height = max(heights)
        
        # 转体统计
        rotation_events = [e for e in events if e.rotation_detected]
        rotation_speeds = [e.avg_rotation_speed for e in rotation_events]
        
        # 置信度统计
        confidences = [e.confidence for e in events]
        avg_confidence = sum(confidences) / len(confidences)
        high_confidence_events = [e for e in events if e.confidence >= 0.8]
        
        report = {
            "总体统计": {
                "总事件数": total_events,
                "总滞空时间": f"{total_airtime:.2f}秒",
                "平均持续时间": f"{avg_duration:.3f}秒",
                "平均置信度": f"{avg_confidence:.3f}"
            },
            "高度统计": {
                "平均滞空高度": f"{avg_height:.2f}米",
                "最大滞空高度": f"{max_height:.2f}米",
                "高度分布": {
                    "0-0.5米": len([h for h in heights if h <= 0.5]),
                    "0.5-1.0米": len([h for h in heights if 0.5 < h <= 1.0]),
                    "1.0-2.0米": len([h for h in heights if 1.0 < h <= 2.0]),
                    ">2.0米": len([h for h in heights if h > 2.0])
                }
            },
            "转体统计": {
                "转体事件数": len(rotation_events),
                "转体比例": f"{len(rotation_events)/total_events*100:.1f}%",
                "平均转体速度": f"{sum(rotation_speeds)/len(rotation_speeds):.2f}圈/s" if rotation_speeds else "0圈/s",
                "最大转体速度": f"{max(rotation_speeds):.2f}圈/s" if rotation_speeds else "0圈/s"
            },
            "质量统计": {
                "高质量事件数": len(high_confidence_events),
                "高质量比例": f"{len(high_confidence_events)/total_events*100:.1f}%",
                "置信度分布": {
                    "0.5-0.6": len([c for c in confidences if 0.5 <= c < 0.6]),
                    "0.6-0.7": len([c for c in confidences if 0.6 <= c < 0.7]),
                    "0.7-0.8": len([c for c in confidences if 0.7 <= c < 0.8]),
                    "0.8-0.9": len([c for c in confidences if 0.8 <= c < 0.9]),
                    "0.9-1.0": len([c for c in confidences if 0.9 <= c <= 1.0])
                }
            }
        }
        
        return report


def main():
    """主函数"""
    print("🌟 终极滞空检测器测试")
    print("=" * 80)
    
    detector = UltimateAirtimeDetector()
    sensor_data = detector.load_sensor_data('20250330153942712068-济洲平花.txt')
    
    if not sensor_data:
        print("❌ 无法加载数据")
        return
    
    # 使用终极检测方法
    ultimate_events = detector.detect_ultimate_airtime_events(sensor_data)
    
    # 生成详细报告
    report = detector.generate_detailed_report(ultimate_events)
    
    print(f"\n📊 终极检测报告:")
    print("=" * 80)
    
    for category, stats in report.items():
        print(f"\n{category}:")
        if isinstance(stats, dict):
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")
        else:
            print(f"  {stats}")
    
    # 显示中等质量以上的滞空事件（置信度≥0.6）
    if ultimate_events:
        medium_quality_events = [e for e in ultimate_events if e.confidence >= 0.6]

        print(f"\n🎿 中等质量以上滞空事件列表 (置信度≥0.6): {len(medium_quality_events)} 次")
        print(f"{'序号':<4} {'开始时间戳':<23} {'结束时间戳':<23} {'持续时间(s)':<12} {'滞空高度(m)':<12} {'置信度':<8} {'转体角度(°)':<12} {'转体速度(圈/s)':<14} {'转动方向':<10}")
        print("-" * 140)

        for i, event in enumerate(medium_quality_events, 1):
            # 获取精确的时间戳和转动信息
            precise_info = detector.get_precise_timestamps_and_rotation(sensor_data, event)

            print(f"{i:<4} {precise_info['start_timestamp']:<23} {precise_info['end_timestamp']:<23} "
                  f"{event.duration_seconds:<12.3f} {event.max_height_change:<12.3f} "
                  f"{event.confidence:<8.3f} {precise_info['rotation_angle']:<12.1f} "
                  f"{precise_info['rotation_speed_rev_per_sec']:<14.2f} {precise_info['rotation_direction']:<10}")

        # 如果还有其他质量的事件，显示统计
        if len(medium_quality_events) < len(ultimate_events):
            low_quality_events = [e for e in ultimate_events if e.confidence < 0.6]
            print(f"\n📊 其他质量事件统计:")
            print(f"   低质量事件 (置信度<0.6): {len(low_quality_events)} 次")
            print(f"   总事件数: {len(ultimate_events)} 次")

        # 显示最佳事件摘要
        best_events = sorted(ultimate_events, key=lambda e: e.confidence, reverse=True)[:5]
        print(f"\n🏆 最佳滞空事件摘要 (Top 5):")
        print(f"{'序号':<4} {'时间':<12} {'持续时间':<10} {'高度':<8} {'置信度':<8} {'转体角度':<10} {'转体速度':<12}")
        print("-" * 75)

        for i, event in enumerate(best_events, 1):
            precise_info = detector.get_precise_timestamps_and_rotation(sensor_data, event)
            time_str = event.start_time.strftime('%H:%M:%S')
            angle_str = f"{precise_info['rotation_angle']:.1f}°"
            speed_str = f"{precise_info['rotation_speed_rev_per_sec']:.2f}圈/s"


            print(f"{i:<4} {time_str:<12} {event.duration_seconds:<10.3f} "
                  f"{event.max_height_change:<8.2f} {event.confidence:<8.3f} {angle_str:<10} {speed_str:<12}")


if __name__ == "__main__":
    main()
