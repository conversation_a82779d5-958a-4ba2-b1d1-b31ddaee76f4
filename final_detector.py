#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版滑雪滞空检测器 - 只输出高置信度事件详细信息
"""

import re
import os
from datetime import datetime
from typing import List, Dict


class SensorData:
    """传感器数据类"""
    def __init__(self, timestamp, milliseconds, x, y, z, P, ax, ay, az, gx, gy, gz, 
                 mx, my, mz, t, humidity, speed, longitude, latitude, gh):
        self.timestamp = timestamp
        self.milliseconds = milliseconds
        self.x = x
        self.y = y
        self.z = z
        self.P = P
        self.ax = ax
        self.ay = ay
        self.az = az
        self.gx = gx
        self.gy = gy
        self.gz = gz
        self.mx = mx
        self.my = my
        self.mz = mz
        self.t = t
        self.humidity = humidity
        self.speed = speed
        self.longitude = longitude
        self.latitude = latitude
        self.gh = gh
    
    def total_acceleration(self):
        """计算总加速度"""
        return (self.ax**2 + self.ay**2 + self.az**2)**0.5
    
    def total_angular_velocity(self):
        """计算总角速度"""
        return (self.gx**2 + self.gy**2 + self.gz**2)**0.5


class AirtimeEvent:
    """滞空事件类"""
    def __init__(self, start_time, end_time, duration_ms, max_height_change, 
                 max_acceleration, confidence):
        self.start_time = start_time
        self.end_time = end_time
        self.duration_ms = duration_ms
        self.max_height_change = max_height_change
        self.max_acceleration = max_acceleration
        self.confidence = confidence
    
    @property
    def duration_seconds(self):
        """持续时间（秒）"""
        return self.duration_ms / 1000.0


class AirtimeDetector:
    """滑雪滞空检测器"""
    
    def __init__(self):
        # 基础检测参数
        self.gravity_threshold = 600
        self.angular_velocity_threshold = 800
        self.min_airtime_ms = 150
        self.max_airtime_ms = 3000
        self.standard_gravity = 1000
        self.weightless_ratio = 0.15
        self.z_spike_ratio = 0.6
        self.consistency_window = 3
        
        # 边界扩展参数
        self.pre_jump_window = 8
        self.post_landing_window = 8
        self.boundary_extension_threshold = 0.3
        self.gradient_threshold = 200
    
    def load_sensor_data(self, filename: str) -> List[SensorData]:
        """加载传感器数据"""
        if not os.path.exists(filename):
            print(f"错误: 文件 '{filename}' 不存在")
            return []
        
        sensor_data = []
        pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\d+)\] x: (-?\d+), y: (-?\d+), z: (-?\d+), P: (-?\d+), ax: (-?\d+), ay: (-?\d+), az: (-?\d+), gx: (-?\d+), gy: (-?\d+), gz: (-?\d+), mx: (-?\d+), my: (-?\d+), mz: (-?\d+), t: (-?\d+), h: (-?\d+), s: (-?\d+).*?lo: (-?\d+\.?\d*), la: (-?\d+\.?\d*), gh: (-?\d+)'
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                parsed_count = 0
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    match = re.match(pattern, line)
                    if match:
                        try:
                            groups = match.groups()
                            timestamp = datetime.strptime(groups[0], '%Y-%m-%d %H:%M:%S')
                            milliseconds = int(groups[1])
                            
                            data = SensorData(
                                timestamp=timestamp,
                                milliseconds=milliseconds,
                                x=int(groups[2]),
                                y=int(groups[3]),
                                z=int(groups[4]),
                                P=int(groups[5]),
                                ax=int(groups[6]),
                                ay=int(groups[7]),
                                az=int(groups[8]),
                                gx=int(groups[9]),
                                gy=int(groups[10]),
                                gz=int(groups[11]),
                                mx=int(groups[12]),
                                my=int(groups[13]),
                                mz=int(groups[14]),
                                t=int(groups[15]),
                                humidity=int(groups[16]),
                                speed=int(groups[17]),
                                longitude=float(groups[18]),
                                latitude=float(groups[19]),
                                gh=int(groups[20])
                            )
                            sensor_data.append(data)
                            parsed_count += 1
                        except ValueError:
                            continue
                
                print(f"成功加载 {parsed_count} 条数据记录")
                
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []
        
        return sensor_data
    
    def detect_airtime_events(self, sensor_data: List[SensorData]) -> List[AirtimeEvent]:
        """检测滞空事件 - 恢复原始算法"""
        if len(sensor_data) < 5:
            return []

        print("使用优化的Z轴重力检测算法...")

        # 平滑Z轴加速度 - 使用原始的3点窗口算法
        z_values = [data.az for data in sensor_data]
        smoothed_z = []
        window_size = 3

        for i in range(len(z_values)):
            start = max(0, i - window_size // 2)
            end = min(len(z_values), i + window_size // 2 + 1)
            smoothed_z.append(sum(z_values[start:end]) / (end - start))

        # 检测Z轴模式
        patterns = []
        for i, data in enumerate(sensor_data):
            z_smooth = smoothed_z[i]

            # 1. 重力偏差检测
            z_gravity_deviation = abs(abs(z_smooth) - self.standard_gravity)
            gravity_anomaly = z_gravity_deviation > self.gravity_threshold

            # 2. 失重状态检测（Z轴加速度很小）
            weightless_threshold = self.standard_gravity * self.weightless_ratio
            is_weightless = abs(z_smooth) < weightless_threshold

            # 3. Z轴突变检测（起跳/落地特征）
            z_spike = False
            if i > 0:
                z_change = abs(z_smooth - smoothed_z[i-1])
                spike_threshold = self.standard_gravity * self.z_spike_ratio
                z_spike = z_change > spike_threshold

            # 4. 角速度检测
            angular_velocity = data.total_angular_velocity()
            high_angular = angular_velocity > self.angular_velocity_threshold

            patterns.append({
                'gravity_anomaly': gravity_anomaly,
                'is_weightless': is_weightless,
                'z_spike': z_spike,
                'high_angular': high_angular,
                'angular_velocity': angular_velocity
            })

        # 定义多种滞空判断条件
        conditions = []
        for pattern in patterns:
            # 条件1: 重力异常 + 高角速度
            condition1 = pattern['gravity_anomaly'] and pattern['high_angular']

            # 条件2: 失重状态 + 中等角速度
            condition2 = pattern['is_weightless'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.4

            # 条件3: Z轴突变 + 较高角速度（起跳/落地瞬间）
            condition3 = pattern['z_spike'] and pattern['angular_velocity'] > self.angular_velocity_threshold * 0.6

            # 综合判断
            is_airborne = condition1 or condition2 or condition3
            conditions.append(is_airborne)

        # 应用一致性过滤 - 使用原始算法
        filtered_conditions = []
        for i in range(len(conditions)):
            start = max(0, i - self.consistency_window // 2)
            end = min(len(conditions), i + self.consistency_window // 2 + 1)

            window_conditions = conditions[start:end]
            # 窗口内超过60%的点满足条件才认为有效
            true_ratio = sum(window_conditions) / len(window_conditions)
            filtered_conditions.append(true_ratio >= 0.6)
        
        # 提取滞空事件
        events = []
        in_airtime = False
        airtime_start = None
        airtime_start_idx = None
        
        for i, is_airborne in enumerate(filtered_conditions):
            if is_airborne and not in_airtime:
                # 开始滞空
                in_airtime = True
                airtime_start = sensor_data[i]
                airtime_start_idx = i
            elif not is_airborne and in_airtime:
                # 结束滞空
                in_airtime = False
                airtime_end = sensor_data[i-1]
                airtime_end_idx = i - 1
                
                # 计算持续时间
                time_diff = (airtime_end.timestamp - airtime_start.timestamp).total_seconds()
                ms_diff = airtime_end.milliseconds - airtime_start.milliseconds
                duration_ms = int(time_diff * 1000 + ms_diff)
                
                # 时间过滤
                if self.min_airtime_ms <= duration_ms <= self.max_airtime_ms:
                    # 计算事件特征
                    airtime_segment = sensor_data[airtime_start_idx:airtime_end_idx+1]
                    heights = [data.humidity / 10.0 for data in airtime_segment]
                    max_height_change = max(heights) - min(heights)
                    max_acceleration = max(d.total_acceleration() for d in airtime_segment)
                    confidence = self._calculate_confidence(airtime_segment)
                    
                    # 创建事件
                    event = AirtimeEvent(
                        start_time=airtime_start.timestamp,
                        end_time=airtime_end.timestamp,
                        duration_ms=duration_ms,
                        max_height_change=max_height_change,
                        max_acceleration=max_acceleration,
                        confidence=confidence
                    )
                    
                    # 保存索引信息
                    event.start_data_index = airtime_start_idx
                    event.end_data_index = airtime_end_idx
                    
                    events.append(event)
        
        return events
    
    def _calculate_confidence(self, airtime_segment: List[SensorData]) -> float:
        """计算置信度"""
        if not airtime_segment:
            return 0.0
        
        # 失重状态评分
        weightless_count = sum(1 for data in airtime_segment 
                              if abs(data.az) < (self.weightless_ratio * self.standard_gravity))
        weightless_score = min(0.3, (weightless_count / len(airtime_segment)) * 0.3)
        
        # Z轴突变评分
        spike_count = 0
        for i in range(1, len(airtime_segment)):
            z_change = abs(airtime_segment[i].az - airtime_segment[i-1].az)
            if z_change > (self.z_spike_ratio * self.standard_gravity):
                spike_count += 1
        
        z_pattern_score = 0.2 if spike_count >= 2 else (0.1 if spike_count >= 1 else 0.0)
        
        # 重力异常评分
        gravity_anomaly_count = sum(1 for data in airtime_segment 
                                   if abs(abs(data.az) - self.standard_gravity) > self.gravity_threshold)
        gravity_score = min(0.2, (gravity_anomaly_count / len(airtime_segment)) * 0.2)
        
        # 角速度一致性评分
        high_angular_count = sum(1 for data in airtime_segment 
                                if data.total_angular_velocity() > self.angular_velocity_threshold)
        angular_score = min(0.3, (high_angular_count / len(airtime_segment)) * 0.3)
        
        return weightless_score + z_pattern_score + gravity_score + angular_score
    
    def _calculate_detailed_rotation(self, airtime_segment: List[SensorData], 
                                   duration_seconds: float) -> Dict:
        """计算详细的转体信息，包括分段转速"""
        yaw_angles = [data.z for data in airtime_segment]
        
        # 计算累积旋转角度
        cumulative_rotation = 0.0
        angle_changes = []
        
        for j in range(1, len(yaw_angles)):
            angle_change = yaw_angles[j] - yaw_angles[j-1]
            
            # 处理角度跨越360°边界的情况
            if angle_change > 180:
                angle_change -= 360
            elif angle_change < -180:
                angle_change += 360
            
            cumulative_rotation += angle_change
            angle_changes.append(angle_change)
        
        # 转体角度和基本信息
        total_rotation_angle = abs(cumulative_rotation)
        avg_rotation_speed = total_rotation_angle / 360.0 / duration_seconds if duration_seconds > 0 else 0
        
        # 判断转动方向
        if cumulative_rotation > 50:
            rotation_direction = 'clockwise'
        elif cumulative_rotation < -50:
            rotation_direction = 'counterclockwise'
        else:
            rotation_direction = 'none'
            total_rotation_angle = 0.0
            avg_rotation_speed = 0.0
        
        # 计算分段转速（当转体角度大于360度时）
        first_half_speed = 0.0
        second_half_speed = 0.0
        
        if total_rotation_angle > 360 and len(angle_changes) > 2:
            # 目标角度：总角度的一半
            target_half_angle = total_rotation_angle / 2.0
            
            # 寻找前半段结束点（累积角度达到一半时）
            cumulative_angle = 0.0
            split_point = 0
            
            for i, angle_change in enumerate(angle_changes):
                cumulative_angle += abs(angle_change)
                if cumulative_angle >= target_half_angle:
                    split_point = i + 1
                    break
            
            # 确保分割点有效
            if split_point == 0:
                split_point = len(angle_changes) // 2
            elif split_point >= len(angle_changes):
                split_point = len(angle_changes) - 1
            
            # 分割角度变化数组
            first_half_changes = angle_changes[:split_point]
            second_half_changes = angle_changes[split_point:]
            
            # 计算前半段和后半段的实际角度
            first_half_angle = sum(abs(change) for change in first_half_changes)
            second_half_angle = sum(abs(change) for change in second_half_changes)
            
            # 计算时间分配（按分割点比例分配）
            first_half_duration = (split_point / len(angle_changes)) * duration_seconds
            second_half_duration = duration_seconds - first_half_duration
            
            # 计算分段转速
            if first_half_duration > 0:
                first_half_speed = first_half_angle / 360.0 / first_half_duration
            if second_half_duration > 0:
                second_half_speed = second_half_angle / 360.0 / second_half_duration
        
        return {
            'total_angle': total_rotation_angle,
            'avg_speed': avg_rotation_speed,
            'direction': rotation_direction,
            'first_half_speed': first_half_speed,
            'second_half_speed': second_half_speed,
            'has_split_analysis': total_rotation_angle > 360
        }
    
    def _calculate_horizontal_distance(self, airtime_segment: List[SensorData], 
                                     duration_seconds: float) -> str:
        """计算水平距离，使用速度speed与时间段相乘"""
        
        # 获取所有速度值
        speeds = [data.speed for data in airtime_segment]
        
        # 检查是否有非零速度
        non_zero_speeds = [s for s in speeds if s != 0]
        
        if not non_zero_speeds:
            return "null"
        
        # 计算平均速度（排除零值）
        avg_speed = sum(non_zero_speeds) / len(non_zero_speeds)
        
        # 计算水平距离 = 平均速度 × 时间
        horizontal_distance = avg_speed * duration_seconds
        
        return f"{horizontal_distance:.2f}m"


def main():
    """滑雪滞空检测器"""
    detector = AirtimeDetector()
    
    # 加载数据
    sensor_data = detector.load_sensor_data('720.txt')
    
    if not sensor_data:
        print("无法加载数据文件")
        return
    
    # 检测滞空事件
    events = detector.detect_airtime_events(sensor_data)
    
    # 过滤高置信度事件
    high_confidence_events = [event for event in events if event.confidence >= 0.85]
    
    if not high_confidence_events:
        print("未检测到高置信度滞空事件")
        return
    
    # 输出表格
    print(f"{'序号':<4} {'开始时间戳':<19} {'结束时间戳':<19} {'持续时间(s)':<10} {'滞空高度(m)':<10} {'水平距离':<10} {'置信度':<6} {'转体角度(°)':<10} {'转体速度(圈/s)':<12} {'转动方向':<10}")
    print("-" * 120)
    
    for i, event in enumerate(high_confidence_events, 1):
        # 获取精确时间戳
        if hasattr(event, 'start_data_index') and hasattr(event, 'end_data_index'):
            start_data = sensor_data[event.start_data_index]
            end_data = sensor_data[event.end_data_index]
            
            start_timestamp = f"{start_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{start_data.milliseconds:03d}"
            end_timestamp = f"{end_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')}.{end_data.milliseconds:03d}"
            
            # 计算转体信息
            airtime_segment = sensor_data[event.start_data_index:event.end_data_index+1]
            rotation_info = detector._calculate_detailed_rotation(airtime_segment, event.duration_seconds)
            
            rotation_angle = rotation_info['total_angle']
            rotation_speed = rotation_info['avg_speed']
            rotation_direction = rotation_info['direction']
            first_half_speed = rotation_info['first_half_speed']
            second_half_speed = rotation_info['second_half_speed']
            has_split_analysis = rotation_info['has_split_analysis']
            
            # 计算水平距离
            horizontal_distance = detector._calculate_horizontal_distance(airtime_segment, event.duration_seconds)
        else:
            start_timestamp = event.start_time.strftime('%Y-%m-%d %H:%M:%S.000')
            end_timestamp = event.end_time.strftime('%Y-%m-%d %H:%M:%S.000')
            rotation_angle = 0.0
            rotation_speed = 0.0
            rotation_direction = 'none'
            horizontal_distance = "null"
            has_split_analysis = False
            first_half_speed = 0.0
            second_half_speed = 0.0
        
        # 构建转体速度显示字符串
        if has_split_analysis:
            speed_display = f"{rotation_speed:.2f}(前:{first_half_speed:.2f}|后:{second_half_speed:.2f})"
        else:
            speed_display = f"{rotation_speed:.2f}"
        
        # 缩短时间戳显示（只显示时分秒.毫秒）
        start_time_short = start_timestamp.split(' ')[1]  # 只取时间部分
        end_time_short = end_timestamp.split(' ')[1]      # 只取时间部分
        
        print(f"{i:<4} {start_time_short:<19} {end_time_short:<19} "
              f"{event.duration_seconds:<10.3f} {event.max_height_change:<10.3f} "
              f"{horizontal_distance:<10} {event.confidence:<6.3f} {rotation_angle:<10.1f} "
              f"{speed_display:<12} {rotation_direction:<10}")


if __name__ == "__main__":
    main()
