#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滑雪滞空数据可视化工具
生成图表和报告来展示滞空分析结果
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
import os

from skiing_airtime_calculator import SkiingAirtimeCalculator, SensorData, AirtimeEvent

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class AirtimeVisualizer:
    """滑雪滞空数据可视化器"""
    
    def __init__(self, output_dir: str = 'output'):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def plot_sensor_data_overview(self, sensor_data: List[SensorData],
                                  airtime_events: List[AirtimeEvent] = None,
                                  save_path: str = None) -> None:
        """绘制传感器数据总览图"""
        if not sensor_data:
            return

        # 准备数据
        timestamps = [data.timestamp for data in sensor_data]
        # Z轴加速度 (需要除以100获取真实值)
        z_accelerations = [data.az / 100.0 for data in sensor_data]
        # Z轴角加速度 (角速度)
        z_angular_velocities = [data.gz for data in sensor_data]
        # 海拔高度 (存储在humidity字段中)
        altitudes = [data.humidity for data in sensor_data]

        # 创建子图
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle('滑雪传感器数据分析 - Z轴数据', fontsize=16, fontweight='bold')

        # 1. Z轴加速度图
        axes[0].plot(timestamps, z_accelerations, 'b-', linewidth=1, alpha=0.7)
        axes[0].axhline(y=10, color='r', linestyle='--', alpha=0.5, label='标准重力 (10 m/s²)')
        axes[0].axhline(y=-10, color='r', linestyle='--', alpha=0.5)
        axes[0].set_ylabel('Z轴加速度 (m/s²)')
        axes[0].set_title('Z轴加速度变化 (az)')
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()

        # 标记滞空事件
        if airtime_events:
            for event in airtime_events:
                axes[0].axvspan(event.start_time, event.end_time,
                              alpha=0.2, color='red', label='滞空事件' if event == airtime_events[0] else "")

        # 2. Z轴角速度图
        axes[1].plot(timestamps, z_angular_velocities, 'g-', linewidth=1, alpha=0.7)
        axes[1].set_ylabel('Z轴角速度 (度/秒)')
        axes[1].set_title('Z轴角速度变化 (gz) - 转体旋转')
        axes[1].grid(True, alpha=0.3)

        # 标记滞空事件
        if airtime_events:
            for event in airtime_events:
                axes[1].axvspan(event.start_time, event.end_time,
                              alpha=0.2, color='red')

        # 3. 海拔高度图
        axes[2].plot(timestamps, altitudes, 'm-', linewidth=1, alpha=0.7)
        axes[2].set_ylabel('海拔高度')
        axes[2].set_xlabel('时间')
        axes[2].set_title('海拔高度变化 (h)')
        axes[2].grid(True, alpha=0.3)

        # 标记滞空事件
        if airtime_events:
            for event in airtime_events:
                axes[2].axvspan(event.start_time, event.end_time,
                              alpha=0.2, color='red')

        # 格式化时间轴
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.SecondLocator(interval=30))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图片
        if save_path is None:
            save_path = os.path.join(self.output_dir, 'z_axis_sensor_data.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"Z轴传感器数据图已保存到: {save_path}")
    
    def plot_airtime_events(self, airtime_events: List[AirtimeEvent], 
                           save_path: str = None) -> None:
        """绘制滞空事件分析图"""
        if not airtime_events:
            print("没有滞空事件可以绘制")
            return
        
        # 准备数据
        durations = [event.duration_seconds for event in airtime_events]
        confidences = [event.confidence for event in airtime_events]
        max_accelerations = [event.max_acceleration for event in airtime_events]
        height_changes = [event.max_height_change for event in airtime_events]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('滑雪滞空事件分析', fontsize=16, fontweight='bold')
        
        # 1. 滞空时间分布直方图
        axes[0, 0].hist(durations, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_xlabel('滞空时间 (秒)')
        axes[0, 0].set_ylabel('事件数量')
        axes[0, 0].set_title('滞空时间分布')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 置信度分布
        axes[0, 1].hist(confidences, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_xlabel('置信度')
        axes[0, 1].set_ylabel('事件数量')
        axes[0, 1].set_title('置信度分布')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 滞空时间 vs 置信度散点图
        scatter = axes[1, 0].scatter(durations, confidences, 
                                   c=max_accelerations, cmap='viridis', 
                                   alpha=0.7, s=50)
        axes[1, 0].set_xlabel('滞空时间 (秒)')
        axes[1, 0].set_ylabel('置信度')
        axes[1, 0].set_title('滞空时间 vs 置信度')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=axes[1, 0])
        cbar.set_label('最大加速度 (mg)')
        
        # 4. 高度变化 vs 滞空时间
        axes[1, 1].scatter(height_changes, durations, alpha=0.7, color='orange', s=50)
        axes[1, 1].set_xlabel('最大高度变化')
        axes[1, 1].set_ylabel('滞空时间 (秒)')
        axes[1, 1].set_title('高度变化 vs 滞空时间')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = os.path.join(self.output_dir, 'airtime_events_analysis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"滞空事件分析图已保存到: {save_path}")
    
    def plot_airtime_timeline(self, airtime_events: List[AirtimeEvent], 
                             save_path: str = None) -> None:
        """绘制滞空事件时间线"""
        if not airtime_events:
            return
        
        # 按时间排序
        sorted_events = sorted(airtime_events, key=lambda x: x.start_time)
        
        fig, ax = plt.subplots(figsize=(15, 8))
        
        # 绘制时间线
        for i, event in enumerate(sorted_events):
            # 计算颜色（基于置信度）
            color_intensity = event.confidence
            color = plt.cm.RdYlGn(color_intensity)
            
            # 绘制滞空事件条件
            ax.barh(i, event.duration_seconds, 
                   left=mdates.date2num(event.start_time),
                   height=0.8, color=color, alpha=0.8,
                   edgecolor='black', linewidth=0.5)
            
            # 添加文本标签
            mid_time = event.start_time + timedelta(seconds=event.duration_seconds/2)
            ax.text(mdates.date2num(mid_time), i, 
                   f'{event.duration_seconds:.1f}s\n({event.confidence:.2f})',
                   ha='center', va='center', fontsize=8, fontweight='bold')
        
        # 设置坐标轴
        ax.set_xlabel('时间')
        ax.set_ylabel('滞空事件序号')
        ax.set_title('滞空事件时间线')
        ax.grid(True, alpha=0.3)
        
        # 格式化时间轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        ax.xaxis.set_major_locator(mdates.SecondLocator(interval=30))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        # 设置Y轴
        ax.set_ylim(-0.5, len(sorted_events) - 0.5)
        ax.set_yticks(range(len(sorted_events)))
        ax.set_yticklabels([f'事件 {i+1}' for i in range(len(sorted_events))])
        
        # 添加颜色条说明
        sm = plt.cm.ScalarMappable(cmap=plt.cm.RdYlGn, 
                                  norm=plt.Normalize(vmin=0, vmax=1))
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax)
        cbar.set_label('置信度')
        
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = os.path.join(self.output_dir, 'airtime_timeline.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"滞空事件时间线已保存到: {save_path}")
    
    def generate_report(self, sensor_data: List[SensorData], 
                       airtime_events: List[AirtimeEvent],
                       stats: Dict, save_path: str = None) -> None:
        """生成详细的分析报告"""
        if save_path is None:
            save_path = os.path.join(self.output_dir, 'airtime_report.txt')
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("滑雪滞空时间分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本信息
            f.write("基本信息:\n")
            f.write("-" * 30 + "\n")
            if sensor_data:
                f.write(f"数据时间范围: {sensor_data[0].timestamp} 到 {sensor_data[-1].timestamp}\n")
                total_duration = (sensor_data[-1].timestamp - sensor_data[0].timestamp).total_seconds()
                f.write(f"总记录时长: {total_duration:.1f} 秒\n")
                f.write(f"数据点数量: {len(sensor_data)} 个\n")
                f.write(f"平均采样率: {len(sensor_data)/total_duration:.1f} Hz\n\n")
            
            # 滞空统计
            f.write("滞空统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"检测到滞空事件: {stats['total_events']} 次\n")
            f.write(f"总滞空时间: {stats['total_airtime_seconds']:.2f} 秒\n")
            f.write(f"平均滞空时间: {stats['average_airtime_seconds']:.2f} 秒\n")
            f.write(f"最长滞空时间: {stats['max_airtime_seconds']:.2f} 秒\n")
            f.write(f"最短滞空时间: {stats['min_airtime_seconds']:.2f} 秒\n")
            f.write(f"高置信度事件: {stats['high_confidence_events']} 次\n")
            
            if stats['total_events'] > 0:
                f.write(f"滞空频率: {stats['total_events']/total_duration*60:.1f} 次/分钟\n")
                f.write(f"滞空占比: {stats['total_airtime_seconds']/total_duration*100:.1f}%\n\n")
            
            # 详细事件列表
            if airtime_events:
                f.write("详细滞空事件:\n")
                f.write("-" * 30 + "\n")
                f.write(f"{'序号':<4} {'开始时间':<12} {'结束时间':<12} {'持续时间(s)':<12} {'置信度':<8} {'最大加速度(mg)':<15} {'高度变化':<10}\n")
                f.write("-" * 80 + "\n")
                
                for i, event in enumerate(airtime_events, 1):
                    f.write(f"{i:<4} {event.start_time.strftime('%H:%M:%S'):<12} "
                           f"{event.end_time.strftime('%H:%M:%S'):<12} "
                           f"{event.duration_seconds:<12.2f} {event.confidence:<8.2f} "
                           f"{event.max_acceleration:<15.1f} {event.max_height_change:<10.1f}\n")
            
            f.write("\n" + "=" * 60 + "\n")
            f.write("报告生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n")
        
        print(f"详细报告已保存到: {save_path}")


def main():
    """主函数 - 演示可视化功能"""
    
    # 创建计算器和可视化器
    calculator = SkiingAirtimeCalculator()
    visualizer = AirtimeVisualizer()
    
    # 加载数据
    print("正在加载传感器数据...")
    sensor_data = calculator.load_sensor_data('0.txt')
    
    if not sensor_data:
        print("没有加载到有效的传感器数据")
        return
    
    # 检测滞空事件
    print("正在检测滞空事件...")
    airtime_events = calculator.detect_airtime_events(sensor_data)
    
    # 分析统计数据
    stats = calculator.analyze_airtime_statistics(airtime_events)
    
    # 生成可视化图表
    print("正在生成可视化图表...")
    
    # 1. 传感器数据总览
    visualizer.plot_sensor_data_overview(sensor_data, airtime_events)
    
    # 2. 滞空事件分析
    visualizer.plot_airtime_events(airtime_events)
    
    # 3. 滞空事件时间线
    visualizer.plot_airtime_timeline(airtime_events)
    
    # 4. 生成详细报告
    visualizer.generate_report(sensor_data, airtime_events, stats)
    
    print("\n所有可视化图表和报告已生成完成！")
    print(f"输出目录: {visualizer.output_dir}")


if __name__ == "__main__":
    main()
